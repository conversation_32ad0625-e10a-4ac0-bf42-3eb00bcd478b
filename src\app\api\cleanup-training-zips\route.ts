import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '../../../lib/supabase'
import { cleanupTrainingZip } from '../../../utils/loraTraining'

/**
 * Cleanup training ZIP files for completed or failed trainings
 * This endpoint can be called by webhooks or scheduled tasks
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🧹 POST /api/cleanup-training-zips called')
    
    const { trainingId, forceCleanup = false } = await request.json()
    
    if (!trainingId) {
      return NextResponse.json({
        error: 'Training ID is required'
      }, { status: 400 })
    }
    
    if (!supabase) {
      return NextResponse.json({
        error: 'Database not available'
      }, { status: 500 })
    }
    
    // Look for training job in database
    const { data: trainingJob, error } = await supabase
      .from('training_jobs')
      .select('*')
      .eq('replicate_training_id', trainingId)
      .single()
    
    if (error || !trainingJob) {
      console.log(`⚠️ No training job found for ID: ${trainingId}`)
      return NextResponse.json({
        error: 'Training job not found',
        trainingId
      }, { status: 404 })
    }
    
    console.log(`📋 Found training job: ${trainingJob.trainable_type} - ${trainingJob.status}`)
    
    // Only cleanup if training is complete/failed or force cleanup is requested
    const shouldCleanup = forceCleanup || 
      ['completed', 'failed'].includes(trainingJob.status) ||
      (trainingJob.status === 'training' && trainingJob.progress > 10) // Training has definitely started
    
    if (!shouldCleanup) {
      return NextResponse.json({
        message: 'Training not ready for cleanup',
        trainingId,
        status: trainingJob.status,
        progress: trainingJob.progress
      })
    }
    
    // Get the ZIP URL based on trainable type
    let zipUrl = null
    
    if (trainingJob.trainable_type === 'style') {
      // Get ZIP URL from styles table
      const { data: style } = await supabase
        .from('styles')
        .select('image_base64') // Using image_base64 as temporary storage for ZIP URL
        .eq('id', trainingJob.trainable_id)
        .single()
      
      zipUrl = style?.image_base64 // This might need adjustment based on actual schema
    } else if (trainingJob.trainable_type === 'persona') {
      // Get ZIP URL from personas table  
      const { data: persona } = await supabase
        .from('personas')
        .select('image_base64') // Using image_base64 as temporary storage for ZIP URL
        .eq('id', trainingJob.trainable_id)
        .single()
      
      zipUrl = persona?.image_base64 // This might need adjustment based on actual schema
    }
    
    if (!zipUrl || !zipUrl.includes('.zip')) {
      console.log(`⚠️ No ZIP URL found for training ${trainingId}`)
      return NextResponse.json({
        message: 'No ZIP file to cleanup',
        trainingId
      })
    }
    
    // Perform cleanup
    console.log(`🗑️ Cleaning up ZIP file: ${zipUrl}`)
    
    try {
      await cleanupTrainingZip(zipUrl)
      
      // Update training job to mark ZIP as cleaned
      await supabase
        .from('training_jobs')
        .update({
          updated_at: new Date().toISOString(),
          webhook_data: {
            ...trainingJob.webhook_data,
            zip_cleaned_at: new Date().toISOString()
          }
        })
        .eq('id', trainingJob.id)
      
      console.log(`✅ Successfully cleaned up ZIP for training ${trainingId}`)
      
      return NextResponse.json({
        success: true,
        message: 'ZIP file cleaned up successfully',
        trainingId,
        zipUrl,
        cleanedAt: new Date().toISOString()
      })
      
    } catch (cleanupError) {
      console.error(`❌ Failed to cleanup ZIP for training ${trainingId}:`, cleanupError)
      
      return NextResponse.json({
        error: 'Failed to cleanup ZIP file',
        trainingId,
        details: cleanupError instanceof Error ? cleanupError.message : 'Unknown error'
      }, { status: 500 })
    }
    
  } catch (error) {
    console.error('❌ Cleanup endpoint error:', error)
    
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

/**
 * GET endpoint to check cleanup status
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const trainingId = searchParams.get('trainingId')
    
    if (!trainingId) {
      return NextResponse.json({
        error: 'Training ID parameter is required'
      }, { status: 400 })
    }
    
    if (!supabase) {
      return NextResponse.json({
        error: 'Database not available'
      }, { status: 500 })
    }
    
    // Check training job status
    const { data: trainingJob, error } = await supabase
      .from('training_jobs')
      .select('*')
      .eq('replicate_training_id', trainingId)
      .single()
    
    if (error || !trainingJob) {
      return NextResponse.json({
        error: 'Training job not found',
        trainingId
      }, { status: 404 })
    }
    
    const zipCleanedAt = trainingJob.webhook_data?.zip_cleaned_at
    const needsCleanup = !zipCleanedAt && ['completed', 'failed'].includes(trainingJob.status)
    
    return NextResponse.json({
      trainingId,
      status: trainingJob.status,
      progress: trainingJob.progress,
      zipCleanedAt,
      needsCleanup,
      trainableType: trainingJob.trainable_type,
      trainableId: trainingJob.trainable_id
    })
    
  } catch (error) {
    console.error('❌ Cleanup status check error:', error)
    
    return NextResponse.json({
      error: 'Failed to check cleanup status',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
} 