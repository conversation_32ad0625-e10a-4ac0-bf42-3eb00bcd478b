import { NextRequest, NextResponse } from 'next/server';
import { put } from '@vercel/blob';

export async function GET() {
  try {
    console.log('Testing Vercel Blob storage...');
    console.log('BLOB_READ_WRITE_TOKEN exists:', !!process.env.BLOB_READ_WRITE_TOKEN);
    console.log('Token prefix:', process.env.BLOB_READ_WRITE_TOKEN ? 
      process.env.BLOB_READ_WRITE_TOKEN.substring(0, 15) + '...' : 'MISSING');
    
    if (!process.env.BLOB_READ_WRITE_TOKEN) {
      return NextResponse.json({
        success: false,
        error: 'BLOB_READ_WRITE_TOKEN not found in environment variables',
        debug: {
          hasToken: !!process.env.BLOB_READ_WRITE_TOKEN,
          nodeEnv: process.env.NODE_ENV,
          allBlobVars: Object.keys(process.env).filter(key => key.includes('BLOB'))
        }
      }, { status: 500 });
    }
    
    // Test uploading a small text file
    const testContent = 'Hello from Thumbnex API test!';
    const blob = await put('test-file-api.txt', testContent, {
      access: 'public',
    });
    
    return NextResponse.json({
      success: true,
      message: '✅ Blob upload successful!',
      blob: {
        url: blob.url
      },
      debug: {
        hasToken: !!process.env.BLOB_READ_WRITE_TOKEN,
        tokenPrefix: process.env.BLOB_READ_WRITE_TOKEN?.substring(0, 15) + '...'
      }
    });
    
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      debug: {
        hasToken: !!process.env.BLOB_READ_WRITE_TOKEN,
        tokenPrefix: process.env.BLOB_READ_WRITE_TOKEN ? 
          process.env.BLOB_READ_WRITE_TOKEN.substring(0, 15) + '...' : 'MISSING'
      }
    }, { status: 500 });
  }
} 