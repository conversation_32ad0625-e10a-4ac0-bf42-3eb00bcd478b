import { Persona } from '../types/persona'
import { supabase, PersonaRow, isSupabaseConfigured } from '../lib/supabase'
import { put, list, del } from '@vercel/blob'

// In-memory storage fallback for local development
let personasCache: Persona[] = []

// Blob storage key for personas
const PERSONAS_BLOB_KEY = 'personas-storage.json'

// Convert database row to Persona object
function rowToPersona(row: PersonaRow): Persona {
  return {
    id: row.id,
    name: row.name,
    description: row.description,
    imageUrl: row.image_url,
    imageBase64: row.image_base64,
    createdAt: new Date(row.created_at),
    updatedAt: new Date(row.updated_at),
    usageCount: row.usage_count,
    category: row.category as Persona['category'],
    isDefault: row.is_default,
    lastUsed: row.last_used ? new Date(row.last_used) : new Date(),
    loraTraining: row.lora_training
  }
}

// Convert Persona object to database row
function personaToRow(persona: Persona): Partial<PersonaRow> {
  return {
    id: persona.id,
    name: persona.name,
    description: persona.description,
    image_url: persona.imageUrl,
    image_base64: persona.imageBase64,
    created_at: persona.createdAt.toISOString(),
    updated_at: persona.updatedAt.toISOString(),
    usage_count: persona.usageCount,
    category: persona.category,
    is_default: persona.isDefault,
    last_used: persona.lastUsed ? persona.lastUsed.toISOString() : new Date().toISOString(),
    lora_training: persona.loraTraining
  }
}

// Load personas from database or fallback to blob/memory
export async function loadPersonas(): Promise<Persona[]> {
  try {
    // Try to load from Supabase if configured
    if (isSupabaseConfigured() && supabase) {
      console.log('🗄️ Loading personas from Supabase database...')
      const { data, error } = await supabase
        .from('personas')
        .select('*')
        .order('created_at', { ascending: false })
      if (error) {
        console.error('❌ Supabase error, falling back to blob storage:', error)
        return loadPersonasFromBlob()
      }
      if (data && data.length > 0) {
        const personas = data.map(rowToPersona)
        console.log(`✅ Loaded ${personas.length} personas from database`)
        return personas
      } else {
        // No personas in database, return empty array
        return []
      }
    }
    // Fallback to blob storage when Supabase not configured
    return loadPersonasFromBlob()
  } catch (error) {
    console.error('❌ Error loading personas from database:', error)
    return loadPersonasFromBlob()
  }
}

// Load personas from Vercel Blob storage
async function loadPersonasFromBlob(): Promise<Persona[]> {
  try {
    console.log('☁️ Loading personas from Vercel Blob storage...')
    
    // List all blobs to find the personas storage file
    const { blobs } = await list()
    const personasBlob = blobs.find(blob => blob.pathname === PERSONAS_BLOB_KEY)
    
    if (!personasBlob) {
      console.log('📄 No personas storage file found, starting with empty list')
      return []
    }
    
    // Fetch the personas data
    const response = await fetch(personasBlob.url)
    const personasData = await response.json()
    
    // Convert dates back to Date objects
    const personas = personasData.map((persona: any) => ({
      ...persona,
      createdAt: new Date(persona.createdAt),
      updatedAt: new Date(persona.updatedAt),
      lastUsed: persona.lastUsed ? new Date(persona.lastUsed) : new Date(),
      loraTraining: persona.loraTraining ? {
        ...persona.loraTraining,
        createdAt: persona.loraTraining.createdAt ? new Date(persona.loraTraining.createdAt) : undefined,
        completedAt: persona.loraTraining.completedAt ? new Date(persona.loraTraining.completedAt) : undefined
      } : undefined
    }))
    
    console.log(`✅ Loaded ${personas.length} personas from Vercel Blob storage`)
    return personas
    
  } catch (error) {
    console.error('❌ Error loading personas from Vercel Blob:', error)
    return loadPersonasFromMemory()
  }
}

// Save personas to Vercel Blob storage
async function savePersonasToBlob(personas: Persona[]): Promise<void> {
  try {
    console.log(`☁️ Saving ${personas.length} personas to Vercel Blob storage...`)
    
    // Convert to JSON string
    const personasJson = JSON.stringify(personas, null, 2)
    
    // Upload to Vercel Blob
    await put(PERSONAS_BLOB_KEY, personasJson, {
      access: 'public',
      contentType: 'application/json'
    })
    
    console.log(`✅ Successfully saved ${personas.length} personas to Vercel Blob storage`)
    
  } catch (error) {
    console.error('❌ Error saving personas to Vercel Blob:', error)
    throw error
  }
}

// Fallback in-memory storage
function loadPersonasFromMemory(): Persona[] {
  try {
    console.log('💾 Using in-memory storage (local development mode)')
    return personasCache
  } catch (error) {
    console.error('❌ Error loading personas from memory:', error)
    return []
  }
}

// Save personas to database or fallback to blob/memory
export async function savePersonas(personas: Persona[]): Promise<void> {
  try {
    // Try to save to Supabase if configured
    if (isSupabaseConfigured() && supabase) {
      console.log(`🗄️ Saving ${personas.length} personas to Supabase database...`)
      
      // Convert personas to database rows
      const rows = personas.map(personaToRow)
      
      // Upsert all personas (insert new, update existing)
      const { error } = await supabase
        .from('personas')
        .upsert(rows, { onConflict: 'id' })
      
      if (error) {
        console.error('❌ Supabase save error:', error)
        console.error('🔍 Database error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        })
        
        // Try blob storage fallback
        try {
          console.log('🔄 Attempting blob storage fallback...')
          await savePersonasToBlob(personas)
          console.log('✅ Blob storage fallback successful')
        } catch (blobError) {
          console.error('❌ Blob storage fallback also failed:', blobError)
          throw new Error(`Database save failed: ${error.message}. Blob storage fallback failed: ${blobError instanceof Error ? blobError.message : 'Unknown error'}`)
        }
        return
      }
      
      console.log(`✅ Successfully saved ${personas.length} personas to database`)
      return
    }
    
    // Fallback to blob storage when Supabase not configured
    console.log('🔄 Supabase not configured, using blob storage...')
    await savePersonasToBlob(personas)
    
  } catch (error) {
    console.error('❌ Critical error in savePersonas:', error)
    
    // Last resort: try memory storage
    try {
      console.log('🆘 Last resort: attempting memory storage...')
      savePersonasToMemory(personas)
      console.log('✅ Last resort memory storage successful')
    } catch (memoryError) {
      console.error('❌ All storage methods failed:', memoryError)
      throw new Error(`All persona storage methods failed. Database error: ${error instanceof Error ? error.message : 'Unknown'}. Memory error: ${memoryError instanceof Error ? memoryError.message : 'Unknown'}`)
    }
  }
}

// Fallback in-memory save
function savePersonasToMemory(personas: Persona[]): void {
  try {
    console.log('💾 Saving to in-memory storage (local development mode)')
    console.log(`📊 Attempting to save ${personas.length} personas`)
    
    // Validate input
    if (!Array.isArray(personas)) {
      throw new Error('Personas must be an array')
    }
    
    // Create a deep copy to avoid reference issues
    personasCache = personas.map((persona, index) => {
      try {
        if (!persona || !persona.id) {
          throw new Error(`Invalid persona at index ${index}: missing ID`)
        }
        
        return {
          ...persona,
          createdAt: new Date(persona.createdAt),
          updatedAt: new Date(persona.updatedAt),
          lastUsed: persona.lastUsed ? new Date(persona.lastUsed) : new Date(),
          loraTraining: persona.loraTraining ? {
            ...persona.loraTraining,
            createdAt: persona.loraTraining.createdAt ? new Date(persona.loraTraining.createdAt) : undefined,
            completedAt: persona.loraTraining.completedAt ? new Date(persona.loraTraining.completedAt) : undefined,
            trainingImages: persona.loraTraining.trainingImages ? [...persona.loraTraining.trainingImages] : []
          } : undefined
        }
      } catch (personaError) {
        console.error(`❌ Error processing persona at index ${index}:`, personaError)
        throw personaError
      }
    })
    
    console.log(`💾 Successfully saved ${personas.length} personas to cache`)
    console.log(`📝 Cache now contains IDs:`, personasCache.map(p => p.id))
  } catch (error) {
    console.error('❌ Critical error saving personas to memory:', error)
    console.error('🔍 Memory save error details:', {
      errorType: error?.constructor?.name,
      errorMessage: error instanceof Error ? error.message : 'Unknown error',
      personasInput: Array.isArray(personas) ? personas.length : 'Not an array',
      cacheState: personasCache.length
    })
    throw new Error(`Failed to save personas to memory: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Utility function to find a specific persona
export async function findPersonaById(id: string): Promise<Persona | null> {
  try {
    console.log(`🔍 Finding persona by ID: ${id}`)
    
    const personas = await loadPersonas()
    console.log(`📋 Loaded ${personas.length} personas, searching for ID: ${id}`)
    
    const persona = personas.find(p => p.id === id)
    
    if (persona) {
      console.log(`✅ Found persona: ${persona.name}`)
    } else {
      console.log(`❌ Persona not found with ID: ${id}`)
      console.log(`📝 Available persona IDs:`, personas.map(p => p.id))
    }
    
    return persona || null
  } catch (error) {
    console.error('❌ Error finding persona:', error)
    return null
  }
}

// Utility function to save a single persona
export async function savePersona(persona: Persona): Promise<void> {
  try {
    console.log(`💾 Saving persona: ${persona.name} (${persona.id})`)
    
    const personas = await loadPersonas()
    const index = personas.findIndex(p => p.id === persona.id)
    
    if (index >= 0) {
      console.log(`📝 Updating existing persona at index ${index}`)
      personas[index] = persona
    } else {
      console.log(`➕ Adding new persona to collection`)
      personas.push(persona)
    }
    
    await savePersonas(personas)
    console.log(`✅ Successfully saved persona: ${persona.name}`)
  } catch (error) {
    console.error('❌ Error saving persona:', error)
    throw new Error('Failed to save persona')
  }
}

// Create a persona from completed training data
export async function createPersonaFromTraining(params: {
  personaName: string
  triggerWord: string
  trainingId: string
  modelUrl: string
  zipUrl: string
  thumbnailImage?: string // Optional: first training image as thumbnail
}): Promise<Persona> {
  try {
    console.log(`🎯 Creating persona from completed training: ${params.personaName}`)
    
    const now = new Date()
    const personaId = `persona-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`
    
    // Create a placeholder image URL if no thumbnail provided
    const imageUrl = params.thumbnailImage || '/placeholder-thumbnail.svg'
    const imageBase64 = params.thumbnailImage || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjNjM2NjZhIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iI2ZmZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkxvUkE8L3RleHQ+Cjwvc3ZnPg=='
    
    const persona: Persona = {
      id: personaId,
      name: params.personaName,
      description: `LoRA-trained persona with trigger word: ${params.triggerWord}`,
      imageUrl: imageUrl,
      imageBase64: imageBase64,
      createdAt: now,
      updatedAt: now,
      usageCount: 0,
      category: 'other',
      isDefault: false,
      lastUsed: now,
      loraTraining: {
        status: 'completed',
        trainingId: params.trainingId,
        modelUrl: params.modelUrl,
        triggerWord: params.triggerWord,
        trainingProgress: 100,
        trainingImages: [], // We don't have the original images in this context
        trainingZipUrl: params.zipUrl,
        createdAt: now,
        completedAt: now,
        steps: 1000,
        resolution: 1024,
        loraType: 'subject'
      }
    }
    
    // Save the persona
    await savePersona(persona)
    
    console.log(`✅ Successfully created persona from training: ${persona.name}`)
    return persona
    
  } catch (error) {
    console.error('❌ Error creating persona from training:', error)
    throw new Error(`Failed to create persona from training: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
} 