'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import { Sparkles, Gamepad2, Smartphone, ChefHat, Lightbulb } from 'lucide-react'

interface PromptSectionProps {
  prompt: string
  onPromptChange: (prompt: string) => void
}

const promptSuggestions = [
  { icon: Gamepad2, label: 'Gaming Reaction', prompt: 'Gaming thumbnail with shocked expression, exciting gameplay moment' },
  { icon: Smartphone, label: 'Tech Review', prompt: 'Tech review thumbnail with product showcase, modern clean design' },
  { icon: ChefHat, label: 'Food Close-up', prompt: 'Cooking video thumbnail with delicious food close-up, mouth-watering' },
  { icon: Lightbulb, label: 'Tutorial Style', prompt: 'Tutorial thumbnail with step-by-step elements, educational design' },
]

export function PromptSection({ prompt, onPromptChange }: PromptSectionProps) {
  const [aiEnhancement, setAiEnhancement] = useState(true)

  const handleSuggestionClick = (suggestionPrompt: string) => {
    onPromptChange(suggestionPrompt)
  }

  return (
    <section className="rounded-lg border border-border-primary bg-transparent p-6">
      {/* Section Header */}
      <div className="mb-4 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Sparkles className="h-5 w-5 text-white" />
          <h3 className="text-lg font-semibold text-text-primary">
            Describe Your Thumbnail
          </h3>
        </div>
        <span className="rounded-full bg-accent-primary/20 px-2 py-1 text-xs font-medium text-accent-primary">
          AI Enhanced
        </span>
      </div>

      {/* Prompt Input */}
      <div className="space-y-4">
        <textarea
          value={prompt}
          onChange={(e) => onPromptChange(e.target.value)}
          placeholder={`Describe the thumbnail you want to create...

Examples:
• Gaming thumbnail with shocked expression
• Tech review with product showcase  
• Cooking video with delicious food close-up`}
          className="min-h-[120px] w-full resize-none rounded-lg border border-border-primary bg-bg-tertiary p-3 text-text-primary placeholder-text-tertiary focus:border-border-focus focus:outline-none focus:ring-2 focus:ring-border-focus/20"
          rows={4}
        />

        {/* Smart Suggestions */}
        <div className="space-y-2">
          <p className="text-sm text-text-secondary">Quick suggestions:</p>
          <div className="flex flex-wrap gap-2">
            {promptSuggestions.map((suggestion, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => handleSuggestionClick(suggestion.prompt)}
                className="flex items-center space-x-2 text-xs"
              >
                <suggestion.icon className="h-3 w-3" />
                <span>{suggestion.label}</span>
              </Button>
            ))}
          </div>
        </div>

        {/* AI Enhancement Toggle */}
        <div className="flex items-center justify-between rounded-lg bg-bg-tertiary p-3">
          <div className="flex items-center space-x-3">
            <div className="flex h-6 w-6 items-center justify-center rounded bg-accent-primary/20">
              <Sparkles className="h-3 w-3 text-white" />
            </div>
            <div>
              <p className="text-sm font-medium text-text-primary">AI Prompt Enhancement</p>
              <p className="text-xs text-text-secondary">
                Automatically improve your prompt for better results
              </p>
            </div>
          </div>
          <button
            onClick={() => setAiEnhancement(!aiEnhancement)}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              aiEnhancement ? 'bg-accent-primary' : 'bg-border-primary'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                aiEnhancement ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>
      </div>
    </section>
  )
} 