/**
 * Memory Monitoring Utility
 * Tracks browser memory usage and provides cleanup recommendations
 */

interface MemoryInfo {
  usedJSHeapSize: number
  totalJSHeapSize: number
  jsHeapSizeLimit: number
}

interface MemoryStats {
  used: string
  total: string
  limit: string
  usagePercentage: number
  isHighUsage: boolean
  recommendation: string
}

class MemoryMonitor {
  private readonly HIGH_USAGE_THRESHOLD = 0.8 // 80%
  private readonly CRITICAL_USAGE_THRESHOLD = 0.9 // 90%

  /**
   * Get current memory usage statistics
   */
  getMemoryStats(): MemoryStats | null {
    // Check if performance.memory is available (Chrome/Edge)
    if (!('memory' in performance)) {
      console.warn('⚠️ Memory monitoring not available in this browser')
      return null
    }

    const memory = (performance as any).memory as MemoryInfo
    const usagePercentage = memory.usedJSHeapSize / memory.jsHeapSizeLimit

    return {
      used: this.formatBytes(memory.usedJSHeapSize),
      total: this.formatBytes(memory.totalJSHeapSize),
      limit: this.formatBytes(memory.jsHeapSizeLimit),
      usagePercentage: Math.round(usagePercentage * 100) / 100,
      isHighUsage: usagePercentage > this.HIGH_USAGE_THRESHOLD,
      recommendation: this.getRecommendation(usagePercentage)
    }
  }

  /**
   * Log memory usage to console
   */
  logMemoryUsage(context?: string): void {
    const stats = this.getMemoryStats()
    if (!stats) return

    const prefix = context ? `[${context}]` : ''
    const emoji = stats.isHighUsage ? '🚨' : '📊'
    
    console.log(`${emoji} ${prefix} Memory Usage: ${stats.used}/${stats.limit} (${(stats.usagePercentage * 100).toFixed(1)}%)`)
    
    if (stats.isHighUsage) {
      console.warn(`⚠️ ${stats.recommendation}`)
    }
  }

  /**
   * Check if memory cleanup is recommended
   */
  shouldCleanup(): boolean {
    const stats = this.getMemoryStats()
    return stats ? stats.usagePercentage > this.HIGH_USAGE_THRESHOLD : false
  }

  /**
   * Check if memory usage is critical
   */
  isCriticalUsage(): boolean {
    const stats = this.getMemoryStats()
    return stats ? stats.usagePercentage > this.CRITICAL_USAGE_THRESHOLD : false
  }

  /**
   * Force garbage collection if available (Chrome DevTools)
   */
  forceGarbageCollection(): void {
    if ('gc' in window) {
      (window as any).gc()
      console.log('🧹 Forced garbage collection')
    } else {
      console.log('ℹ️ Garbage collection not available (enable in Chrome DevTools)')
    }
  }

  /**
   * Start periodic memory monitoring
   */
  startMonitoring(intervalMs: number = 30000): () => void {
    console.log('🔍 Started memory monitoring')
    
    const interval = setInterval(() => {
      this.logMemoryUsage('Monitor')
      
      if (this.isCriticalUsage()) {
        console.error('🚨 CRITICAL: Memory usage is very high! Consider refreshing the page.')
      }
    }, intervalMs)

    return () => {
      clearInterval(interval)
      console.log('⏹️ Stopped memory monitoring')
    }
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  private getRecommendation(usagePercentage: number): string {
    if (usagePercentage > this.CRITICAL_USAGE_THRESHOLD) {
      return 'CRITICAL: Consider refreshing the page to free memory'
    } else if (usagePercentage > this.HIGH_USAGE_THRESHOLD) {
      return 'HIGH: Consider clearing image cache or reducing image generation frequency'
    } else {
      return 'Memory usage is normal'
    }
  }
}

// Global instance
export const memoryMonitor = new MemoryMonitor()

/**
 * React hook for memory monitoring
 */
export function useMemoryMonitor() {
  return {
    getStats: () => memoryMonitor.getMemoryStats(),
    logUsage: (context?: string) => memoryMonitor.logMemoryUsage(context),
    shouldCleanup: () => memoryMonitor.shouldCleanup(),
    isCritical: () => memoryMonitor.isCriticalUsage(),
    forceGC: () => memoryMonitor.forceGarbageCollection(),
    startMonitoring: (interval?: number) => memoryMonitor.startMonitoring(interval)
  }
}

/**
 * Memory-aware image loading
 */
export async function loadImageWithMemoryCheck(url: string): Promise<HTMLImageElement> {
  // Check memory before loading
  if (memoryMonitor.shouldCleanup()) {
    console.warn('⚠️ High memory usage detected before image load')
  }

  return new Promise((resolve, reject) => {
    const img = new Image()
    
    img.onload = () => {
      memoryMonitor.logMemoryUsage('Image Loaded')
      resolve(img)
    }
    
    img.onerror = () => {
      reject(new Error(`Failed to load image: ${url}`))
    }
    
    img.src = url
  })
}
