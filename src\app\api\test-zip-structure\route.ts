import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const zipUrl = searchParams.get('zipUrl')
    
    if (!zipUrl) {
      return NextResponse.json({
        error: 'zipUrl parameter required'
      }, { status: 400 })
    }
    
    console.log('🔍 Testing ZIP structure for URL:', zipUrl)
    
    // Verify URL points to a ZIP file
    if (!zipUrl.includes('.zip')) {
      return NextResponse.json({
        error: 'URL does not appear to be a ZIP file',
        zipUrl
      }, { status: 400 })
    }
    
    // Fetch the ZIP file
    console.log('📥 Fetching ZIP file from Vercel Blob...')
    const response = await fetch(zipUrl)
    
    if (!response.ok) {
      return NextResponse.json({
        error: 'Failed to fetch ZIP file',
        status: response.status,
        statusText: response.statusText
      }, { status: 500 })
    }
    
    const contentType = response.headers.get('content-type')
    const contentLength = response.headers.get('content-length')
    
    console.log('📋 ZIP file details:')
    console.log('  - Content-Type:', contentType)
    console.log('  - Content-Length:', contentLength)
    console.log('  - URL ends with .zip:', zipUrl.endsWith('.zip'))
    
    // Verify it's actually a ZIP file
    const arrayBuffer = await response.arrayBuffer()
    const uint8Array = new Uint8Array(arrayBuffer)
    
    // Check ZIP magic bytes (PK header)
    const isZipFile = uint8Array.length >= 4 && 
      uint8Array[0] === 0x50 && uint8Array[1] === 0x4B && 
      (uint8Array[2] === 0x03 || uint8Array[2] === 0x05 || uint8Array[2] === 0x07)
    
    return NextResponse.json({
      success: true,
      zipUrl,
      isDirectZipFile: true,
      details: {
        contentType,
        contentLength: parseInt(contentLength || '0'),
        sizeInMB: (parseInt(contentLength || '0') / 1024 / 1024).toFixed(2),
        hasZipMagicBytes: isZipFile,
        urlEndsWithZip: zipUrl.endsWith('.zip'),
        isValidZipFile: isZipFile && zipUrl.endsWith('.zip')
      },
      message: 'ZIP file verification completed'
    })
    
  } catch (error) {
    console.error('❌ ZIP structure test failed:', error)
    return NextResponse.json({
      error: 'Failed to test ZIP structure',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
} 