export interface Style {
  id: string
  name: string
  category: string
  description?: string
  imageUrl: string // Main display image URL
  imageBase64: string // Main display image base64
  tags: string[]
  createdAt: Date
  updatedAt: Date
  usageCount: number
  isDefault?: boolean
  // LoRA Training fields - Same as personas but for style training
  loraTraining?: {
    status: 'pending' | 'training' | 'completed' | 'failed'
    trainingId?: string
    modelUrl?: string
    triggerWord: string // Required unique trigger word (e.g., "STYLE123", "RETRO456")
    trainingProgress?: number
    errorMessage?: string
    trainingImages: StyleTrainingImage[] // Multiple training images (10-20 recommended)
    trainingZipUrl?: string // ZIP file URL for Replicate training
    createdAt?: Date
    completedAt?: Date
    // Fast FLUX Trainer specific parameters
    steps: number // Default 1000 steps
    learningRate?: number // Default from Replicate
    batchSize?: number // Default from Replicate
    resolution: number // Target resolution (1024 recommended)
    loraType: 'style' // For styles, always 'style' (vs 'subject' for personas)
  }
}

export interface StyleTrainingImage {
  id: string
  originalFilename: string
  processedImageBase64: string
  storageUrl?: string // Vercel Blob URL after upload
  storagePath?: string // Storage path reference
  width: number
  height: number
  fileSize: number
  uploadedAt: Date
}

// Database row interface for Supabase
export interface StyleRow {
  id: string
  name: string
  category: string
  description?: string
  image_url: string
  image_base64: string
  tags: string[]
  created_at: string
  updated_at: string
  usage_count: number
  is_default?: boolean
  lora_training?: any // JSONB field
}

// Style categories
export type StyleCategory = 
  | 'gaming'
  | 'tech' 
  | 'vlog'
  | 'educational'
  | 'fitness'
  | 'business'
  | 'creative'
  | 'minimal'
  | 'retro'
  | 'cinematic'
  | 'other'

// Training request interface
export interface StyleTrainingRequest {
  styleName: string
  category: StyleCategory
  description?: string
  triggerWord: string
  trainingImages: File[]
  tags?: string[]
} 