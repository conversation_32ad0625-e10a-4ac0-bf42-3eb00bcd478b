/**
 * LoRA Training utilities for persona training workflows
 */
import J<PERSON><PERSON><PERSON> from 'jszip'
import { TrainingImage } from '../types/persona'
import { supabase } from '../lib/supabase'
import { put, del } from '@vercel/blob'

interface TrainingResult {
  success: boolean
  trainingId?: string
  error?: string
  zipSize?: number
  blobUrl?: string // Track blob URL for cleanup
}

/**
 * Create ZIP file directly from training images (no external storage needed)
 */
export async function createTrainingZip(trainingImages: TrainingImage[], triggerWord: string): Promise<Blob> {
  console.log(`📦 Creating ZIP file directly from ${trainingImages.length} training images...`)
  
  const zip = new JSZip()
  
  // Add each training image to ZIP directly from base64 data
  for (let i = 0; i < trainingImages.length; i++) {
    const image = trainingImages[i]
    
    // Convert base64 directly to ArrayBuffer (no network calls needed)
    console.log(`📄 Processing image ${i + 1} from base64 data...`)
    const base64Data = image.processedImageBase64.split(',')[1]
    const imageData = Uint8Array.from(atob(base64Data), c => c.charCodeAt(0)).buffer
    
    console.log(`✅ Processed ${(imageData.byteLength / 1024).toFixed(1)}KB for image ${i + 1}`)
    
    // Add image to ZIP with sequential naming
    const filename = `img_${i.toString().padStart(2, '0')}.jpg`
    zip.file(filename, imageData)
    
    // Add caption file for each image (following Replicate best practices)
    const expressionText = image.expression ? ` with ${image.expression} expression` : ''
    const settingText = image.setting ? ` in ${image.setting}` : ''
    const caption = `A photo of ${triggerWord}${expressionText}${settingText}`.trim()
    zip.file(`img_${i.toString().padStart(2, '0')}.txt`, caption)
  }
  
  // Generate ZIP blob with optimal compression
  console.log('🗜️ Compressing ZIP file...')
  const zipBlob = await zip.generateAsync({ 
    type: 'blob',
    compression: 'DEFLATE',
    compressionOptions: { level: 6 }
  })
  
  console.log(`✅ ZIP created successfully: ${(zipBlob.size / 1024 / 1024).toFixed(2)}MB`)
  console.log(`📊 Compression ratio: ${((zipBlob.size / (trainingImages.length * 500 * 1024)) * 100).toFixed(1)}%`)
  
  return zipBlob
}

/**
 * Upload training ZIP to Vercel Blob (public, reliable, CDN-backed)
 */
export async function uploadTrainingZip(zipBlob: Blob, personaName: string): Promise<string> {
  try {
    console.log('📤 Uploading training ZIP to Vercel Blob...')
    console.log('📊 ZIP size:', (zipBlob.size / 1024 / 1024).toFixed(2), 'MB')
    
    // Generate unique filename
    const timestamp = Date.now()
    const randomId = Math.random().toString(36).substring(2, 8)
    const sanitizedName = personaName.toLowerCase().replace(/[^a-z0-9]/g, '-')
    const filename = `training-zips/${sanitizedName}-${timestamp}-${randomId}.zip`
    
    console.log('🔧 Uploading to Vercel Blob with filename:', filename)
    
    // Upload to Vercel Blob - this creates a public, immutable URL
    const blob = await put(filename, zipBlob, {
      access: 'public', // Ensures public access for Replicate
      contentType: 'application/zip'
    })
    
    console.log('✅ Vercel Blob upload successful')
    console.log('🌐 Public URL:', blob.url)
    console.log('📏 Original ZIP size:', (zipBlob.size / 1024 / 1024).toFixed(2), 'MB')
    
    return blob.url
    
  } catch (error) {
    console.error('❌ Vercel Blob upload error:', error)
    throw new Error(`Failed to upload to Vercel Blob: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Delete training ZIP from Vercel Blob after successful upload to Replicate
 */
export async function cleanupTrainingZip(blobUrl: string): Promise<void> {
  try {
    console.log('🧹 Cleaning up training ZIP from Vercel Blob...')
    console.log('🗑️ Deleting:', blobUrl)
    
    await del(blobUrl)
    
    console.log('✅ Training ZIP cleaned up successfully')
  } catch (error) {
    console.warn('⚠️ Failed to cleanup training ZIP (non-critical):', error)
    // Don't throw - this is cleanup, not critical to training
  }
}

/**
 * DEPRECATED: Old LoRA training function
 * This function used the wrong Replicate API endpoints (/predictions instead of /trainings)
 * and has been replaced by the direct training system in /api/train-lora-direct
 */

/**
 * Check training status
 */
export async function checkTrainingStatus(trainingId: string): Promise<{
  status: string
  progress?: number
  error?: string
  modelUrl?: string
}> {
  try {
    if (!process.env.REPLICATE_API_TOKEN) {
      throw new Error('REPLICATE_API_TOKEN not configured')
    }
    
    const response = await fetch(
      `https://api.replicate.com/v1/predictions/${trainingId}`,
      {
        headers: {
          'Authorization': `Bearer ${process.env.REPLICATE_API_TOKEN}`
        }
      }
    )
    
    if (!response.ok) {
      throw new Error(`Failed to check training status: ${response.status}`)
    }
    
    const data = await response.json()
    
    // For successful predictions, get the LoRA model URL from output
    let modelUrl = null
    if (data.status === 'succeeded' && data.output) {
      // The output should contain the LoRA model URL
      modelUrl = data.output
    }
    
    console.log('🔍 Replicate prediction response:', {
      status: data.status,
      progress: data.progress,
      hasOutput: !!data.output,
      output: data.output,
      finalModelUrl: modelUrl
    })
    
    return {
      status: data.status,
      progress: data.progress ? Math.round(data.progress * 100) : undefined,
      error: data.error,
      modelUrl: modelUrl
    }
    
  } catch (error) {
    console.error('❌ Failed to check training status:', error)
    return {
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * DEPRECATED: Complete training workflow function
 * This function was part of the old persona-based training system and is no longer used.
 * The new system handles training directly in /api/train-lora-direct without this intermediate function.
 */

/**
 * Note: Individual image uploads are no longer needed.
 * We now create ZIP files directly from base64 data and upload only the ZIP to Vercel Blob.
 * This is more efficient and reduces storage costs.
 */ 