import { Style, StyleRow } from '../types/style'
import { supabase, isSupabaseConfigured } from '../lib/supabase'

// Convert database row to Style object
function rowToStyle(row: StyleRow): Style {
  return {
    id: row.id,
    name: row.name,
    category: row.category,
    description: row.description,
    imageUrl: row.image_url,
    imageBase64: row.image_base64,
    tags: row.tags || [],
    createdAt: new Date(row.created_at),
    updatedAt: new Date(row.updated_at),
    usageCount: row.usage_count,
    isDefault: row.is_default,
    loraTraining: row.lora_training
  }
}

// Convert Style object to database row
function styleToRow(style: Style): Partial<StyleRow> {
  return {
    id: style.id,
    name: style.name,
    category: style.category,
    description: style.description,
    image_url: style.imageUrl,
    image_base64: style.imageBase64,
    tags: style.tags,
    created_at: style.createdAt.toISOString(),
    updated_at: style.updatedAt.toISOString(),
    usage_count: style.usageCount,
    is_default: style.isDefault,
    lora_training: style.loraTraining
  }
}

// Load styles from database
export async function loadStyles(): Promise<Style[]> {
  try {
    if (!isSupabaseConfigured() || !supabase) {
      console.log('⚠️ Supabase not configured, returning empty styles array')
      return []
    }

    console.log('🗄️ Loading styles from Supabase database...')
    const { data, error } = await supabase
      .from('styles')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      console.error('❌ Supabase error loading styles:', error)
      return []
    }

    if (data && data.length > 0) {
      const styles = data.map(rowToStyle)
      console.log(`✅ Loaded ${styles.length} styles from database`)
      return styles
    } else {
      console.log('📄 No styles found in database')
      return []
    }
  } catch (error) {
    console.error('❌ Error loading styles from database:', error)
    return []
  }
}

// Save styles to database
export async function saveStyles(styles: Style[]): Promise<void> {
  try {
    if (!isSupabaseConfigured() || !supabase) {
      throw new Error('Supabase not configured')
    }

    console.log(`🗄️ Saving ${styles.length} styles to Supabase database...`)
    
    // Convert styles to database rows
    const rows = styles.map(styleToRow)
    
    // Upsert all styles (insert new, update existing)
    const { error } = await supabase
      .from('styles')
      .upsert(rows, { onConflict: 'id' })
    
    if (error) {
      console.error('❌ Supabase save error:', error)
      throw new Error(`Database save failed: ${error.message}`)
    }
    
    console.log(`✅ Successfully saved ${styles.length} styles to database`)
  } catch (error) {
    console.error('❌ Critical error in saveStyles:', error)
    throw error
  }
}

// Find style by ID
export async function findStyleById(id: string): Promise<Style | null> {
  try {
    if (!isSupabaseConfigured() || !supabase) {
      return null
    }

    const { data, error } = await supabase
      .from('styles')
      .select('*')
      .eq('id', id)
      .single()

    if (error || !data) {
      return null
    }

    return rowToStyle(data)
  } catch (error) {
    console.error('❌ Error finding style by ID:', error)
    return null
  }
}

// Save single style
export async function saveStyle(style: Style): Promise<void> {
  try {
    if (!isSupabaseConfigured() || !supabase) {
      throw new Error('Supabase not configured')
    }

    const row = styleToRow(style)
    const { error } = await supabase
      .from('styles')
      .upsert(row, { onConflict: 'id' })

    if (error) {
      throw new Error(`Failed to save style: ${error.message}`)
    }

    console.log(`✅ Successfully saved style: ${style.name}`)
  } catch (error) {
    console.error('❌ Error saving style:', error)
    throw error
  }
}

// Create style from training
export async function createStyleFromTraining(params: {
  styleName: string
  category: string
  description?: string
  triggerWord: string
  trainingId: string
  modelUrl: string
  zipUrl: string
  thumbnailImage?: string // Optional: first training image as thumbnail
  tags?: string[]
}): Promise<Style> {
  const styleId = `style-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  
  const style: Style = {
    id: styleId,
    name: params.styleName,
    category: params.category,
    description: params.description,
    imageUrl: params.thumbnailImage || '/placeholder-thumbnail.svg',
    imageBase64: params.thumbnailImage || '',
    tags: params.tags || [],
    createdAt: new Date(),
    updatedAt: new Date(),
    usageCount: 0,
    isDefault: false,
    loraTraining: {
      status: 'completed',
      trainingId: params.trainingId,
      modelUrl: params.modelUrl,
      triggerWord: params.triggerWord,
      trainingImages: [], // Training images not stored after completion
      trainingZipUrl: params.zipUrl,
      createdAt: new Date(),
      completedAt: new Date(),
      steps: 1000,
      resolution: 1024,
      loraType: 'style' // Key difference from personas
    }
  }

  await saveStyle(style)
  return style
}

// Delete style
export async function deleteStyle(styleId: string): Promise<void> {
  try {
    if (!isSupabaseConfigured() || !supabase) {
      throw new Error('Supabase not configured')
    }

    const { error } = await supabase
      .from('styles')
      .delete()
      .eq('id', styleId)

    if (error) {
      throw new Error(`Failed to delete style: ${error.message}`)
    }

    console.log(`✅ Successfully deleted style: ${styleId}`)
  } catch (error) {
    console.error('❌ Error deleting style:', error)
    throw error
  }
} 