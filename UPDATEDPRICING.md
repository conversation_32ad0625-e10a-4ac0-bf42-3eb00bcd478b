# Credit-Based Pricing for Per-Operation Charging: Complete Implementation Guide

## Table of Contents
1. [Core Concept & Architecture](#core-concept--architecture)
2. [Credit System Models](#credit-system-models)
3. [Operation-to-Credit Mapping Examples](#operation-to-credit-mapping-examples)
4. [Implementation Framework](#implementation-framework)
5. [Credit Management Logic](#credit-management-logic)
6. [Advanced Features](#advanced-features)
7. [Pricing Strategy Examples](#pricing-strategy-examples)
8. [Real-World Implementation Examples](#real-world-implementation-examples)
9. [Technical Implementation Considerations](#technical-implementation-considerations)
10. [Best Practices & Recommendations](#best-practices--recommendations)

## Core Concept & Architecture

**Credit-based pricing** transforms complex, multi-dimensional operations into a unified currency system where:
- Users purchase credits upfront (prepaid model)
- Each operation consumes a specific number of credits
- Different operations have different credit costs based on computational complexity/value

## Credit System Models

### Model A: Committed Spend
- Credits are shown in fiat currency (USD/EUR)
- Users commit to spending $X per month
- Operations deduct from this balance
- Example: $100/month commitment, API calls cost $0.01 each

### Model B: Credits as Currency
- Credits are proprietary tokens/coins
- Operations cost X credits each
- Users see "credits remaining" not dollar amounts
- Example: 1000 credits/month, API calls cost 1 credit each

## Operation-to-Credit Mapping Examples

Based on real-world implementations:

| Operation Type | Credit Cost | Rationale |
|---------------|-------------|-----------|
| Simple API call | 1 credit | Base unit of work |
| Data enrichment | 1.5 credits | Requires external data sources |
| AI text generation | 5 credits | High compute cost |
| Image processing | 10 credits | GPU-intensive |
| Complex analysis | 15 credits | Multi-step processing |
| Video processing | 50 credits | Highest resource usage |

## Implementation Framework

### Database Schema

```sql
-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY,
    email VARCHAR(255),
    subscription_plan VARCHAR(50),
    created_at TIMESTAMP
);

-- Credit Wallets
CREATE TABLE credit_wallets (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    balance INTEGER DEFAULT 0,
    total_purchased INTEGER DEFAULT 0,
    total_consumed INTEGER DEFAULT 0,
    last_updated TIMESTAMP
);

-- Credit Transactions
CREATE TABLE credit_transactions (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    operation_type VARCHAR(100),
    credits_consumed INTEGER,
    operation_metadata JSONB,
    timestamp TIMESTAMP,
    status VARCHAR(20) -- 'completed', 'failed', 'refunded'
);

-- Credit Grants (for monthly renewals, bonuses)
CREATE TABLE credit_grants (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    credits_granted INTEGER,
    grant_type VARCHAR(50), -- 'monthly', 'bonus', 'refund'
    expires_at TIMESTAMP,
    granted_at TIMESTAMP
);
```

### Credit Pricing Configuration

```json
{
  "operations": {
    "api_call_basic": {
      "credits": 1,
      "description": "Basic API request"
    },
    "data_enrichment": {
      "credits": 2,
      "description": "Contact/company enrichment"
    },
    "ai_content_generation": {
      "credits": 5,
      "description": "AI-powered content creation"
    },
    "bulk_processing": {
      "credits": 10,
      "description": "Batch data processing"
    }
  },
  "plans": {
    "starter": {
      "monthly_credits": 1000,
      "price_usd": 29,
      "rollover_limit": 2000
    },
    "professional": {
      "monthly_credits": 5000,
      "price_usd": 99,
      "rollover_limit": 10000
    }
  }
}
```

## Credit Management Logic

### Credit Consumption Flow

```python
class CreditManager:
    def consume_credits(self, user_id, operation_type, metadata=None):
        # 1. Get operation cost
        credit_cost = self.get_operation_cost(operation_type)
        
        # 2. Check user balance
        wallet = self.get_user_wallet(user_id)
        if wallet.balance < credit_cost:
            raise InsufficientCreditsError()
        
        # 3. Deduct credits atomically
        with transaction():
            wallet.balance -= credit_cost
            wallet.total_consumed += credit_cost
            wallet.save()
            
            # 4. Log transaction
            CreditTransaction.create(
                user_id=user_id,
                operation_type=operation_type,
                credits_consumed=credit_cost,
                operation_metadata=metadata,
                status='completed'
            )
        
        return wallet.balance

    def grant_monthly_credits(self, user_id, plan_credits):
        """Called monthly to replenish credits"""
        wallet = self.get_user_wallet(user_id)
        plan = self.get_user_plan(user_id)
        
        # Handle rollover limits
        max_balance = plan.rollover_limit
        new_balance = min(wallet.balance + plan_credits, max_balance)
        
        wallet.balance = new_balance
        wallet.save()
        
        CreditGrant.create(
            user_id=user_id,
            credits_granted=plan_credits,
            grant_type='monthly'
        )
```

## Advanced Features

### Credit Expiration

```python
def expire_credits(self, user_id):
    """Remove expired credits based on grant dates"""
    expired_grants = CreditGrant.objects.filter(
        user_id=user_id,
        expires_at__lt=timezone.now(),
        status='active'
    )
    
    total_expired = sum(grant.credits_granted for grant in expired_grants)
    
    wallet = self.get_user_wallet(user_id)
    wallet.balance = max(0, wallet.balance - total_expired)
    wallet.save()
    
    expired_grants.update(status='expired')
```

### Credit Priority System

```python
def consume_credits_with_priority(self, user_id, credits_needed):
    """Consume credits in priority order (promotional first, then regular)"""
    grants = CreditGrant.objects.filter(
        user_id=user_id,
        status='active',
        expires_at__gt=timezone.now()
    ).order_by('priority', 'granted_at')  # Lower priority number = higher priority
    
    remaining_needed = credits_needed
    
    for grant in grants:
        if remaining_needed <= 0:
            break
            
        available = min(grant.remaining_credits, remaining_needed)
        grant.remaining_credits -= available
        remaining_needed -= available
        grant.save()
    
    if remaining_needed > 0:
        raise InsufficientCreditsError()
```

## Pricing Strategy Examples

### Tiered Credit Pricing

```json
{
  "credit_packages": [
    {
      "credits": 1000,
      "price_usd": 10,
      "cost_per_credit": 0.01
    },
    {
      "credits": 5000,
      "price_usd": 40,
      "cost_per_credit": 0.008,
      "discount": "20%"
    },
    {
      "credits": 25000,
      "price_usd": 175,
      "cost_per_credit": 0.007,
      "discount": "30%"
    }
  ]
}
```

### Operation Complexity Pricing

```json
{
  "pricing_tiers": {
    "light": {
      "operations": ["api_call", "data_lookup"],
      "credit_multiplier": 1.0
    },
    "medium": {
      "operations": ["data_enrichment", "basic_ai"],
      "credit_multiplier": 2.0
    },
    "heavy": {
      "operations": ["complex_ai", "bulk_processing"],
      "credit_multiplier": 5.0
    },
    "premium": {
      "operations": ["video_processing", "advanced_ai"],
      "credit_multiplier": 10.0
    }
  }
}
```

## Real-World Implementation Examples

### Clay's Model (Lead enrichment tool)
- 2,000-50,000 credits/month depending on plan
- Creating a prospect table: 10 credits
- Enriching a contact: 1.5 credits per row
- Credits roll over up to 2x monthly allocation

### Snowflake's Model (Data warehouse)
- Credits based on compute warehouse size
- X-Small warehouse: 1 credit/hour
- Large warehouse: 8 credits/hour
- Serverless features: Variable credit rates

## Technical Implementation Considerations

### Real-time Balance Tracking

```python
class RealtimeCreditTracker:
    def __init__(self, redis_client):
        self.redis = redis_client
    
    def get_balance(self, user_id):
        """Get real-time balance from cache"""
        cached_balance = self.redis.get(f"credits:{user_id}")
        if cached_balance is None:
            # Fallback to database
            wallet = CreditWallet.objects.get(user_id=user_id)
            self.redis.setex(f"credits:{user_id}", 300, wallet.balance)
            return wallet.balance
        return int(cached_balance)
    
    def consume_credits_atomic(self, user_id, credits):
        """Atomic credit consumption with Redis"""
        pipeline = self.redis.pipeline()
        pipeline.decrby(f"credits:{user_id}", credits)
        pipeline.expire(f"credits:{user_id}", 300)
        results = pipeline.execute()
        
        new_balance = results[0]
        if new_balance < 0:
            # Rollback
            self.redis.incrby(f"credits:{user_id}", credits)
            raise InsufficientCreditsError()
        
        return new_balance
```

### Billing Integration

```python
def sync_credit_usage_to_billing():
    """Sync credit consumption to billing system"""
    transactions = CreditTransaction.objects.filter(
        synced_to_billing=False,
        status='completed'
    )
    
    for transaction in transactions:
        billing_event = {
            "customer_id": transaction.user_id,
            "event_type": "credit_consumption",
            "credits_used": transaction.credits_consumed,
            "operation_type": transaction.operation_type,
            "timestamp": transaction.timestamp.isoformat(),
            "metadata": transaction.operation_metadata
        }
        
        # Send to billing system (Stripe, Orb, etc.)
        billing_api.create_usage_record(billing_event)
        transaction.synced_to_billing = True
        transaction.save()
```

## Best Practices & Recommendations

1. **Start Simple**: Begin with basic credit-per-operation mapping
2. **Monitor Usage Patterns**: Track which operations consume the most credits
3. **Transparent Pricing**: Clearly show credit costs before operations
4. **Grace Periods**: Allow small negative balances with warnings
5. **Usage Analytics**: Provide detailed credit usage dashboards
6. **Refund Handling**: Implement credit refunds for failed operations
7. **Rate Limiting**: Prevent credit exhaustion through rate limits
8. **Audit Trails**: Maintain detailed logs for financial compliance

## For Thumbnex Implementation

### Recommended Credit Structure for Thumbnail Generation

```json
{
  "operations": {
    "persona_training": {
      "credits": 20,
      "description": "Train custom persona model (minimum 6 images)",
      "estimated_time": "2-3 minutes"
    },
    "style_training": {
      "credits": 15,
      "description": "Train custom style model (3-4 images)",
      "estimated_time": "2-3 minutes"
    },
    "thumbnail_generation_basic": {
      "credits": 5,
      "description": "Generate thumbnail with existing models"
    },
    "thumbnail_generation_custom": {
      "credits": 10,
      "description": "Generate thumbnail with custom persona + style"
    },
    "batch_generation": {
      "credits": 3,
      "description": "Per thumbnail in batch (5+ thumbnails)",
      "min_batch_size": 5
    }
  },
  "subscription_plans": {
    "creator": {
      "monthly_credits": 500,
      "price_usd": 29,
      "rollover_limit": 1000,
      "includes": ["basic_generation", "1_persona_training", "1_style_training"]
    },
    "professional": {
      "monthly_credits": 2000,
      "price_usd": 99,
      "rollover_limit": 4000,
      "includes": ["all_features", "unlimited_training"]
    },
    "agency": {
      "monthly_credits": 10000,
      "price_usd": 299,
      "rollover_limit": 20000,
      "includes": ["all_features", "priority_processing", "api_access"]
    }
  }
}
```

This credit-based system provides the flexibility and transparency that modern SaaS businesses need while ensuring fair, usage-aligned pricing for customers. 