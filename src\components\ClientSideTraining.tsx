'use client'

import React, { useState, useEffect, useMemo } from 'react'
import { Upload, FileImage, Zap, AlertCircle, CheckCircle, Clock, RefreshCw } from 'lucide-react'
import { uploadTrainingImages, validateTrainingImages, TrainingImage } from '../utils/clientZipUpload'
import { useTrainingPolling } from '../hooks/useTrainingPolling'

interface ClientSideTrainingProps {
  onTrainingStarted?: (data: { zipUrl: string; predictionId: string }) => void
  trainingType?: 'persona' | 'style'
}

export function ClientSideTraining({ onTrainingStarted, trainingType = 'persona' }: ClientSideTrainingProps) {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [triggerWord, setTriggerWord] = useState('')
  const [personaName, setPersonaName] = useState('')
  const [styleName, setStyleName] = useState('')
  const [styleCategory, setStyleCategory] = useState('gaming')
  const [styleDescription, setStyleDescription] = useState('')
  const [isUploading, setIsUploading] = useState(false)
  const [isTraining, setIsTraining] = useState(false)
  const [uploadStatus, setUploadStatus] = useState<{
    type: 'idle' | 'success' | 'error'
    message: string
  }>({ type: 'idle', message: '' })

  // Real-time training polling
  const { 
    activeTrainings, 
    addTraining, 
    getTrainingStatus, 
    isPolling,
    hasActiveTrainings,
    pollNow
  } = useTrainingPolling({
    onStatusUpdate: (status) => {
      console.log(`🔄 Training update: ${status.trainingId} - ${status.status} ${status.progress ? `(${status.progress}%)` : ''}`)
      
      // Update upload status with real-time progress
      if (status.progress !== null) {
        setUploadStatus({
          type: 'idle',
          message: `Training in progress... ${status.progress}% complete`
        })
      } else if (status.status === 'processing') {
        setUploadStatus({
          type: 'idle',
          message: 'Training in progress...'
        })
      }
    },
    onComplete: (status) => {
      console.log(`✅ Training completed: ${status.trainingId}`)
      
      if (status.status === 'succeeded') {
        setUploadStatus({
          type: 'success',
          message: `🎉 Training completed! Persona is ready to use.`
        })
        setIsTraining(false)
      } else {
        setUploadStatus({
          type: 'error',
          message: `❌ Training failed: ${status.error || 'Unknown error'}`
        })
        setIsTraining(false)
      }
    },
    onError: (error) => {
      console.error('❌ Polling error:', error)
    }
  })

  // Create blob URLs for image previews with proper cleanup
  const imagePreviewUrls = useMemo(() => {
    return selectedFiles.map(file => URL.createObjectURL(file))
  }, [selectedFiles])

  // Cleanup blob URLs when component unmounts or files change
  useEffect(() => {
    return () => {
      imagePreviewUrls.forEach(url => {
        URL.revokeObjectURL(url)
      })
    }
  }, [imagePreviewUrls])

  const handleFileSelection = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    
    // Validate files with training type
    const validation = validateTrainingImages(files, trainingType)
    
    if (!validation.valid) {
      setUploadStatus({
        type: 'error',
        message: validation.errors.join('; ')
      })
      return
    }
    
    setSelectedFiles(files)
    setUploadStatus({ type: 'idle', message: '' })
  }

  const generateTriggerWord = (name: string, type: 'persona' | 'style' = 'persona') => {
    const prefix = type === 'style' ? 'STYLE' : 'PERSON'
    const sanitized = name.toUpperCase().replace(/[^A-Z0-9]/g, '')
    const random = Math.floor(Math.random() * 999).toString().padStart(3, '0')
    return `${prefix}${sanitized.substring(0, 3)}${random}`
  }

  const handlePersonaNameChange = (name: string) => {
    setPersonaName(name)
    if (name && !triggerWord) {
      setTriggerWord(generateTriggerWord(name, 'persona'))
    }
  }

  const handleStyleNameChange = (name: string) => {
    setStyleName(name)
    if (name && !triggerWord) {
      setTriggerWord(generateTriggerWord(name, 'style'))
    }
  }

  const handleStartTraining = async () => {
    try {
      setIsUploading(true)
      setUploadStatus({ type: 'idle', message: 'Creating ZIP from images...' })

      // Convert Files to TrainingImage format
      const trainingImages: TrainingImage[] = selectedFiles.map(file => ({ file }))

      // Step 1: Upload images as ZIP to Vercel Blob
      const uploadResult = await uploadTrainingImages(
        trainingImages, 
        triggerWord, 
        trainingType === 'persona' ? personaName : styleName,
        trainingType
      )

      if (!uploadResult.success || !uploadResult.zipUrl) {
        throw new Error(uploadResult.error || 'Failed to upload training images')
      }

      setUploadStatus({
        type: 'success',
        message: `ZIP uploaded successfully (${(uploadResult.zipSize! / 1024 / 1024).toFixed(2)}MB)`
      })

      // Step 2: Start LoRA training with simplified API
      setIsUploading(false)
      setIsTraining(true)
      setUploadStatus({ type: 'idle', message: `Starting ${trainingType} LoRA training...` })

      const endpoint = trainingType === 'style' ? '/api/train-style-direct' : '/api/train-lora-direct'
      const requestBody = trainingType === 'style' 
        ? {
            zipUrl: uploadResult.zipUrl,
            triggerWord: triggerWord,
            styleName: styleName,
            category: styleCategory || 'custom',
            description: styleDescription || `Custom style: ${styleName}`
          }
        : {
            zipUrl: uploadResult.zipUrl,
            triggerWord: triggerWord,
            personaName: personaName
          }

      const trainingResponse = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      })

      const trainingResult = await trainingResponse.json()

      if (!trainingResponse.ok || !trainingResult.success) {
        throw new Error(trainingResult.error || 'Failed to start training')
      }

      setUploadStatus({
        type: 'success',
        message: `${trainingType === 'style' ? 'Style' : 'Persona'} training started! Training ID: ${trainingResult.trainingId}`
      })

      // Step 3: Add to real-time polling
      console.log(`🔄 Starting real-time polling for training: ${trainingResult.trainingId}`)
             addTraining(trainingResult.trainingId, {
         status: 'starting',
         progress: null,
         estimatedTime: '1-2 minutes (updates every 5s)'
       })

      // Step 4: Create persona/style automatically when training starts
      if (trainingType === 'persona') {
        try {
          console.log('🎯 Creating persona from training data...')
          const personaResponse = await fetch('/api/create-persona-from-training', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              personaName: personaName,
              triggerWord: triggerWord,
              trainingId: trainingResult.trainingId,
              modelUrl: null, // Don't set model URL until training completes
              zipUrl: uploadResult.zipUrl
            })
          })

          const personaResult = await personaResponse.json()
          
          if (personaResponse.ok && personaResult.success) {
            console.log('✅ Persona created successfully:', personaResult.persona.id)
            setUploadStatus({
              type: 'success',
              message: `Training started & persona created! ID: ${trainingResult.trainingId}`
            })
            
            // Emit custom event to notify PersonaSelector to refresh
            window.dispatchEvent(new CustomEvent('personaCreated', {
              detail: { persona: personaResult.persona }
            }))
          } else {
            console.warn('⚠️ Training started but persona creation failed:', personaResult.error)
          }
        } catch (personaError) {
          console.warn('⚠️ Training started but persona creation failed:', personaError)
        }
      } else {
        // For styles, create style record (same pattern as personas)
        try {
          console.log('🎨 Creating style from training data...')
          const styleResponse = await fetch('/api/create-style-from-training', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              styleName: styleName,
              category: styleCategory || 'custom',
              description: styleDescription || `Custom style: ${styleName}`,
              triggerWord: triggerWord,
              trainingId: trainingResult.trainingId,
              modelUrl: null, // Don't set model URL until training completes
              zipUrl: uploadResult.zipUrl
            })
          })

          const styleResult = await styleResponse.json()
          
          if (styleResponse.ok && styleResult.success) {
            console.log('✅ Style created successfully:', styleResult.style.id)
            setUploadStatus({
              type: 'success',
              message: `Training started & style created! ID: ${trainingResult.trainingId}`
            })
            
            // Emit custom event to notify StyleSelector to refresh
            window.dispatchEvent(new CustomEvent('styleCreated', {
              detail: { style: styleResult.style }
            }))
          } else {
            console.warn('⚠️ Training started but style creation failed:', styleResult.error)
          }
        } catch (styleError) {
          console.warn('⚠️ Training started but style creation failed:', styleError)
        }
      }

      // Notify parent component
      if (onTrainingStarted) {
        onTrainingStarted({
          zipUrl: uploadResult.zipUrl,
          predictionId: trainingResult.trainingId
        })
      }

    } catch (error) {
      console.error('❌ Training start failed:', error)
      setUploadStatus({
        type: 'error',
        message: error instanceof Error ? error.message : 'Unknown error'
      })
    } finally {
      setIsUploading(false)
      setIsTraining(false)
    }
  }

  const canStartTraining = (
    trainingType === 'persona' 
      ? selectedFiles.length >= 1 // Just need at least 1 image for persona, no hard limit
      : selectedFiles.length >= 3 && selectedFiles.length <= 6 // Style training keeps hard validation
  ) && triggerWord && 
    (trainingType === 'persona' ? personaName : styleName) && 
    !isUploading && !isTraining

  const isActive = activeTrainings.some(t => !t.isComplete)

  return (
    <div className="bg-slate-800 rounded-lg p-6 border border-slate-600">
      <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
        <Zap className="w-5 h-5 text-purple-400" />
        {trainingType === 'style' ? 'Style' : 'Persona'} LoRA Training
      </h3>

      {/* Status Display */}
      {uploadStatus.message && (
        <div className={`mb-4 p-3 rounded-lg border ${
          uploadStatus.type === 'error' 
            ? 'bg-red-900/30 border-red-600/50 text-red-200'
            : uploadStatus.type === 'success'
            ? 'bg-green-900/30 border-green-600/50 text-green-200'
            : 'bg-blue-900/30 border-blue-600/50 text-blue-200'
        }`}>
          <div className="flex items-center gap-2">
            {uploadStatus.type === 'error' && <AlertCircle className="w-4 h-4" />}
            {uploadStatus.type === 'success' && <CheckCircle className="w-4 h-4" />}
            <span className="text-sm">{uploadStatus.message}</span>
          </div>
        </div>
      )}

      {/* Real-time Training Status */}
      {hasActiveTrainings && (
        <div className="mb-4 p-4 bg-purple-900/30 border border-purple-600/50 rounded-lg">
          <div className="flex items-center justify-between mb-4">
            <h4 className="font-medium text-purple-200">
              Active Training Jobs ({activeTrainings.length})
            </h4>
            <div className="flex gap-2">
              <button
                onClick={() => pollNow(false)}
                disabled={!hasActiveTrainings}
                className="px-3 py-1 text-sm bg-purple-600 hover:bg-purple-500 disabled:bg-slate-600 disabled:text-slate-400 text-white rounded transition-colors"
                title="Check status now (may use cache)"
              >
                Check Now (2s)
              </button>
              <button
                onClick={() => pollNow(true)}
                disabled={!hasActiveTrainings}
                className="px-3 py-1 text-sm bg-orange-600 hover:bg-orange-500 disabled:bg-slate-600 disabled:text-slate-400 text-white rounded transition-colors"
                title="Force fresh check from Replicate (bypasses cache)"
              >
                Force Refresh
              </button>
            </div>
          </div>

          {activeTrainings.map((training) => (
            <div key={training.trainingId} className="mb-3 last:mb-0">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-purple-100">
                  Training: {training.trainingId.slice(-8)}...
                </span>
                <div className="flex items-center gap-2">
                  {training.cached && (
                    <span className="text-xs px-2 py-1 bg-slate-700 text-slate-300 rounded">
                      💾 {training.cacheAge}s
                    </span>
                  )}
                  {!training.cached && (
                    <span className="text-xs px-2 py-1 bg-green-900/50 text-green-300 rounded">
                      🔥 Fresh
                    </span>
                  )}
                  <span className={`text-sm px-2 py-1 rounded ${
                    training.status === 'succeeded' ? 'bg-green-900/50 text-green-300' :
                    training.status === 'failed' ? 'bg-red-900/50 text-red-300' :
                    training.status === 'processing' ? 'bg-blue-900/50 text-blue-300' :
                    'bg-green-900/50 text-green-300'
                  }`}>
                    {training.status}
                  </span>
                </div>
              </div>

              {training.progress !== null && (
                <div className="mb-2">
                  <div className="flex justify-between text-sm text-purple-200 mb-1">
                    <span>Progress</span>
                    <span>{training.progress}%</span>
                  </div>
                  <div className="w-full bg-purple-800/50 rounded-full h-2">
                    <div
                      className="bg-purple-400 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${training.progress}%` }}
                    />
                  </div>
                </div>
              )}

              {training.estimatedTime && (
                <p className="text-sm text-purple-300">
                  ⏱️ Estimated time: {training.estimatedTime}
                </p>
              )}

              {training.error && (
                <p className="text-sm text-red-300 mt-1">
                  ❌ Error: {training.error}
                </p>
              )}

              {training.isComplete && training.modelUrl && (
                <p className="text-sm text-green-300 mt-1">
                  ✅ Model ready: {training.modelUrl.slice(-12)}...
                </p>
              )}
            </div>
          ))}

          {isPolling && (
            <div className="text-sm text-purple-300 mt-3 pt-3 border-t border-purple-600/30">
              🔄 Auto-polling every 2s with intelligent caching
            </div>
          )}
        </div>
      )}

      {/* File Selection */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-white mb-2">
            Training Images ({trainingType === 'persona' ? '10-20 recommended' : '3-6 required'})
          </label>
          <div className="relative">
            <input
              type="file"
              multiple
              accept="image/*"
              onChange={handleFileSelection}
              className="hidden"
              id="training-files"
            />
            <label
              htmlFor="training-files"
              className="flex items-center justify-center gap-2 w-full px-4 py-6 border-2 border-dashed border-slate-600 rounded-lg hover:border-slate-500 cursor-pointer transition-colors"
            >
              <Upload className="w-5 h-5 text-slate-400" />
              <span className="text-slate-300">
                {selectedFiles.length > 0 
                  ? `${selectedFiles.length} files selected`
                  : 'Select training images'
                }
              </span>
            </label>
          </div>
          
          {selectedFiles.length > 0 && (
            <div className="mt-3">
              <div className="grid grid-cols-6 gap-2">
                {selectedFiles.slice(0, 12).map((file, index) => (
                  <div key={index} className="relative">
                    <img
                      src={imagePreviewUrls[index]}
                      alt={`Training ${index + 1}`}
                      className="w-full h-16 object-cover rounded border border-slate-600"
                    />
                    <div className="absolute bottom-0 left-0 bg-black/70 text-white text-xs px-1">
                      {index + 1}
                    </div>
                  </div>
                ))}
                {selectedFiles.length > 12 && (
                  <div className="flex items-center justify-center h-16 bg-slate-700 rounded border border-slate-600 text-slate-300 text-xs">
                    +{selectedFiles.length - 12} more
                  </div>
                )}
              </div>
              
              {/* Image count feedback */}
              {trainingType === 'persona' && (
                <div className="mt-2 text-xs text-slate-400">
                  {selectedFiles.length < 10 && (
                    <p>💡 Tip: 10-20 images usually give the best results for persona training</p>
                  )}
                  {selectedFiles.length >= 10 && selectedFiles.length <= 20 && (
                    <p>✅ Perfect! This is the ideal range for persona training</p>
                  )}
                  {selectedFiles.length > 20 && (
                    <p>📋 Note: More than 20 images may not improve results much</p>
                  )}
                </div>
              )}
              
              {trainingType === 'style' && (
                <div className="mt-2 text-xs text-slate-400">
                  {selectedFiles.length >= 3 && selectedFiles.length <= 6 && (
                    <p>✅ Perfect! {selectedFiles.length} images is ideal for style training</p>
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Training Details */}
        {trainingType === 'persona' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-white mb-2">
                Persona Name
              </label>
              <input
                type="text"
                value={personaName}
                onChange={(e) => handlePersonaNameChange(e.target.value)}
                placeholder="e.g., Gaming Avatar"
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-white mb-2">
                Trigger Word
              </label>
              <input
                type="text"
                value={triggerWord}
                onChange={(e) => setTriggerWord(e.target.value.toUpperCase())}
                placeholder="e.g., PERSON123"
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Style Name
                </label>
                <input
                  type="text"
                  value={styleName}
                  onChange={(e) => handleStyleNameChange(e.target.value)}
                  placeholder="e.g., Retro Gaming"
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Category
                </label>
                <select
                  value={styleCategory}
                  onChange={(e) => setStyleCategory(e.target.value)}
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  <option value="gaming">Gaming</option>
                  <option value="tech">Tech</option>
                  <option value="vlog">Vlog</option>
                  <option value="educational">Educational</option>
                  <option value="fitness">Fitness</option>
                  <option value="business">Business</option>
                  <option value="creative">Creative</option>
                  <option value="minimal">Minimal</option>
                  <option value="retro">Retro</option>
                  <option value="cinematic">Cinematic</option>
                  <option value="other">Other</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-white mb-2">
                Description (Optional)
              </label>
              <textarea
                value={styleDescription}
                onChange={(e) => setStyleDescription(e.target.value)}
                placeholder="Describe the visual style you want to train..."
                rows={2}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-white mb-2">
                Trigger Word
              </label>
              <input
                type="text"
                value={triggerWord}
                onChange={(e) => setTriggerWord(e.target.value.toUpperCase())}
                placeholder="e.g., STYLE123"
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
          </div>
        )}

        {/* Start Training Button */}
        <button
          onClick={handleStartTraining}
          disabled={!canStartTraining}
          className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
            canStartTraining
              ? 'bg-purple-600 hover:bg-purple-700 text-white'
              : 'bg-slate-600 text-slate-400 cursor-not-allowed'
          }`}
        >
          {isUploading && '📦 Creating ZIP...'}
          {isTraining && '🚀 Starting Training...'}
          {!isUploading && !isTraining && 'Start LoRA Training'}
        </button>

        {/* Info */}
        <div className="text-sm text-slate-400 space-y-1">
          <p>• Images will be zipped and uploaded to Vercel Blob</p>
          <p>• Training uses Replicate&apos;s Fast FLUX Trainer</p>
          {trainingType === 'persona' ? (
            <p>• Estimated cost: $2-4, Time: 15-20 minutes</p>
          ) : (
            <p>• Estimated cost: $1-2, Time: 8-12 minutes (fewer images)</p>
          )}
        </div>
      </div>
    </div>
  )
} 