const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables')
  console.log('Required: NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function setupTrainingDatabase() {
  try {
    console.log('🚀 Setting up training database schema...')
    
    // Read the SQL schema file
    const schemaPath = path.join(__dirname, 'database-schema.sql')
    if (!fs.existsSync(schemaPath)) {
      console.error('❌ database-schema.sql not found')
      process.exit(1)
    }
    
    const schemaSql = fs.readFileSync(schemaPath, 'utf8')
    
    // Execute the schema
    console.log('📝 Executing database schema...')
    const { error } = await supabase.rpc('exec_sql', { sql: schemaSql })
    
    if (error) {
      console.error('❌ Error executing schema:', error)
      
      // Try alternative method - execute parts separately
      console.log('🔄 Trying alternative setup method...')
      
      // Split SQL into individual statements and execute
      const statements = schemaSql
        .split(';')
        .map(s => s.trim())
        .filter(s => s.length > 0)
      
      for (const statement of statements) {
        try {
          if (statement.toLowerCase().includes('create table') || 
              statement.toLowerCase().includes('create index') ||
              statement.toLowerCase().includes('create trigger') ||
              statement.toLowerCase().includes('create function') ||
              statement.toLowerCase().includes('alter table')) {
            
            console.log(`📄 Executing: ${statement.substring(0, 50)}...`)
            const { error: stmtError } = await supabase.rpc('exec_sql', { sql: statement + ';' })
            
            if (stmtError) {
              console.warn(`⚠️ Statement failed (may already exist): ${stmtError.message}`)
            }
          }
        } catch (err) {
          console.warn(`⚠️ Statement error: ${err.message}`)
        }
      }
    }
    
    // Verify tables were created
    console.log('🔍 Verifying table creation...')
    
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', ['personas', 'training_jobs', 'webhook_events'])
    
    if (tablesError) {
      console.error('❌ Error checking tables:', tablesError)
    } else {
      console.log('✅ Tables found:', tables.map(t => t.table_name))
    }
    
    // Test basic operations
    console.log('🧪 Testing database operations...')
    
    // Test personas table
    const { data: personasTest, error: personasError } = await supabase
      .from('personas')
      .select('id, name')
      .limit(1)
    
    if (personasError) {
      console.error('❌ Personas table test failed:', personasError)
    } else {
      console.log('✅ Personas table accessible')
    }
    
    // Test training_jobs table
    const { data: jobsTest, error: jobsError } = await supabase
      .from('training_jobs')
      .select('id')
      .limit(1)
    
    if (jobsError) {
      console.error('❌ Training jobs table test failed:', jobsError)
    } else {
      console.log('✅ Training jobs table accessible')
    }
    
    // Test webhook_events table
    const { data: webhooksTest, error: webhooksError } = await supabase
      .from('webhook_events')
      .select('id')
      .limit(1)
    
    if (webhooksError) {
      console.error('❌ Webhook events table test failed:', webhooksError)
    } else {
      console.log('✅ Webhook events table accessible')
    }
    
    console.log('🎉 Database setup completed successfully!')
    console.log('🔗 Ready for training dashboard integration')
    
  } catch (error) {
    console.error('❌ Setup failed:', error)
    process.exit(1)
  }
}

// Run setup
setupTrainingDatabase() 