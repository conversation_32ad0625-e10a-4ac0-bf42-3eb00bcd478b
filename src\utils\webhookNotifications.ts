/**
 * Webhook notification utilities for real-time updates
 * This will eventually be replaced with WebSocket/SSE for live updates
 * Server-side utility functions for managing notifications
 */

export interface WebhookNotification {
  type: 'training_completed' | 'training_failed' | 'training_progress'
  personaId: string
  personaName: string
  status: string
  progress?: number
  modelUrl?: string
  error?: string
  timestamp: Date
}

// In-memory store for recent notifications (for development)
// In production, this should be replaced with Redis or a database
const notifications: WebhookNotification[] = []
const MAX_NOTIFICATIONS = 100

/**
 * Add a new webhook notification
 */
export function addWebhookNotification(notification: Omit<WebhookNotification, 'timestamp'>) {
  const fullNotification: WebhookNotification = {
    ...notification,
    timestamp: new Date()
  }
  
  notifications.unshift(fullNotification)
  
  // Keep only the most recent notifications
  if (notifications.length > MAX_NOTIFICATIONS) {
    notifications.splice(MAX_NOTIFICATIONS)
  }
  
  console.log('📣 Webhook notification added:', {
    type: fullNotification.type,
    persona: fullNotification.personaName,
    status: fullNotification.status
  })
  
  // TODO: Send to all connected WebSocket clients
  // broadcastToWebSocketClients(fullNotification)
  
  // TODO: Send push notification if enabled
  // sendPushNotification(fullNotification)
}

/**
 * Get recent notifications for a specific persona
 */
export function getPersonaNotifications(personaId: string): WebhookNotification[] {
  return notifications.filter(n => n.personaId === personaId)
}

/**
 * Get all recent notifications
 */
export function getAllNotifications(): WebhookNotification[] {
  return [...notifications]
}

/**
 * Clear notifications older than specified time
 */
export function clearOldNotifications(olderThanHours: number = 24) {
  const cutoff = new Date(Date.now() - (olderThanHours * 60 * 60 * 1000))
  const initialLength = notifications.length
  
  // Remove old notifications
  for (let i = notifications.length - 1; i >= 0; i--) {
    if (notifications[i].timestamp < cutoff) {
      notifications.splice(i, 1)
    }
  }
  
  const removed = initialLength - notifications.length
  if (removed > 0) {
    console.log(`🧹 Cleared ${removed} old notifications`)
  }
}

/**
 * Check if persona has recent training activity
 */
export function hasRecentTrainingActivity(personaId: string, withinMinutes: number = 30): boolean {
  const cutoff = new Date(Date.now() - (withinMinutes * 60 * 1000))
  return notifications.some(n => 
    n.personaId === personaId && 
    n.timestamp > cutoff &&
    (n.type === 'training_progress' || n.type === 'training_completed')
  )
}

/**
 * Get training progress notifications for a persona
 */
export function getTrainingProgress(personaId: string): WebhookNotification[] {
  return notifications
    .filter(n => n.personaId === personaId && n.type === 'training_progress')
    .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
    .slice(0, 10) // Last 10 progress updates
}

// Auto-cleanup old notifications every hour
if (typeof window === 'undefined') { // Server-side only
  setInterval(() => {
    clearOldNotifications(24) // Keep 24 hours of notifications
  }, 60 * 60 * 1000) // Run every hour
} 