# Local Development Guide for Persona Training

## 🚀 Quick Start

```bash
# 1. Clone and setup
git clone <your-repo>
cd thumbnex

# 2. Install dependencies
npm install

# 3. Create environment file
cp .env.example .env.local
# Add your REPLICATE_API_TOKEN

# 4. Start development
npm run dev

# 5. In another terminal, run the dev helper
npm run dev-helper
```

**What you can test immediately:**
- ✅ Create personas (works offline)
- ✅ Start training (sends to Replicate)
- ✅ Monitor training (use dev helper)
- ✅ Generate images (works with completed personas)

## What Works Locally vs. What Doesn't

### ✅ **Works Locally:**
- Creating personas with image upload
- Processing and storing training images
- Starting LoRA training on Replicate
- Manual status checking
- Image generation with completed personas
- All UI components and interactions

### ❌ **Doesn't Work Locally (Webhook Related):**
- Automatic training completion notifications
- Real-time training progress updates
- Automatic persona status updates

### 🔄 **Workarounds for Local Development:**
- Manual training status polling
- Test with pre-trained personas
- Mock webhook responses for testing

## Local Setup Instructions

### 1. Environment Variables

Create `.env.local` in your project root:

```env
# Required for local development
REPLICATE_API_TOKEN=your_replicate_api_token_here
REPLICATE_USERNAME=your_replicate_username_here

# Optional: For testing webhook logic locally
WEBHOOK_BASE_URL=http://localhost:3000
REPLICATE_WEBHOOK_SECRET=test-secret-for-local

# Database (optional - will use in-memory storage if not provided)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here
```

### 2. Install and Run

```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

### 3. Access the App

Open [http://localhost:3000](http://localhost:3000) in your browser.

## Local Development Workflow

### 1. Create a Persona
```
1. Click "Create New Persona" in the persona selector
2. Upload 10+ face images (drag & drop or browse)
3. Enter a unique trigger word (e.g., "LOCALTOK")
4. Submit the form
```

### 2. Start Training
```
1. Find your persona in the selector (shows "Ready to Train")
2. Click the "Train" button
3. Training will start on Replicate (you'll see the training ID)
4. Status will show as "Training 0%" initially
```

### 3. Monitor Training Progress (Manual)

Since webhooks don't work locally, you have several options:

#### Option A: Development Helper Script (Recommended)
```bash
# Run the interactive development helper
npm run dev-helper

# This will give you options to:
# 1. Check training status for all personas
# 2. List all personas with details
# 3. Simulate webhook calls for testing
# 4. Get manual status update instructions
```

#### Option B: Manual Status Check
```javascript
// Open browser console and run:
fetch('/api/train-lora?personaId=your_persona_id')
  .then(r => r.json())
  .then(console.log)
```

#### Option C: Check on Replicate Dashboard
```
1. Go to replicate.com/trainings
2. Find your training by ID
3. Monitor progress there
```

#### Option D: Use Polling Component
The PersonaSelector automatically polls every 10 seconds, so just wait and refresh the UI.

### 4. Test Generation
```
1. Once training is complete (status shows "Ready")
2. Select your trained persona
3. Enter a prompt like: "LOCALTOK as a superhero"
4. Generate your thumbnail!
```

## Testing Webhook Logic Locally

### Option 1: Manual Webhook Testing

Create a test script to simulate webhook calls:

```javascript
// test-webhook.js
const webhook = async () => {
  const response = await fetch('http://localhost:3000/api/webhooks/training-complete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'replicate-signature': 'sha256=test-signature'
    },
    body: JSON.stringify({
      id: 'your-training-id',
      status: 'succeeded',
      destination: 'your-username/model-name',
      type: 'training'
    })
  })
  
  console.log(await response.json())
}

webhook()
```

### Option 2: ngrok for Real Webhooks

If you want to test real webhooks locally:

```bash
# Install ngrok
npm install -g ngrok

# Expose your local server
ngrok http 3000

# Use the ngrok URL as your WEBHOOK_BASE_URL
# e.g., https://abc123.ngrok.io
```

Then set in `.env.local`:
```env
WEBHOOK_BASE_URL=https://abc123.ngrok.io
```

## Development Tips

### 1. Use Test Images
Keep a folder of 10+ test face images for quick persona creation during development.

### 2. Mock Completed Personas
For UI testing, you can manually set a persona's status:

```javascript
// In browser console
const personas = JSON.parse(localStorage.getItem('personas') || '[]')
personas[0].loraTraining.status = 'completed'
personas[0].loraTraining.modelUrl = 'test-username/test-model'
localStorage.setItem('personas', JSON.stringify(personas))
location.reload()
```

### 3. Database vs. Memory Storage
- **With Supabase**: Personas persist between sessions
- **Without Supabase**: Personas stored in memory (lost on restart)

### 4. Faster Development Cycle
```bash
# For faster iteration during development
npm run dev --turbo
```

## Troubleshooting Local Issues

### "Persona not found" Error During Training

This is a timing issue between persona creation and training initiation. Here's how to debug and fix it:

#### 1. Check Browser Console
Look for these logs in your browser's developer console:
```
⏳ Waiting for persona to be fully saved...
🚀 Attempting to start training (attempt 1/3)...
```

#### 2. Check Server Logs
In your terminal running `npm run dev`, look for:
```
💾 Saving persona: [Name] ([ID])
✅ Successfully saved persona: [Name]
🔍 Looking for persona with ID: [ID]
```

#### 3. Test the Flow Manually
Run the test script to isolate the issue:
```bash
npm run test-persona-flow
```

This will:
- Create a test persona
- Wait 2 seconds
- Check if the persona exists
- Try to start training

#### 4. Use the Development Helper
```bash
npm run dev-helper
# Choose option 2: List all personas
# Check if your persona appears in the list
```

#### 5. Common Fixes

**If using in-memory storage (no Supabase):**
- Restart your development server: `Ctrl+C` then `npm run dev`
- The retry mechanism should handle most timing issues automatically

**If using Supabase:**
- Check your database connection in the Supabase dashboard
- Verify the persona was actually saved to the database
- Check for any database errors in the server logs

**If the error persists:**
- Try creating the persona first, then manually clicking "Train" from the persona selector
- This separates persona creation from training initiation

### Webhook Signature Verification Fails
```env
# Disable signature verification for local testing
# In src/app/api/webhooks/training-complete/route.ts
# Comment out the signature verification section
```

### CORS Issues
```javascript
// Add to next.config.js if needed
module.exports = {
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET,POST,PUT,DELETE' },
        ],
      },
    ]
  },
}
```

### Port Already in Use
```bash
# Use different port
npm run dev -- -p 3001
```

### Image Upload Issues
```bash
# Increase Node.js memory limit for large images
node --max_old_space_size=4096 node_modules/.bin/next dev
```

## Production vs. Local Differences

| Feature | Local | Production |
|---------|-------|------------|
| Persona Creation | ✅ Works | ✅ Works |
| Training Start | ✅ Works | ✅ Works |
| Webhook Updates | ❌ No real webhooks | ✅ Automatic |
| Status Polling | ✅ Manual/Polling | ✅ + Webhooks |
| Image Generation | ✅ Works | ✅ Works |
| Database | 🔄 Memory fallback | ✅ Persistent |

## Next Steps for Local Development

1. **Start with a simple persona** - Use clear, consistent face images
2. **Test the training flow** - Verify training starts successfully  
3. **Monitor on Replicate** - Check training progress on their dashboard
4. **Test generation** - Once complete, test image generation
5. **Deploy for webhooks** - Deploy to test full webhook integration

## Example Local Development Session

```bash
# 1. Setup
cp .env.example .env.local
# Add your REPLICATE_API_TOKEN

# 2. Start
npm install
npm run dev

# 3. Test
# - Create persona with 10+ images
# - Start training (note the training ID)
# - Check status manually or wait for polling
# - Generate images once complete
```

This setup gives you a fully functional local development environment for testing the persona training system, even without webhook integration! 