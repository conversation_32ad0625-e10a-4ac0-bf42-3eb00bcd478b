import { NextRequest, NextResponse } from 'next/server'
import { loadPersonas, findPersonaById } from '../../../utils/personaStorage'
import { createTrainingZip, uploadTrainingZip } from '../../../utils/loraTraining'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const personaId = searchParams.get('personaId')
    const step = searchParams.get('step') || 'env'

    const result: any = {
      timestamp: new Date().toISOString(),
      step: step,
      success: false
    }

    // Step 1: Environment check
    if (step === 'env' || step === 'all') {
      result.environment = {
        REPLICATE_API_TOKEN: !!process.env.REPLICATE_API_TOKEN,
        REPLICATE_USERNAME: !!process.env.REPLICATE_USERNAME,
        WEBHOOK_BASE_URL: !!process.env.WEBHOOK_BASE_URL,
        NEXT_PUBLIC_SUPABASE_URL: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
        NEXT_PUBLIC_SUPABASE_ANON_KEY: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
        REPLICATE_API_TOKEN_PREFIX: process.env.REPLICATE_API_TOKEN ? 
          process.env.REPLICATE_API_TOKEN.substring(0, 8) + '...' : 'NOT_SET',
        REPLICATE_USERNAME_VALUE: process.env.REPLICATE_USERNAME || 'NOT_SET'
      }
      
      const missingVars = []
      if (!process.env.REPLICATE_API_TOKEN) missingVars.push('REPLICATE_API_TOKEN')
      if (!process.env.REPLICATE_USERNAME) missingVars.push('REPLICATE_USERNAME')
      
      result.missingEnvironmentVars = missingVars
      result.environmentValid = missingVars.length === 0
    }

    // Step 2: Persona check
    if ((step === 'persona' || step === 'all') && personaId) {
      try {
        const persona = await findPersonaById(personaId)
        result.persona = {
          found: !!persona,
          id: persona?.id,
          name: persona?.name,
          hasLoraTraining: !!persona?.loraTraining,
          trainingStatus: persona?.loraTraining?.status,
          trainingImagesCount: persona?.loraTraining?.trainingImages?.length || 0,
          triggerWord: persona?.loraTraining?.triggerWord
        }
        
        if (persona?.loraTraining?.trainingImages) {
          result.persona.firstImageSize = persona.loraTraining.trainingImages[0]?.processedImageBase64?.length || 0
        }
      } catch (personaError) {
        result.persona = {
          error: personaError instanceof Error ? personaError.message : 'Unknown error'
        }
      }
    }

    // Step 3: ZIP creation test
    if ((step === 'zip' || step === 'all') && personaId) {
      try {
        const persona = await findPersonaById(personaId)
        if (persona?.loraTraining?.trainingImages) {
          console.log('🧪 Testing ZIP creation...')
          const zipBlob = await createTrainingZip(
            persona.loraTraining.trainingImages,
            persona.loraTraining.triggerWord
          )
          
          result.zipTest = {
            success: true,
            zipSize: zipBlob.size,
            zipSizeMB: (zipBlob.size / 1024 / 1024).toFixed(2)
          }
        } else {
          result.zipTest = {
            success: false,
            error: 'No persona or training images found'
          }
        }
      } catch (zipError) {
        result.zipTest = {
          success: false,
          error: zipError instanceof Error ? zipError.message : 'Unknown ZIP error'
        }
      }
    }

    // Step 4: Upload test (simplified)
    if (step === 'upload-test' && personaId) {
      try {
        const persona = await findPersonaById(personaId)
        if (persona?.loraTraining?.trainingImages) {
          console.log('🧪 Testing ZIP upload...')
          const zipBlob = await createTrainingZip(
            persona.loraTraining.trainingImages,
            persona.loraTraining.triggerWord
          )
          
          const zipUrl = await uploadTrainingZip(zipBlob, persona.name)
          
          result.uploadTest = {
            success: true,
            zipUrl: zipUrl,
            zipSize: zipBlob.size
          }
        } else {
          result.uploadTest = {
            success: false,
            error: 'No persona or training images found'
          }
        }
      } catch (uploadError) {
        result.uploadTest = {
          success: false,
          error: uploadError instanceof Error ? uploadError.message : 'Unknown upload error',
          stack: uploadError instanceof Error ? uploadError.stack : undefined
        }
      }
    }

    result.success = true
    return NextResponse.json(result)

  } catch (error) {
    console.error('❌ Debug endpoint error:', error)
    return NextResponse.json(
      { 
        error: 'Debug endpoint failed', 
        details: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    )
  }
} 