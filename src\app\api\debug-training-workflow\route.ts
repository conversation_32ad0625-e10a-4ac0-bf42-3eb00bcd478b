import { NextRequest, NextResponse } from 'next/server'
import { loadPersonas, findPersonaById } from '../../../utils/personaStorage'
import { createTrainingZip, uploadTrainingZip } from '../../../utils/loraTraining'

export async function POST(request: NextRequest) {
  try {
    const { personaId, step = 'all' } = await request.json()
    
    const results = {
      timestamp: new Date().toISOString(),
      step,
      personaId,
      results: {} as any
    }

    // Step 1: Environment Check
    if (step === 'env' || step === 'all') {
      results.results.environment = {
        replicate: {
          hasApiToken: !!process.env.REPLICATE_API_TOKEN,
          hasUsername: !!process.env.REPLICATE_USERNAME,
          tokenPrefix: process.env.REPLICATE_API_TOKEN?.substring(0, 8) + '...' || 'MISSING'
        },
        deployment: {
          platform: process.env.VERCEL ? 'Vercel' : 'Local',
          region: process.env.VERCEL_REGION || 'unknown'
        }
      }
    }

    // Step 2: Persona Loading
    if (step === 'persona' || step === 'all') {
      try {
        console.log('🔍 Loading all personas...')
        const allPersonas = await loadPersonas()
        
        results.results.personas = {
          total: allPersonas.length,
          withTraining: allPersonas.filter(p => p.loraTraining).length,
          list: allPersonas.map(p => ({
            id: p.id,
            name: p.name,
            hasTraining: !!p.loraTraining,
            status: p.loraTraining?.status,
            imageCount: p.loraTraining?.trainingImages?.length || 0
          }))
        }

        if (personaId) {
          console.log(`🔍 Looking for specific persona: ${personaId}`)
          const persona = await findPersonaById(personaId)
          
          results.results.targetPersona = {
            found: !!persona,
            id: persona?.id,
            name: persona?.name,
            hasLoraTraining: !!persona?.loraTraining,
            trainingStatus: persona?.loraTraining?.status,
            trainingImagesCount: persona?.loraTraining?.trainingImages?.length || 0,
            triggerWord: persona?.loraTraining?.triggerWord,
            firstImageSize: persona?.loraTraining?.trainingImages?.[0]?.processedImageBase64?.length || 0
          }
        }
      } catch (personaError) {
        results.results.personas = {
          error: personaError instanceof Error ? personaError.message : 'Unknown error'
        }
      }
    }

    // Step 3: ZIP Creation Test
    if ((step === 'zip' || step === 'all') && personaId) {
      try {
        const persona = await findPersonaById(personaId)
        if (persona?.loraTraining?.trainingImages) {
          console.log('🧪 Testing ZIP creation...')
          const zipBlob = await createTrainingZip(
            persona.loraTraining.trainingImages,
            persona.loraTraining.triggerWord
          )
          
          results.results.zipTest = {
            success: true,
            zipSize: zipBlob.size,
            zipSizeMB: (zipBlob.size / 1024 / 1024).toFixed(2),
            imageCount: persona.loraTraining.trainingImages.length
          }
        } else {
          results.results.zipTest = {
            success: false,
            error: 'No persona or training images found'
          }
        }
      } catch (zipError) {
        results.results.zipTest = {
          success: false,
          error: zipError instanceof Error ? zipError.message : 'Unknown ZIP error'
        }
      }
    }

    // Step 4: Upload Test (be careful - this actually uploads)
    if (step === 'upload-test' && personaId) {
      try {
        const persona = await findPersonaById(personaId)
        if (persona?.loraTraining?.trainingImages) {
          console.log('🧪 Testing ZIP upload...')
          const zipBlob = await createTrainingZip(
            persona.loraTraining.trainingImages,
            persona.loraTraining.triggerWord
          )
          
          const zipUrl = await uploadTrainingZip(zipBlob, persona.name)
          
          results.results.uploadTest = {
            success: true,
            zipUrl: zipUrl,
            zipSize: zipBlob.size
          }
        } else {
          results.results.uploadTest = {
            success: false,
            error: 'No persona or training images found'
          }
        }
      } catch (uploadError) {
        results.results.uploadTest = {
          success: false,
          error: uploadError instanceof Error ? uploadError.message : 'Unknown upload error'
        }
      }
    }

    return NextResponse.json({
      success: true,
      ...results
    })

  } catch (error) {
    console.error('❌ Debug workflow error:', error)
    return NextResponse.json(
      { 
        error: 'Debug workflow failed', 
        details: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Training workflow debug endpoint',
    usage: {
      POST: 'Debug training workflow',
      parameters: {
        personaId: 'string (optional) - ID of persona to test',
        step: 'string (optional) - specific step to test: env, persona, zip, upload-test, or all'
      },
      examples: [
        'POST /api/debug-training-workflow { "step": "env" }',
        'POST /api/debug-training-workflow { "personaId": "persona_123", "step": "all" }'
      ]
    }
  })
} 