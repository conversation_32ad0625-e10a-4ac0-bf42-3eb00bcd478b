#!/usr/bin/env node

/**
 * Local Development Helper for Persona Training
 * 
 * This script helps with local development by:
 * - Checking training status for all pending personas
 * - Manually updating persona status when training completes
 * - Simulating webhook calls for testing
 */

const readline = require('readline');
const fetch = require('node-fetch');

const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function checkTrainingStatus() {
  try {
    console.log('🔍 Checking training status for all personas...\n');
    
    // Get all personas
    const response = await fetch(`${BASE_URL}/api/personas`);
    const data = await response.json();
    
    if (!data.success) {
      console.error('❌ Failed to fetch personas:', data.error);
      return;
    }
    
    const trainingPersonas = data.personas.filter(p => 
      p.loraTraining && p.loraTraining.status === 'training'
    );
    
    if (trainingPersonas.length === 0) {
      console.log('ℹ️  No personas currently training.');
      return;
    }
    
    console.log(`Found ${trainingPersonas.length} personas training:\n`);
    
    for (const persona of trainingPersonas) {
      const trainingId = persona.loraTraining.trainingId;
      console.log(`👤 ${persona.name} (${persona.id})`);
      console.log(`🔗 Training ID: ${trainingId}`);
      
      if (trainingId) {
        try {
          const statusResponse = await fetch(
            `${BASE_URL}/api/train-lora-direct?trainingId=${trainingId}`
          );
          const statusData = await statusResponse.json();
          
          if (statusData.success) {
            console.log(`📊 Status: ${statusData.status}`);
            console.log(`📈 Progress: ${statusData.progress || 0}%`);
            if (statusData.modelUrl) {
              console.log(`🎯 Model URL: ${statusData.modelUrl}`);
            }
            if (statusData.error) {
              console.log(`❌ Error: ${statusData.error}`);
            }
          } else {
            console.log(`❌ Failed to check status: ${statusData.error}`);
          }
        } catch (error) {
          console.log(`❌ Error checking status: ${error.message}`);
        }
      }
      
      console.log(''); // Empty line
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

async function simulateWebhook() {
  try {
    const trainingId = await question('Enter training ID: ');
    const status = await question('Enter status (succeeded/failed/processing): ');
    
    let destination = '';
    if (status === 'succeeded') {
      destination = await question('Enter model destination (e.g., username/model-name): ');
    }
    
    console.log('\n🔔 Simulating webhook call...');
    
    const webhookPayload = {
      id: trainingId,
      status: status,
      type: 'training',
      progress: status === 'succeeded' ? 1.0 : 0.5,
      destination: destination || undefined
    };
    
    const response = await fetch(`${BASE_URL}/api/webhooks/training-complete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'replicate-signature': 'sha256=test-signature-for-local-dev'
      },
      body: JSON.stringify(webhookPayload)
    });
    
    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Webhook simulation successful!');
      console.log('📋 Response:', result);
    } else {
      console.log('❌ Webhook simulation failed:');
      console.log('📋 Response:', result);
    }
  } catch (error) {
    console.error('❌ Error simulating webhook:', error.message);
  }
}

async function listPersonas() {
  try {
    console.log('📋 Fetching all personas...\n');
    
    const response = await fetch(`${BASE_URL}/api/personas`);
    const data = await response.json();
    
    if (!data.success) {
      console.error('❌ Failed to fetch personas:', data.error);
      return;
    }
    
    if (data.personas.length === 0) {
      console.log('ℹ️  No personas found.');
      return;
    }
    
    console.log(`Found ${data.personas.length} personas:\n`);
    
    data.personas.forEach((persona, index) => {
      console.log(`${index + 1}. 👤 ${persona.name} (${persona.id})`);
      console.log(`   📝 Description: ${persona.description || 'None'}`);
      console.log(`   📊 Status: ${persona.loraTraining?.status || 'No training data'}`);
      console.log(`   🎯 Trigger: ${persona.loraTraining?.triggerWord || 'None'}`);
      console.log(`   📈 Progress: ${persona.loraTraining?.trainingProgress || 0}%`);
      console.log(`   🔗 Model: ${persona.loraTraining?.modelUrl || 'None'}`);
      console.log('');
    });
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

async function manualStatusUpdate() {
  try {
    await listPersonas();
    
    const personaId = await question('Enter persona ID to update: ');
    const status = await question('Enter new status (pending/training/completed/failed): ');
    
    let modelUrl = '';
    if (status === 'completed') {
      modelUrl = await question('Enter model URL: ');
    }
    
    console.log('\n🔄 Updating persona status...');
    
    // This would require a custom update endpoint or direct database access
    console.log('ℹ️  Manual status update requires direct database access.');
    console.log('📝 You can update the persona manually in your database with:');
    console.log(`   Persona ID: ${personaId}`);
    console.log(`   Status: ${status}`);
    if (modelUrl) {
      console.log(`   Model URL: ${modelUrl}`);
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

async function main() {
  console.log('🚀 Thumbnex Local Development Helper\n');
  
  while (true) {
    console.log('Choose an option:');
    console.log('1. Check training status');
    console.log('2. List all personas');
    console.log('3. Simulate webhook call');
    console.log('4. Manual status update info');
    console.log('5. Exit');
    
    const choice = await question('\nEnter your choice (1-5): ');
    
    console.log(''); // Empty line
    
    switch (choice) {
      case '1':
        await checkTrainingStatus();
        break;
      case '2':
        await listPersonas();
        break;
      case '3':
        await simulateWebhook();
        break;
      case '4':
        await manualStatusUpdate();
        break;
      case '5':
        console.log('👋 Goodbye!');
        rl.close();
        return;
      default:
        console.log('❌ Invalid choice. Please try again.');
    }
    
    console.log('\n' + '='.repeat(50) + '\n');
  }
}

// Handle Ctrl+C
process.on('SIGINT', () => {
  console.log('\n👋 Goodbye!');
  rl.close();
  process.exit(0);
});

if (require.main === module) {
  main().catch(console.error);
} 