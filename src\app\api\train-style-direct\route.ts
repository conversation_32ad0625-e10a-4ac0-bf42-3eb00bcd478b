import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    console.log('🎨 POST /api/train-style-direct called - Direct style LoRA training')
    
    const body = await request.json()
    console.log('🔍 Received request body:', JSON.stringify(body, null, 2))
    
    const { styleName, triggerWord, zipUrl: inputZipUrl, category, description } = body
    
    console.log('🔍 Extracted fields:', {
      styleName,
      triggerWord, 
      inputZipUrl,
      category,
      description
    })

    // Validate only essential fields
    if (!styleName) {
      return NextResponse.json(
        { error: 'Style name is required' },
        { status: 400 }
      )
    }

    if (!triggerWord) {
      return NextResponse.json(
        { error: 'Trigger word is required' },
        { status: 400 }
      )
    }

    if (!inputZipUrl) {
      return NextResponse.json(
        { error: 'ZIP URL is required (images must be uploaded first)' },
        { status: 400 }
      )
    }

    console.log(`🚀 Starting style LoRA training for: ${styleName}`)
    console.log(`🎯 Trigger word: ${triggerWord}`)
    console.log(`✅ Using pre-uploaded ZIP: ${inputZipUrl}`)

    // Validate Replicate environment
    if (!process.env.REPLICATE_API_TOKEN) {
      return NextResponse.json({
        error: 'REPLICATE_API_TOKEN not configured'
      }, { status: 500 })
    }

    if (!process.env.REPLICATE_USERNAME) {
      return NextResponse.json({
        error: 'REPLICATE_USERNAME not configured'
      }, { status: 500 })
    }

    // Step 1: Create a destination model (same as persona training)
    const modelName = `style-model-${Date.now()}`
    const destination = `${process.env.REPLICATE_USERNAME}/${modelName}`
    
    console.log('📦 Creating destination model:', destination)
    
    const createModelResponse = await fetch('https://api.replicate.com/v1/models', {
      method: 'POST',
      headers: {
        'Authorization': `Token ${process.env.REPLICATE_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        owner: process.env.REPLICATE_USERNAME,
        name: modelName,
        description: `FLUX LoRA style trained with trigger word: ${triggerWord}`,
        visibility: 'private',
        hardware: 'gpu-a100-large'
      })
    })
    
    if (!createModelResponse.ok) {
      const createError = await createModelResponse.text()
      console.error('❌ Failed to create model:', createError)
      return NextResponse.json({
        error: 'Failed to create destination model',
        details: createError
      }, { status: 500 })
    }
    
    const createdModel = await createModelResponse.json()
    console.log('✅ Created model:', createdModel.url)

    // Step 2: Start training with webhook (same pattern as persona training)
    console.log('🚀 Starting style training with webhook...')
    
    // Construct webhook URL - use the actual deployment URL
    const baseUrl = request.url.includes('localhost') 
      ? 'https://thumbnex-ejan.vercel.app'  // Always use production URL for webhooks
      : request.url.split('/api')[0]  // Extract base URL from current request
    
    const webhookUrl = `${baseUrl}/api/webhooks/training-complete`
    console.log('🔗 Webhook URL:', webhookUrl)
    
    const replicateResponse = await fetch('https://api.replicate.com/v1/models/replicate/fast-flux-trainer/versions/8b10794665aed907bb98a1a5324cd1d3a8bea0e9b31e65210967fb9c9e2e08ed/trainings', {
      method: 'POST',
      headers: {
        'Authorization': `Token ${process.env.REPLICATE_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        destination: destination,
        webhook: webhookUrl,
        webhook_events_filter: ["completed"],  // Only notify when training is complete
        input: {
          input_images: inputZipUrl,
          trigger_word: triggerWord.toUpperCase(),
          lora_type: 'style',  // Key difference: 'style' instead of 'subject'
          steps: 1000,
          lora_rank: 16,
          optimizer: 'adamw8bit',
          batch_size: 1,
          resolution: '512,768,1024',
          autocaption: true,
          trigger_word_weight: 1.0,
          learning_rate: 0.0004
        }
      })
    })

    if (!replicateResponse.ok) {
      const errorData = await replicateResponse.text()
      console.error('❌ Replicate API error details:', {
        status: replicateResponse.status,
        statusText: replicateResponse.statusText,
        errorData: errorData,
        headers: Object.fromEntries(replicateResponse.headers.entries())
      })
      
      return NextResponse.json(
        {
          error: 'Replicate API error',
          details: `Status: ${replicateResponse.status} - ${errorData}`,
          replicateStatus: replicateResponse.status,
          replicateError: errorData,
          timestamp: new Date().toISOString()
        },
        { status: 500 }
      )
    }

    const result = await replicateResponse.json()
    console.log('✅ Style training started successfully with webhook:', result.id)

    return NextResponse.json({
      success: true,
      trainingId: result.id,
      status: result.status,
      message: 'Style training started successfully with webhook notification',
      styleName: styleName,
      triggerWord: triggerWord.toUpperCase(),
      estimatedTime: '10-15 minutes',
      estimatedCost: '$6-8 USD',
      modelUrl: createdModel.url,
      destination: destination,
      webhook: {
        url: webhookUrl,
        configured: true,
        events: ["completed"]
      },
      loraType: 'style'
    })

  } catch (error) {
    console.error('❌ Style training error:', error)
    
    return NextResponse.json(
      {
        error: 'Failed to start style training',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
} 