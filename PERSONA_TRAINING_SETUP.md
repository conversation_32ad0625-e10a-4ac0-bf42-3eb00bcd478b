# Persona Training Setup Guide

This guide will help you set up the complete persona training and generation flow in Thumbnex.

## Environment Variables

Create a `.env.local` file in your project root with these variables:

```env
# =============================================
# REPLICATE API CONFIGURATION (Required)
# =============================================
REPLICATE_API_TOKEN=your_replicate_api_token_here
REPLICATE_USERNAME=your_replicate_username_here

# =============================================
# WEBHOOK CONFIGURATION (Required for Production)
# =============================================
# Your deployed app's base URL (for webhook callbacks)
WEBHOOK_BASE_URL=https://your-app-domain.com

# Secret for webhook signature verification
REPLICATE_WEBHOOK_SECRET=your_webhook_secret_here

# =============================================
# SUPABASE CONFIGURATION (Optional - for persistent storage)
# =============================================
# If not provided, app will use in-memory storage (local dev only)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here
```

## Setup Steps

### 1. Replicate Configuration

1. **Get your Replicate API token**: 
   - Go to [replicate.com/account/api-tokens](https://replicate.com/account/api-tokens)
   - Create a new token and set it as `REPLICATE_API_TOKEN`

2. **Set your Replicate username**:
   - Set `REPLICATE_USERNAME` to your Replicate username
   - This is where your trained models will be stored

### 2. Webhook Setup

1. **Deploy your app** to get a public URL (Vercel, Netlify, etc.)

2. **Set webhook URL**:
   - Set `WEBHOOK_BASE_URL` to your deployed app URL (e.g., `https://your-app.vercel.app`)
   - The webhook endpoint will be: `https://your-app.vercel.app/api/webhooks/training-complete`

3. **Generate webhook secret**:
   - Generate a random string for `REPLICATE_WEBHOOK_SECRET`
   - You can use: `openssl rand -hex 32`

### 3. Database Setup (Optional)

If using Supabase for persona storage:

1. **Create Supabase project**: [supabase.com](https://supabase.com)

2. **Run database schema**:
   ```sql
   -- Copy and run the SQL from database-schema.sql
   ```

3. **Get Supabase credentials**:
   - Project URL → `NEXT_PUBLIC_SUPABASE_URL`
   - API Keys → Anon key → `NEXT_PUBLIC_SUPABASE_ANON_KEY`
   - API Keys → Service role key → `SUPABASE_SERVICE_ROLE_KEY`

## How It Works

### 1. Persona Creation
- User uploads 10+ images
- Images are processed and resized to 1024x1024
- Persona is created with `loraTraining.status = 'pending'`

### 2. Training Initiation
- User clicks "Train" button on a pending persona
- System creates ZIP file from training images
- Uploads ZIP to Replicate
- Starts LoRA training with webhook URL
- Persona status changes to `'training'`

### 3. Training Monitoring
- Frontend automatically polls for training status updates
- Webhook receives updates from Replicate when training completes
- Persona status changes to `'completed'` with `modelUrl` set

### 4. Image Generation
- When generating with a trained persona:
  - System checks if `loraTraining.status === 'completed'` and `modelUrl` exists
  - Uses the custom LoRA model for generation
  - Falls back to base FLUX if no LoRA available

## Testing the Flow

1. **Create a persona**:
   - Upload 10+ clear face images
   - Choose a unique trigger word
   - Submit the form

2. **Start training**:
   - Find the persona in the selector
   - Click the "Train" button
   - Wait for training to start

3. **Monitor progress**:
   - Training status shows in persona selector
   - Progress updates automatically every 10 seconds
   - Training typically takes 15-20 minutes

4. **Generate images**:
   - Once training is complete, select the persona
   - Enter a prompt including the trigger word
   - Generate thumbnail

## Troubleshooting

### Training Fails to Start
- Check `REPLICATE_API_TOKEN` is valid
- Ensure `REPLICATE_USERNAME` is correct
- Verify you have at least 10 training images

### Webhook Not Working
- Check `WEBHOOK_BASE_URL` is accessible
- Verify `REPLICATE_WEBHOOK_SECRET` matches between app and Replicate
- Check webhook logs in your deployment platform

### Generation Not Using LoRA
- Confirm persona has `loraTraining.status === 'completed'`
- Check `loraTraining.modelUrl` is set
- Include trigger word in your prompt
- Check console logs for error messages

### Persona Not Found
- Ensure database connection is working
- Check persona storage (Supabase or local)
- Try refreshing the persona list

## Cost Considerations

- **Training cost**: ~$2-4 USD per persona (15-20 minutes on A40 GPU)
- **Generation cost**: ~$0.003-0.012 per image (depending on settings)
- **Storage**: Minimal cost for ZIP files and model storage

## Performance Tips

1. **Image quality**: Use high-quality, well-lit face images
2. **Variety**: Include different expressions and backgrounds
3. **Trigger words**: Use unique, memorable trigger words
4. **Prompt engineering**: Include trigger word and descriptive prompts

## Next Steps

- Test the complete flow with a sample persona
- Monitor webhook logs to ensure proper updates
- Experiment with different trigger words and prompts
- Scale up with multiple personas for different content types 