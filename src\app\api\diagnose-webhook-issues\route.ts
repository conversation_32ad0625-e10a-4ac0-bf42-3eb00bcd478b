import { NextRequest, NextResponse } from 'next/server'

/**
 * Comprehensive Webhook Diagnostics
 * Tests all potential webhook failure points
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const trainingId = searchParams.get('trainingId')
    
    const baseUrl = request.url.includes('localhost') 
      ? 'https://thumbnex-ejan.vercel.app'
      : request.url.split('/api')[0]
    
    const results: any = {
      timestamp: new Date().toISOString(),
      diagnostics: {},
      recommendations: [],
      webhook: {
        url: `${baseUrl}/api/webhooks/training-complete`,
        testUrl: `${baseUrl}/api/test-webhook-direct`
      }
    }
    
    // Test 1: Environment Variables
    console.log('🔍 Testing environment variables...')
    const envTest = {
      hasReplicateApiToken: !!process.env.REPLICATE_API_TOKEN,
      hasWebhookSecret: !!process.env.REPLICATE_WEBHOOK_SECRET,
      hasReplicateUsername: !!process.env.REPLICATE_USERNAME,
      webhookSecretLength: process.env.REPLICATE_WEBHOOK_SECRET?.length || 0,
      webhookSecretPreview: process.env.REPLICATE_WEBHOOK_SECRET?.substring(0, 10) + '...' || 'none'
    }
    results.diagnostics.environment = envTest
    
    if (!envTest.hasWebhookSecret) {
      results.recommendations.push('❌ REPLICATE_WEBHOOK_SECRET is not set - this will cause all webhooks to fail')
    } else if (envTest.webhookSecretLength < 20) {
      results.recommendations.push('⚠️ REPLICATE_WEBHOOK_SECRET seems too short - verify it matches Replicate configuration')
    }
    
    // Test 2: Webhook URL Accessibility
    console.log('🌐 Testing webhook URL accessibility...')
    try {
      const webhookTestResponse = await fetch(`${baseUrl}/api/test-webhook-direct`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ test: 'accessibility' })
      })
      
      results.diagnostics.webhookAccessibility = {
        status: webhookTestResponse.status,
        accessible: webhookTestResponse.ok,
        error: webhookTestResponse.ok ? null : await webhookTestResponse.text()
      }
      
      if (!webhookTestResponse.ok) {
        results.recommendations.push('❌ Webhook URL is not accessible - Replicate cannot reach your webhook endpoint')
      }
    } catch (error) {
      results.diagnostics.webhookAccessibility = {
        status: 0,
        accessible: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
      results.recommendations.push('❌ Webhook URL test failed - network or deployment issue')
    }
    
    // Test 3: Database Connection
    console.log('🗄️ Testing database connection...')
    try {
      const { supabase } = require('../../../lib/supabase')
      if (supabase) {
        const { data, error } = await supabase
          .from('webhook_events')
          .select('id')
          .limit(1)
        
        results.diagnostics.database = {
          connected: !error,
          error: error?.message || null
        }
        
        if (error) {
          results.recommendations.push('❌ Database connection failed - webhook events cannot be stored')
        }
      } else {
        results.diagnostics.database = {
          connected: false,
          error: 'Supabase client not initialized'
        }
        results.recommendations.push('❌ Supabase client not initialized - check environment variables')
      }
    } catch (error) {
      results.diagnostics.database = {
        connected: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
    
    // Test 4: Recent Training Status (if trainingId provided)
    if (trainingId) {
      console.log(`🚂 Testing training status for: ${trainingId}`)
      try {
        const statusResponse = await fetch(`${baseUrl}/api/debug-training-status?trainingId=${trainingId}`)
        if (statusResponse.ok) {
          const statusData = await statusResponse.json()
          results.diagnostics.trainingStatus = {
            found: statusData.success,
            status: statusData.training?.status,
            hasDestination: !!statusData.training?.destination,
            hasOutput: !!statusData.training?.output
          }
          
          if (statusData.training?.status === 'succeeded' && !statusData.training?.destination) {
            results.recommendations.push('⚠️ Training succeeded but no destination URL found - may be a model structure issue')
          }
        } else {
          results.diagnostics.trainingStatus = {
            found: false,
            error: await statusResponse.text()
          }
        }
      } catch (error) {
        results.diagnostics.trainingStatus = {
          found: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }
      
      // Check if training job exists in database
      try {
        const { supabase } = require('../../../lib/supabase')
        if (supabase) {
          const { data, error } = await supabase
            .from('training_jobs')
            .select('*')
            .eq('replicate_training_id', trainingId)
            .single()
          
          results.diagnostics.trainingJob = {
            found: !error && !!data,
            status: data?.status,
            personaId: data?.persona_id,
            error: error?.message || null
          }
          
          if (error) {
            results.recommendations.push('❌ Training job not found in database - webhook cannot update persona')
          }
        }
      } catch (error) {
        results.diagnostics.trainingJob = {
          found: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }
    }
    
    // Test 5: Recent Webhook Attempts
    console.log('📋 Checking recent webhook attempts...')
    try {
      const { supabase } = require('../../../lib/supabase')
      if (supabase) {
        const { data, error } = await supabase
          .from('webhook_events')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(5)
        
        results.diagnostics.recentWebhooks = {
          count: data?.length || 0,
          events: data?.map((event: any) => ({
            id: event.id,
            eventType: event.event_type,
            processed: event.processed,
            error: event.processing_error,
            createdAt: event.created_at
          })) || []
        }
        
        if (data && data.length === 0) {
          results.recommendations.push('⚠️ No webhook events received recently - Replicate may not be sending webhooks')
        }
      }
    } catch (error) {
      results.diagnostics.recentWebhooks = {
        count: 0,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
    
    // Generate overall health score
    const issues = results.recommendations.filter((r: string) => r.startsWith('❌')).length
    const warnings = results.recommendations.filter((r: string) => r.startsWith('⚠️')).length
    
    results.health = {
      score: Math.max(0, 100 - (issues * 30) - (warnings * 10)),
      status: issues === 0 ? (warnings === 0 ? 'healthy' : 'warning') : 'error',
      issues: issues,
      warnings: warnings
    }
    
    // Add specific recommendations
    if (results.health.score < 50) {
      results.recommendations.unshift('🚨 Critical webhook issues detected - immediate attention required')
    } else if (results.health.score < 80) {
      results.recommendations.unshift('⚠️ Webhook issues detected - should be addressed')
    } else {
      results.recommendations.unshift('✅ Webhook system appears healthy')
    }
    
    return NextResponse.json(results)
    
  } catch (error) {
    console.error('❌ Diagnostic error:', error)
    return NextResponse.json({
      error: 'Diagnostic failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function POST() {
  return NextResponse.json({
    message: 'Webhook Diagnostics',
    description: 'Comprehensive webhook failure analysis',
    usage: 'GET /api/diagnose-webhook-issues?trainingId=optional',
    note: 'Provides detailed analysis of webhook configuration and potential issues'
  })
} 