import { NextRequest, NextResponse } from 'next/server'
import { put } from '@vercel/blob'

export async function POST(request: NextRequest) {
  try {
    console.log('📤 POST /api/upload-training-zip called')
    
    const formData = await request.formData()
    const file = formData.get('file') as File
    
    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }
    
    console.log(`📦 Processing ZIP upload: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)`)
    
    // Validate file type
    if (!file.name.endsWith('.zip')) {
      return NextResponse.json(
        { error: 'Only ZIP files are allowed' },
        { status: 400 }
      )
    }
    
    // Validate file size (max 100MB)
    const maxSize = 100 * 1024 * 1024 // 100MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: `File too large. Maximum size is ${maxSize / 1024 / 1024}MB` },
        { status: 400 }
      )
    }
    
    try {
      // Upload to Vercel Blob as a single ZIP file (not extracted)
      console.log('☁️ Uploading ZIP file to Vercel Blob as single file...')
      console.log(`📋 File details:`)
      console.log(`  - Name: ${file.name}`)
      console.log(`  - Type: ${file.type}`)
      console.log(`  - Size: ${file.size} bytes (${(file.size / 1024 / 1024).toFixed(2)}MB)`)
      console.log(`  - Expected in Vercel dashboard as: ${file.name}`)
      
      const blob = await put(file.name, file, {
        access: 'public',
        // Ensure it's stored as a ZIP file, not extracted
        contentType: 'application/zip'
      })
      
      console.log('✅ ZIP uploaded to Vercel Blob successfully as single file!')
      console.log(`📍 Blob details:`)
      console.log(`  - URL: ${blob.url}`)
      console.log(`  - Filename in dashboard: ${file.name}`)
      console.log(`  - Size: ${file.size} bytes`)
      console.log(`  - Should appear in Vercel Blob dashboard as: ${file.name}`)
      
      // Verify the URL structure
      const urlParts = blob.url.split('/')
      const actualFilename = urlParts[urlParts.length - 1]
      console.log(`🔍 URL analysis:`)
      console.log(`  - Full URL: ${blob.url}`)
      console.log(`  - Extracted filename: ${actualFilename}`)
      console.log(`  - Ends with .zip: ${blob.url.endsWith('.zip')}`)
      console.log(`  - Contains .zip: ${blob.url.includes('.zip')}`)
      
      if (!blob.url.includes('.zip')) {
        console.warn('⚠️ Warning: Blob URL does not contain .zip:', blob.url)
      }
      
      return NextResponse.json({
        success: true,
        url: blob.url,
        filename: file.name,
        size: file.size,
        contentType: 'application/zip',
        message: 'ZIP file uploaded successfully to Vercel Blob as single file (not extracted)'
      })
      
    } catch (blobError) {
      console.error('❌ Vercel Blob upload failed:', blobError)
      return NextResponse.json(
        { 
          error: 'Failed to upload to Vercel Blob',
          details: blobError instanceof Error ? blobError.message : 'Unknown blob error'
        },
        { status: 500 }
      )
    }
    
  } catch (error) {
    console.error('❌ Error uploading ZIP:', error)
    return NextResponse.json(
      { 
        error: 'Failed to upload ZIP file',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// GET endpoint to test the upload endpoint
export async function GET() {
  return NextResponse.json({
    success: true,
    message: 'Upload training ZIP endpoint is ready',
    endpoint: '/api/upload-training-zip',
    method: 'POST',
    contentType: 'multipart/form-data',
    field: 'file',
    maxSize: '100MB',
    allowedTypes: ['.zip']
  })
} 