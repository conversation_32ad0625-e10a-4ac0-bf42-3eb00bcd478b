'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Palette, Eye, Trash2, Upload, Sparkles, Circle, Disc } from 'lucide-react'
import Image from 'next/image'

interface InspirationSectionProps {
  selectedInspiration: string | null
  onInspirationChange: (inspiration: string | null) => void
}

// Mock inspiration data
const inspirations = [
  {
    id: 'style-1',
    name: 'Gaming Red/Blue',
    image: '/inspirations/gaming-red-blue.jpg',
    tags: ['gaming', 'red', 'blue', 'neon']
  },
  {
    id: 'style-2',
    name: 'Tech Minimal',
    image: '/inspirations/tech-minimal.jpg', 
    tags: ['tech', 'minimal', 'clean', 'white']
  },
  {
    id: 'style-3',
    name: 'Food Warm',
    image: '/inspirations/food-warm.jpg',
    tags: ['food', 'warm', 'cozy', 'orange']
  }
]

const strengthOptions = [
  { value: 'low', icon: Circle, label: 'Subtle', description: 'Subtle style influence while maintaining original concept' },
  { value: 'medium', icon: Disc, label: 'Balanced', description: 'Balanced blend of style and original concept' },
  { value: 'high', icon: Disc, label: 'Strong', description: 'Strong style influence, major visual changes' }
]

export function InspirationSection({ selectedInspiration, onInspirationChange }: InspirationSectionProps) {
  const [activeTab, setActiveTab] = useState<'library' | 'trending' | 'upload'>('library')
  const [styleStrength, setStyleStrength] = useState<'low' | 'medium' | 'high'>('low')

  return (
    <section className="rounded-lg border border-border-primary bg-bg-secondary p-6">
      {/* Section Header */}
      <div className="mb-4 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Palette className="h-5 w-5 text-white" />
          <h3 className="text-lg font-semibold text-text-primary">
            Style Inspiration
          </h3>
        </div>
        
        {/* Tabs */}
        <div className="flex rounded-lg bg-bg-tertiary p-1">
          {[
            { key: 'library', label: 'My Library' },
            { key: 'trending', label: 'Trending' },
            { key: 'upload', label: 'Upload' }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`rounded-md px-3 py-1 text-xs font-medium transition-all duration-200 border ${
                activeTab === tab.key
                  ? 'bg-accent-primary border-accent-primary text-white shadow-[0_0_10px_rgba(251,86,7,0.3)]'
                  : 'bg-bg-secondary border-white/20 text-text-secondary hover:text-text-primary hover:border-accent-primary hover:bg-bg-tertiary'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Inspiration Grid */}
      <div className="mb-4 grid grid-cols-2 gap-3">
        {/* No Inspiration Option */}
        <button
          onClick={() => onInspirationChange(null)}
          className={`flex flex-col items-center space-y-2 rounded-lg border-2 p-3 transition-colors ${
            selectedInspiration === null
              ? 'border-accent-primary bg-accent-primary/10'
              : 'border-border-primary bg-bg-tertiary hover:border-border-secondary'
          }`}
        >
          <div className="flex h-20 w-full items-center justify-center rounded-lg bg-bg-primary">
            <div className="text-center">
              <Sparkles className="mx-auto h-6 w-6 text-text-tertiary" />
              <span className="mt-1 text-xs text-text-tertiary">Original Style</span>
            </div>
          </div>
          <span className="text-xs font-medium text-text-primary">No Inspiration</span>
        </button>

        {/* Inspiration Options */}
        {inspirations.map((inspiration) => (
          <button
            key={inspiration.id}
            onClick={() => onInspirationChange(inspiration.id)}
            className={`group relative flex flex-col items-center space-y-2 rounded-lg border-2 p-3 transition-colors ${
              selectedInspiration === inspiration.id
                ? 'border-accent-primary bg-accent-primary/10'
                : 'border-border-primary bg-bg-tertiary hover:border-border-secondary'
            }`}
          >
            <div className="relative h-20 w-full overflow-hidden rounded-lg">
              <Image
                src={inspiration.image}
                alt={inspiration.name}
                fill
                className="object-cover"
                onError={(e) => {
                  // Fallback for missing images
                  const target = e.target as HTMLImageElement
                  target.style.display = 'none'
                  const parent = target.parentElement
                  if (parent) {
                    parent.innerHTML = `<div class="flex h-full w-full items-center justify-center bg-bg-primary"><Palette class="h-6 w-6 text-text-tertiary" /></div>`
                  }
                }}
              />
              
              {/* Hover Overlay */}
              <div className="absolute inset-0 flex items-center justify-center space-x-1 bg-black/50 opacity-0 transition-opacity group-hover:opacity-100">
                <div className="rounded bg-white/20 p-1 cursor-pointer hover:bg-white/30 transition-colors">
                  <Eye className="h-3 w-3 text-white" />
                </div>
                <div className="rounded bg-white/20 p-1 cursor-pointer hover:bg-white/30 transition-colors">
                  <Trash2 className="h-3 w-3 text-white" />
                </div>
              </div>
            </div>
            
            <div className="w-full text-center">
              <span className="text-xs font-medium text-text-primary">{inspiration.name}</span>
              <div className="mt-1 flex flex-wrap justify-center gap-1">
                {inspiration.tags.map((tag) => (
                  <span
                    key={tag}
                    className="rounded bg-bg-primary px-1 py-0.5 text-xs text-text-tertiary"
                  >
                    #{tag}
                  </span>
                ))}
              </div>
            </div>
          </button>
        ))}

        {/* Add Inspiration Card */}
        <button className="flex flex-col items-center justify-center space-y-2 rounded-lg border-2 border-dashed border-border-primary bg-bg-tertiary p-3 transition-colors hover:border-border-secondary hover:bg-bg-tertiary/80">
          <div className="flex h-20 w-full items-center justify-center rounded-lg bg-bg-primary">
            <div className="text-center">
              <Upload className="mx-auto h-6 w-6 text-text-tertiary" />
              <span className="mt-1 text-xs text-text-tertiary">Add Style</span>
            </div>
          </div>
          <input type="file" accept="image/*" className="hidden" />
        </button>
      </div>

      {/* Style Strength Control */}
      {selectedInspiration && (
        <div className="space-y-3 rounded-lg bg-bg-tertiary p-4">
          <h4 className="text-sm font-medium text-text-primary">Style Influence</h4>
          
          <div className="grid grid-cols-3 gap-2">
            {strengthOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => setStyleStrength(option.value as any)}
                className={`flex flex-col items-center space-y-1 rounded-lg border-2 p-3 transition-colors ${
                  styleStrength === option.value
                    ? 'border-accent-primary bg-accent-primary/10'
                    : 'border-border-primary hover:border-border-secondary'
                }`}
              >
                <option.icon 
                  className={`h-5 w-5 ${
                    styleStrength === option.value ? 'text-accent-primary' : 'text-text-tertiary'
                  }`}
                  fill={option.value === 'high' ? 'currentColor' : 'none'}
                />
                <span className="text-xs font-medium text-text-primary">{option.label}</span>
              </button>
            ))}
          </div>
          
          <p className="text-xs text-text-secondary">
            {strengthOptions.find(opt => opt.value === styleStrength)?.description}
          </p>
        </div>
      )}
    </section>
  )
} 