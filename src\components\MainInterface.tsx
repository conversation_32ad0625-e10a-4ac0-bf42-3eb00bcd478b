'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from './ui/Button'
import { ThumbnailGenerator } from './ThumbnailGenerator'
import { ClientSideTraining } from './ClientSideTraining'
import { 
  Stars,
  User,
  Users,
  Palette,
  PanelLeftOpen,
  PanelLeftClose,
  Plus,
  MessageSquare,
  Edit2,
  Check,
  X,
  BarChart3,
  Menu
} from 'lucide-react'

export function MainInterface() {
  const [sidebarOpen, setSidebarOpen] = useState(false) // Default closed on all devices
  const [showTrainingModal, setShowTrainingModal] = useState(false)
  const [trainingType, setTrainingType] = useState<'persona' | 'style'>('persona')
  const [editingHistoryId, setEditingHistoryId] = useState<string | null>(null)
  const [historyItems, setHistoryItems] = useState([
    { id: '1', title: 'Gaming setup with RGB lighting...' },
    { id: '2', title: 'Earth cracked in space...' },
    { id: '3', title: 'Futuristic city skyline...' },
    { id: '4', title: 'Mountain landscape sunset...' }
  ])
  const [editingTitle, setEditingTitle] = useState('')

  // Close sidebar when clicking outside on mobile and iPad
  useEffect(() => {
    if (!sidebarOpen) return

    const handleClickOutside = (e: MouseEvent) => {
      const sidebar = document.getElementById('sidebar')
      const toggle = document.getElementById('sidebar-toggle')

      if (window.innerWidth < 1200 &&
          sidebar && !sidebar.contains(e.target as Node) &&
          toggle && !toggle.contains(e.target as Node)) {
        setSidebarOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [sidebarOpen])

  // Close sidebar on mobile when resizing to smaller screens
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768 && sidebarOpen) {
        setSidebarOpen(false)
      }
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [sidebarOpen])

  const startEditing = (id: string, currentTitle: string) => {
    setEditingHistoryId(id)
    setEditingTitle(currentTitle)
  }

  const saveEdit = (id: string) => {
    if (editingTitle.trim()) {
      setHistoryItems(items => 
        items.map(item => 
          item.id === id ? { ...item, title: editingTitle.trim() } : item
        )
      )
    }
    setEditingHistoryId(null)
    setEditingTitle('')
  }

  const cancelEdit = () => {
    setEditingHistoryId(null)
    setEditingTitle('')
  }

  const handleKeyPress = (e: React.KeyboardEvent, id: string) => {
    if (e.key === 'Enter') {
      saveEdit(id)
    } else if (e.key === 'Escape') {
      cancelEdit()
    }
  }

  // Listen for training modal events from nested components
  useEffect(() => {
    const handleNavigateToTraining = (event: CustomEvent) => {
      console.log('🚀 Training modal event received:', event.detail)
      if (event.detail?.openNewTraining) {
        setTrainingType(event.detail?.trainingType || 'persona')
        setShowTrainingModal(true)
      }
    }

    window.addEventListener('navigateToTraining', handleNavigateToTraining as EventListener)
    
    return () => {
      window.removeEventListener('navigateToTraining', handleNavigateToTraining as EventListener)
    }
  }, [])

  return (
    <div className="relative flex min-h-screen overflow-hidden">
      {/* Simplified Background */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
      </div>

      {/* Mobile/iPad Overlay Background with stronger blur */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 bg-black/60 backdrop-blur-md z-40 xl:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Enhanced Glass Sidebar */}
      <div 
        id="sidebar"
        className={`
          fixed z-50
          flex flex-col
          ${sidebarOpen
            ? 'w-full sm:w-80 translate-x-0'
            : 'w-0 -translate-x-full overflow-hidden'
          }
          transition-all duration-300 ease-out
          sidebar-ultra-low-opacity
          h-screen
        `}
      >
        {/* Sidebar Header with Close Button */}
        <div className="flex items-center justify-between p-4 sidebar-header-ultra-low-opacity">
          <div className="flex items-center space-x-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-neon-green">
              <span className="text-sm font-bold text-black">T</span>
            </div>
            <span className="text-lg font-bold text-neon whitespace-nowrap">Thumbnex</span>
          </div>

          {/* Prominent Close Button - Always Visible */}
          <button
            onClick={() => setSidebarOpen(false)}
            className="flex items-center justify-center w-9 h-9 rounded-lg backdrop-blur-xl bg-red-500/25 hover:bg-red-500/40 border-2 border-red-400/40 hover:border-red-300/60 transition-all duration-300 group shadow-lg"
            title="Close sidebar"
          >
            <X className="h-5 w-5 text-red-200 group-hover:text-white transition-colors font-bold" />
          </button>
        </div>

        {/* New Chat Button with enhanced glow */}
        <div className="px-4 xl:px-4 pt-4">
          <button className="w-full flex items-center space-x-3 px-4 py-3 xl:py-3 bg-neon-green hover:bg-neon-green-strong text-white rounded-lg transition-all duration-300 font-medium min-h-[44px] touch-manipulation group">
            <Plus className="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" />
            <span className="whitespace-nowrap">New Chat</span>
          </button>
        </div>

        {/* Chat History with glass items */}
        <div className="flex-1 px-4 xl:px-4 pt-4 pb-4 overflow-y-auto">
          <div className="mb-3">
            <h3 className="text-xs font-semibold text-green-300/70 uppercase tracking-wider px-2">Recent</h3>
          </div>
          <div className="space-y-1">
            {historyItems.map((item) => (
              <div key={item.id} className="w-full group">
                {editingHistoryId === item.id ? (
                  // Editing mode with glass effect
                  <div className="flex items-center space-x-2 px-3 py-3 glass-neon rounded-lg min-h-[44px]">
                    <MessageSquare className="h-4 w-4 flex-shrink-0 text-green-300" />
                    <input
                      type="text"
                      value={editingTitle}
                      onChange={(e) => setEditingTitle(e.target.value)}
                      onKeyDown={(e) => handleKeyPress(e, item.id)}
                      className="flex-1 min-w-0 glass text-white text-sm xl:text-sm px-2 py-1 rounded border border-green-400/30 focus:border-green-300 focus:outline-none"
                      autoFocus
                    />
                    <button
                      onClick={() => saveEdit(item.id)}
                      className="flex-shrink-0 p-2 text-green-400 hover:text-green-300 transition-colors min-w-[44px] min-h-[44px] xl:min-w-[auto] xl:min-h-[auto] xl:p-1 flex items-center justify-center touch-manipulation glass-neon rounded"
                    >
                                              <Check className="h-3 w-3 xl:h-3 xl:w-3" />
                      </button>
                      <button
                        onClick={cancelEdit}
                        className="flex-shrink-0 p-2 text-red-400 hover:text-red-300 transition-colors min-w-[44px] min-h-[44px] xl:min-w-[auto] xl:min-h-[auto] xl:p-1 flex items-center justify-center touch-manipulation glass-neon rounded"
                      >
                        <X className="h-3 w-3 xl:h-3 xl:w-3" />
                    </button>
                  </div>
                ) : (
                  // Display mode with glass hover
                  <div className="flex items-center space-x-2 px-3 py-3 text-white/70 hover:text-white card-glass-hover rounded-lg transition-all duration-300 cursor-pointer min-h-[44px] touch-manipulation group">
                    <MessageSquare className="h-4 w-4 flex-shrink-0 text-green-300/70 group-hover:text-green-300" />
                    <span className="text-sm xl:text-sm truncate flex-1 min-w-0">{item.title}</span>
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        startEditing(item.id, item.title)
                      }}
                      className="flex-shrink-0 opacity-0 group-hover:opacity-100 p-2 xl:p-1 text-white/50 hover:text-green-300 transition-all duration-300 min-w-[44px] min-h-[44px] xl:min-w-[auto] xl:min-h-[auto] flex items-center justify-center touch-manipulation glass-neon rounded"
                    >
                      <Edit2 className="h-3 w-3" />
                    </button>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Bottom Navigation with enhanced glass effects */}
        <div className="p-4 space-y-2 border-t border-green-400/20 xl:border-t-0">
          <button 
            className="w-full flex items-center space-x-3 px-4 py-3 text-left transition-all duration-300 rounded-lg glass-neon text-green-300 bg-green-400/10 min-h-[44px] touch-manipulation group"
          >
            <Plus className="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" />
            <span className="font-medium whitespace-nowrap">Generator</span>
          </button>

          <button 
            onClick={() => {
              window.dispatchEvent(new CustomEvent('navigateToTraining', { 
                detail: { openNewTraining: true } 
              }))
              setSidebarOpen(false) // Close sidebar on mobile after action
            }}
            className="w-full flex items-center space-x-3 px-4 py-3 text-left transition-all duration-300 rounded-lg card-glass-hover text-white/70 hover:text-white min-h-[44px] touch-manipulation group"
          >
            <BarChart3 className="h-5 w-5 group-hover:text-green-300 transition-colors" />
            <span className="font-medium whitespace-nowrap">Training</span>
          </button>

          <button className="w-full flex items-center space-x-3 px-4 py-3 text-left text-white/70 hover:text-white transition-all duration-300 rounded-lg card-glass-hover min-h-[44px] touch-manipulation group">
            <Users className="h-5 w-5 group-hover:text-green-300 transition-colors" />
            <span className="font-medium whitespace-nowrap">Personas</span>
          </button>
          
          <button 
            onClick={() => {
              window.dispatchEvent(new CustomEvent('navigateToTraining', { 
                detail: { openNewTraining: true, trainingType: 'style' } 
              }))
              setSidebarOpen(false) // Close sidebar on mobile after action
            }}
            className="w-full flex items-center space-x-3 px-4 py-3 text-left text-white/70 hover:text-white transition-all duration-300 rounded-lg card-glass-hover min-h-[44px] touch-manipulation group"
          >
            <Palette className="h-5 w-5 group-hover:text-green-300 transition-colors" />
            <span className="font-medium whitespace-nowrap">Train Style</span>
          </button>

          <button className="w-full flex items-center space-x-3 px-4 py-3 text-left text-white/70 hover:text-white transition-all duration-300 rounded-lg card-glass-hover min-h-[44px] touch-manipulation group">
            <User className="h-5 w-5 group-hover:text-green-300 transition-colors" />
            <span className="font-medium whitespace-nowrap">Account</span>
          </button>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="relative z-10 flex-1 flex flex-col min-w-0">
        {/* Header with hamburger menu - always visible with higher z-index */}
        <div className="relative z-[60] p-4">
          <button
            id="sidebar-toggle"
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="flex items-center justify-center w-10 h-10 rounded-lg glass-neon hover:glass-neon-strong border border-green-500/20 hover:border-green-400/30 transition-all duration-300 touch-manipulation group"
            title={sidebarOpen ? "Close sidebar" : "Open sidebar"}
          >
            {sidebarOpen ? (
              <X className="h-5 w-5 text-white group-hover:text-green-400 transition-colors" />
            ) : (
              <Menu className="h-5 w-5 text-white group-hover:text-green-400 transition-colors" />
            )}
          </button>
        </div>

        {/* Main Content */}
        <ThumbnailGenerator />
      </div>

      {/* Enhanced Training Modal with stronger glass effect */}
      {showTrainingModal && (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-md z-50 flex items-end xl:items-center justify-center p-0 xl:p-4">
          <div className="glass-neon-strong border-t xl:border border-green-400/30 rounded-t-2xl xl:rounded-lg w-full xl:max-w-2xl max-h-[90vh] xl:max-h-[80vh] overflow-y-auto">
            <div className="p-4 xl:p-6 border-b border-green-400/20">
              <div className="flex items-center justify-between">
                {/* Mobile/iPad handle */}
                <div className="xl:hidden w-12 h-1 bg-green-300/50 rounded-full mx-auto mb-4"></div>
                
                <h3 className="text-lg xl:text-xl font-semibold text-neon">New Training</h3>
                <Button
                  onClick={() => setShowTrainingModal(false)}
                  variant="secondary"
                  size="sm"
                  className="min-w-[44px] min-h-[44px] xl:min-w-[auto] xl:min-h-[auto] p-2 xl:p-1 touch-manipulation glass-neon border-green-400/30 hover:border-green-300/50 group"
                >
                  <X className="h-4 w-4 group-hover:text-green-300 transition-colors" />
                </Button>
              </div>
            </div>

            <div className="p-4 xl:p-6 space-y-4">
              <ClientSideTraining 
                trainingType={trainingType}
                onTrainingStarted={(data) => {
                  console.log('Training started:', data)
                  setShowTrainingModal(false)
                }}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 