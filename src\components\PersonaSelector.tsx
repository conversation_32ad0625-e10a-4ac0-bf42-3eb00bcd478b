'use client'

import React, { useState, useCallback, useEffect, useRef } from 'react'
import { 
  User, 
  Plus, 
  Settings, 
  Star, 
  Trash2, 
  Edit, 
  Crown,
  Image as ImageIcon,
  ChevronDown,
  X,
  Crop,
  RotateCcw,
  Check,
  AlertCircle,
  Clock,
  RefreshCw
} from 'lucide-react'
import { But<PERSON> } from './ui/Button'
import { Persona, CreatePersonaRequest } from '../types/persona'
import { usePersonas } from '../hooks/usePersonas'
import { useTrainingPolling } from '../hooks/useTrainingPolling'
import ReactCrop, { Crop as CropType, centerCrop, makeAspectCrop } from 'react-image-crop'
import 'react-image-crop/dist/ReactCrop.css'


interface PersonaSelectorProps {
  onPersonaSelect?: (persona: Persona | null) => void
  showCreateButton?: boolean
  className?: string
  selectedPersona?: Persona | null
  disabled?: boolean
}

const CATEGORY_COLORS = {
  business: 'bg-blue-500',
  gaming: 'bg-purple-500', 
  casual: 'bg-green-500',
  professional: 'bg-indigo-500',
  other: 'bg-gray-500'
}

const CATEGORY_LABELS = {
  business: 'Business',
  gaming: 'Gaming',
  casual: 'Casual', 
  professional: 'Professional',
  other: 'Other'
}

export function PersonaSelector({
  onPersonaSelect,
  showCreateButton = true,
  className = '',
  selectedPersona: externalSelectedPersona,
  disabled = false
}: PersonaSelectorProps) {
  const {
    personas,
    selectedPersona: internalSelectedPersona,
    isLoading,
    error,
    createPersona,
    deletePersona,
    selectPersona,
    setDefaultPersona,
    refreshPersonas,
    resetError
  } = usePersonas()

  // Use external selection if provided, otherwise use internal
  const currentSelectedPersona = externalSelectedPersona !== undefined ? externalSelectedPersona : internalSelectedPersona

  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const buttonRef = useRef<HTMLButtonElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  // Add training polling to track real-time progress
  const { 
    activeTrainings, 
    getTrainingStatus,
    getTimeUntilPolling,
    hasActiveTrainings,
    getWaitingTrainingCount,
    getPollingTrainingCount,
    pollNow 
  } = useTrainingPolling({
    onComplete: (status) => {
      console.log('🎯 Training completed in PersonaSelector:', status)
      // Refresh personas when training completes to update model URLs
      refreshPersonas()
    }
  })

  // Track countdown for waiting trainings
  const [countdowns, setCountdowns] = useState<Record<string, number>>({})

  // Update countdowns every second for waiting trainings
  useEffect(() => {
    const waitingTrainings = activeTrainings.filter(t => t.isWaitingToStartPolling)
    
    if (waitingTrainings.length === 0) {
      setCountdowns({})
      return
    }

    const interval = setInterval(() => {
      const newCountdowns: Record<string, number> = {}
      waitingTrainings.forEach(training => {
        const remaining = getTimeUntilPolling(training.trainingId)
        newCountdowns[training.trainingId] = remaining
      })
      setCountdowns(newCountdowns)
    }, 1000)

    return () => clearInterval(interval)
  }, [activeTrainings, getTimeUntilPolling])

  // Format countdown display
  const formatCountdown = (seconds: number): string => {
    if (seconds <= 0) return "Starting..."
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    if (mins > 0) {
      return `${mins}:${secs.toString().padStart(2, '0')}`
    }
    return `${secs}s`
  }

  // Filter personas: show all personas (simplified approach)
  const filteredPersonas = personas

  // Check if persona is ready (has model_url)
  const isPersonaReady = (persona: any) => {
    return persona.model_url && persona.model_url.length > 0
  }

  // Check if persona is currently training
  const getPersonaTrainingStatus = (persona: any) => {
    // Check if this persona has an active training by looking for training jobs
    // that match this persona's ID and have matching replicate_training_id
    // For now, we'll use a simplified approach and enhance with database lookup if needed
    const training = activeTrainings.find(t => {
      // We need to check if any training job exists with:
      // 1. persona_id matching this persona's id
      // 2. replicate_training_id matching the active training's trainingId
      // For now, we'll use a simplified approach and enhance with database lookup if needed
      return persona.lora_training?.trainingId === t.trainingId
    })
    
    return training || null
  }

  // Handle persona selection - map simplified schema to complex structure
  const handlePersonaSelect = useCallback((persona: any) => {
    selectPersona(persona)
    
    // Map simplified database schema to expected complex structure for generation
    let mappedPersona = null
    if (persona) {
      mappedPersona = {
        ...persona,
        // Map simplified database fields to expected complex structure
        loraTraining: {
          status: persona.model_url ? 'completed' : 'pending',
          modelUrl: persona.model_url,
          triggerWord: persona.trigger_word,
          trainingProgress: persona.model_url ? 100 : 0
        }
      }
      
      console.log('🎭 Persona selected and mapped:', {
        originalPersona: persona,
        mappedPersona: mappedPersona,
        hasModelUrl: !!persona.model_url,
        triggerWord: persona.trigger_word,
        ready: !!persona.model_url
      })
    }
    
    onPersonaSelect?.(mappedPersona)
    setIsDropdownOpen(false)
  }, [onPersonaSelect, selectPersona])

  const handleTrainPersona = async (persona: Persona) => {
    // Navigate to Training Dashboard and open New Training modal
    window.dispatchEvent(new CustomEvent('navigateToTraining', { 
      detail: { openNewTraining: true } 
    }))
    setIsDropdownOpen(false)
  }

  // No auto-refresh needed with simplified approach

  // Listen for persona creation events from training
  useEffect(() => {
    const handlePersonaCreated = (event: CustomEvent) => {
      console.log('🎯 Persona created event received:', event.detail)
      // Refresh personas to show the new one
      refreshPersonas()
    }

    const handleTrainingCompleted = (event: CustomEvent) => {
      console.log('✅ Training completed event received:', event.detail)
      // Refresh personas to update model URLs
      refreshPersonas()
    }

    window.addEventListener('personaCreated', handlePersonaCreated as EventListener)
    window.addEventListener('trainingCompleted', handleTrainingCompleted as EventListener)
    
    return () => {
      window.removeEventListener('personaCreated', handlePersonaCreated as EventListener)
      window.removeEventListener('trainingCompleted', handleTrainingCompleted as EventListener)
    }
  }, [refreshPersonas])

  // Close dropdown when clicking outside
  useEffect(() => {
    if (!isDropdownOpen) return;
    function handleClickOutside(event: MouseEvent) {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isDropdownOpen]);

  return (
    <div className={`relative w-full ${className}`}>
      {/* Enhanced Error Display */}
      {error && (
        <div 
          className="mb-3 p-3 backdrop-blur-xl bg-red-500/10 border-2 border-red-400/40 rounded-xl text-red-300 text-sm shadow-[0_0_30px_rgba(239,68,68,0.3)]"
          style={{
            background: 'linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.05) 50%, rgba(239, 68, 68, 0.1) 100%)',
            boxShadow: '0 0 30px rgba(239, 68, 68, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1), inset 0 -1px 0 rgba(239, 68, 68, 0.2)'
          }}
        >
          <div className="flex items-center justify-between gap-2">
            <span className="flex-1 min-w-0 break-words">{error}</span>
            <button 
              onClick={resetError} 
              className="flex-shrink-0 text-red-400 hover:text-red-300 p-1 min-w-[44px] min-h-[44px] lg:min-w-[auto] lg:min-h-[auto] lg:p-0 flex items-center justify-center touch-manipulation glass-neon rounded hover:shadow-lg hover:shadow-red-500/20 transition-all duration-300"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* Enhanced Main Selector */}
      <div className="space-y-3">
        <div className="flex gap-2">
          <button
            ref={buttonRef}
            onClick={() => !disabled && setIsDropdownOpen(!isDropdownOpen)}
            className={`min-h-[44px] flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-300 flex-1 touch-manipulation group ${
              disabled
                ? 'glass-neon border border-gray-500/30 opacity-50 cursor-not-allowed'
                : 'glass-neon hover:glass-neon-strong border border-green-500/30 hover:border-green-400/50'
            }`}
            disabled={isLoading || disabled}
          >
            <User className={`w-4 h-4 flex-shrink-0 ${disabled ? 'text-gray-400' : 'text-green-400 group-hover:animate-subtle-pulse'}`} />
            <span className={`font-medium text-sm lg:text-xs flex-1 min-w-0 text-left truncate ${disabled ? 'text-gray-400' : 'text-white'}`}>
              {disabled ? 'Persona (Style Selected)' : currentSelectedPersona ? currentSelectedPersona.name : 'Persona'}
              {!disabled && hasActiveTrainings && (
                                  <span className="block lg:inline lg:ml-1 text-green-300 text-xs">
                    ({getPollingTrainingCount()} polling{getWaitingTrainingCount() > 0 ? `, ${getWaitingTrainingCount()} waiting` : ''})
                  </span>
              )}
            </span>
            <ChevronDown className={`w-4 h-4 transition-transform flex-shrink-0 ${disabled ? 'text-gray-400' : 'text-green-400'} ${!disabled && isDropdownOpen ? 'rotate-180' : ''}`} />
          </button>
          
          {hasActiveTrainings && (
            <button
              onClick={() => pollNow(true)}
              className="min-h-[44px] min-w-[44px] px-3 rounded-lg glass-neon hover:glass-neon-strong border border-green-500/50 hover:border-green-400/60 transition-all duration-300 flex items-center justify-center touch-manipulation group"
              title="Force refresh training status"
            >
              <RefreshCw className="w-4 h-4 text-green-400 group-hover:animate-spin" />
            </button>
          )}
        </div>

        {/* Enhanced Dropdown Menu - Opens Upward */}
        {isDropdownOpen && !disabled && (
          <div
            ref={menuRef}
            className="absolute left-0 right-0 bottom-full z-50 mb-2 p-3 backdrop-blur-3xl bg-slate-900/20 border-2 border-green-300/40 rounded-2xl max-h-[70vh] lg:max-h-80 overflow-y-auto min-w-[280px] w-full"
            style={{
              background: 'linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 191, 0, 0.05) 50%, rgba(255, 215, 0, 0.1) 100%)',
              boxShadow: 'inset 0 1px 0 rgba(255, 255, 255, 0.2), inset 0 -1px 0 rgba(255, 215, 0, 0.2)'
            }}
          >
            {/* Enhanced No Persona Option */}
            <button
              onClick={() => {
                onPersonaSelect?.(null)
                setIsDropdownOpen(false)
              }}
              className={`w-full flex items-center space-x-3 p-3 backdrop-blur-xl bg-white/5 hover:bg-white/10 border border-white/10 hover:border-green-300/30 rounded-lg mb-1 transition-all duration-300 ${!currentSelectedPersona ? 'bg-green-500/10 border-green-300/40' : ''}`}
            >
              <div className="w-10 h-10 bg-gradient-to-r from-slate-600 to-slate-500 rounded-lg flex items-center justify-center flex-shrink-0 shadow-lg">
                <User className="w-5 h-5 text-slate-300 group-hover:text-white transition-colors" />
              </div>
              <div className="flex-1 text-left min-w-0">
                <span className="text-white font-medium text-sm block truncate">No Persona</span>
                <p className="text-xs text-green-200/70 truncate">Generate without persona reference</p>
              </div>
            </button>

            {/* Enhanced Create New Button */}
            {showCreateButton && (
              <button
                onClick={() => {
                  // Navigate to Training Dashboard and open New Training modal
                  window.dispatchEvent(new CustomEvent('navigateToTraining', { 
                    detail: { openNewTraining: true } 
                  }))
                  setIsDropdownOpen(false)
                }}
                className="w-full flex items-center space-x-3 p-3 backdrop-blur-xl bg-white/5 hover:bg-white/10 border border-white/10 hover:border-green-300/30 rounded-lg mb-1 transition-all duration-300"
              >
                <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center flex-shrink-0 shadow-lg shadow-purple-500/30">
                  <Plus className="w-5 h-5 text-white group-hover:rotate-90 transition-transform duration-300" />
                </div>
                <div className="flex-1 text-left min-w-0">
                  <span className="text-white font-medium text-sm block truncate">Create New Persona</span>
                  <p className="text-xs text-green-200/70 truncate">Upload images for LoRA training</p>
                </div>
              </button>
            )}

            {/* Enhanced Empty State */}
            {filteredPersonas.length === 0 && !isLoading && (
              <div 
                className="p-6 text-center backdrop-blur-xl bg-white/5 border-2 border-green-300/20 rounded-xl"
                style={{
                  background: 'linear-gradient(135deg, rgba(255, 215, 0, 0.05) 0%, rgba(255, 191, 0, 0.02) 50%, rgba(255, 215, 0, 0.05) 100%)',
                  boxShadow: 'inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                }}
              >
                <User className="w-8 h-8 text-green-300 mx-auto mb-2 animate-float" />
                <p className="text-green-200 text-sm">No personas available</p>
                <p className="text-xs text-green-300/70 mt-1">Create your first persona to get started</p>
              </div>
            )}

            {/* Simplified Persona Items */}
            {filteredPersonas.map((persona: any) => {
              const isReady = isPersonaReady(persona)
              const isSelected = currentSelectedPersona?.id === persona.id
              const trainingStatus = getPersonaTrainingStatus(persona)
              const isTraining = trainingStatus && !trainingStatus.isComplete
              
              return (
                <button
                  key={persona.id}
                  onClick={() => handlePersonaSelect(persona)}
                  className={`w-full flex items-center space-x-3 p-3 backdrop-blur-xl bg-white/5 hover:bg-white/10 border border-white/10 hover:border-green-300/30 rounded-lg mb-1 transition-all duration-300 touch-manipulation ${isSelected ? 'bg-green-500/10 border-green-300/40' : ''}`}
                  disabled={!!isTraining}
                >
                  <div className="relative flex-shrink-0">
                    {persona.image_base64 ? (
                      <img
                        src={persona.image_base64}
                        alt={persona.name}
                        className="w-10 h-10 rounded-lg object-cover border border-green-500/20 shadow-lg"
                      />
                    ) : (
                                              <div className="w-10 h-10 bg-gray-600 rounded-lg flex items-center justify-center border border-green-500/20 shadow-lg">
                        <User className="w-5 h-5 text-white" />
                      </div>
                    )}
                    {/* Simple Status indicator */}
                    <div className={`absolute -top-1 -right-1 w-3 h-3 rounded-full shadow-lg ${
                      isTraining ? 'bg-blue-500 animate-pulse shadow-blue-500/30' :
                      isReady ? 'bg-green-500 shadow-green-500/30' : 'bg-gray-500'
                    }`}></div>
                  </div>
                  
                  <div className="flex-1 text-left min-w-0">
                                         <div className="flex items-center justify-between min-w-0">
                       <span className="text-white font-medium text-sm truncate flex-1">{persona.name}</span>
                       <span className={`hidden md:inline-block text-xs px-2 py-0.5 rounded-full flex-shrink-0 ml-2 ${
                         isTraining ? 'bg-blue-900/30 text-blue-300' :
                         isReady ? 'bg-green-900/30 text-green-300' : 'bg-gray-900/30 text-gray-300'
                       }`}>
                         {isTraining ? 'Training' : isReady ? 'Ready' : 'Not Ready'}
                       </span>
                     </div>
                    {persona.trigger_word && (
                      <p className="text-xs text-green-200/70 truncate mt-0.5">
                        {persona.trigger_word}
                      </p>
                    )}
                  </div>
                </button>
              )
            })}
          </div>
        )}
      </div>

      {/* Note: MultiImagePersonaModal removed - using Training Dashboard instead */}

      {/* Enhanced Overlay */}
      {isDropdownOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black/50 lg:bg-black/30 backdrop-blur-md"
          onClick={() => {
            setIsDropdownOpen(false)
          }}
        />
      )}
    </div>
  )
}

/* 
DEPRECATED: Removed redundant CreatePersonaModal component.
Using MultiImagePersonaModal instead which has the same functionality
with better training integration.
*/ 