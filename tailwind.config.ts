import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        // Custom dark mode colors with orange/red theme
        'bg-primary': '#0a0a0b',
        'bg-secondary': '#1a1a1b', 
        'bg-tertiary': '#2d2d30',
        'accent-primary': '#fb5607',      // Vibrant Orange
        'accent-secondary': '#dc2626',    // Deep Red
        'accent-success': '#10b981',
        'accent-warning': '#fbbf24',      // Gold/Yellow
        'accent-danger': '#ef4444',
        // Orange/red accent colors matching the new theme
        'accent-orange': '#fb5607',
        'accent-orange-light': '#ffbe0b',
        'accent-orange-dark': '#ea580c',
        'accent-red': '#dc2626',
        'accent-red-light': '#f87171',
        'accent-red-dark': '#b91c1c',
        'text-primary': '#f9fafb',
        'text-secondary': '#d1d5db',
        'text-tertiary': '#9ca3af',
        'text-disabled': '#6b7280',
        'border-primary': '#374151',
        'border-secondary': '#4b5563',
        'border-focus': '#fb5607',        // Orange focus
      },
      boxShadow: {
        'glow': '0 0 20px rgba(251, 86, 7, 0.3)',
        'glow-orange': '0 0 20px rgba(251, 86, 7, 0.4)',
        'glow-red': '0 0 20px rgba(220, 38, 38, 0.4)',
      },
      animation: {
        'spin-slow': 'spin 3s linear infinite',
        'pulse-glow': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'float': 'float 3s ease-in-out infinite',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        }
      }
    },
  },
  plugins: [],
}
export default config 