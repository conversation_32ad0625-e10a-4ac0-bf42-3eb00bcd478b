import { NextRequest, NextResponse } from 'next/server';
import Replicate from 'replicate';

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN!,
});

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const trainingId = searchParams.get('trainingId');
    
    if (!trainingId) {
      return NextResponse.json({ error: 'Training ID required' }, { status: 400 });
    }

    console.log('🔍 Checking training status for:', trainingId);
    
    // Get the training status from Replicate
    const training = await replicate.trainings.get(trainingId);
    const trainingData = training as any; // Cast to access all fields
    
    console.log('📊 Training status from Replicate:', {
      id: training.id,
      status: training.status,
      created_at: training.created_at,
      completed_at: training.completed_at,
      error: training.error,
      destination: trainingData.destination,
      logs: training.logs?.slice(-500), // Last 500 chars of logs
      urls: training.urls
    });

    // Extract model URL if training is completed
    let modelUrl = null;
    if (training.status === 'succeeded') {
      // The model URL should be the destination field
      modelUrl = trainingData.destination;
      console.log('🎯 Model URL found:', modelUrl);
      
      if (!modelUrl) {
        console.log('⚠️ No destination found, checking other fields...');
        console.log('Full training object keys:', Object.keys(training));
        console.log('Full training object:', training);
      }
    }

    return NextResponse.json({
      success: true,
      training: {
        id: training.id,
        status: training.status,
        created_at: training.created_at,
        completed_at: training.completed_at,
        error: training.error,
        logs: training.logs,
        urls: training.urls,
        destination: trainingData.destination,
        modelUrl: modelUrl
      }
    });

  } catch (error) {
    console.error('❌ Error checking training status:', error);
    return NextResponse.json({ 
      error: 'Failed to check training status',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 