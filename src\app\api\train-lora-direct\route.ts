import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Direct LoRA training endpoint called')
    
    // Parse request body
    const requestBody = await request.json()
    const { zipUrl, triggerWord, personaName, personaId } = requestBody
    
    // Support both new direct format AND old persona-based format
    if (personaId) {
      console.log('🔄 Legacy persona-based training request detected, redirecting...')
      // Redirect to existing persona workflow but return proper error for now
      return NextResponse.json({
        error: 'Legacy persona-based training deprecated. Please use the new Training Dashboard for creating personas.',
        message: 'The old persona-based training system has been deprecated. Please create new personas using the Training Dashboard interface.',
        migrationRequired: true
      }, { status: 400 })
    }
    
    // Validate inputs for new direct format
    if (!zipUrl || !triggerWord || !personaName) {
      return NextResponse.json({
        error: 'Missing required parameters',
        required: ['zipUrl', 'triggerWord', 'personaName'],
        note: 'Use the new Training Dashboard to create personas with LoRA training'
      }, { status: 400 })
    }
    
    // Validate environment
    if (!process.env.REPLICATE_API_TOKEN) {
      return NextResponse.json({
        error: 'REPLICATE_API_TOKEN not configured'
      }, { status: 500 })
    }
    
    if (!process.env.REPLICATE_USERNAME) {
      return NextResponse.json({
        error: 'REPLICATE_USERNAME not configured'
      }, { status: 500 })
    }

    // Validate webhook configuration
    if (!process.env.REPLICATE_WEBHOOK_SECRET) {
      console.warn('⚠️ REPLICATE_WEBHOOK_SECRET not configured - webhooks may not work')
    }
    
    console.log('📋 Training parameters:', {
      zipUrl,
      triggerWord,
      personaName,
      timestamp: new Date().toISOString()
    })
    
    // Step 1: Create a new model for the training destination
    const modelName = `trained-model-${Date.now()}`
    const destination = `${process.env.REPLICATE_USERNAME}/${modelName}`
    
    console.log('📦 Creating destination model:', destination)
    
    const createModelResponse = await fetch('https://api.replicate.com/v1/models', {
      method: 'POST',
      headers: {
        'Authorization': `Token ${process.env.REPLICATE_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        owner: process.env.REPLICATE_USERNAME,
        name: modelName,
        description: `FLUX LoRA trained with trigger word: ${triggerWord}`,
        visibility: 'private',
        hardware: 'gpu-a100-large'
      })
    })
    
    if (!createModelResponse.ok) {
      const createError = await createModelResponse.text()
      console.error('❌ Failed to create model:', createError)
      return NextResponse.json({
        error: 'Failed to create destination model',
        details: createError
      }, { status: 500 })
    }
    
    const createdModel = await createModelResponse.json()
    console.log('✅ Created model:', createdModel.url)
    
    // Step 2: Start training into the created model WITH WEBHOOK
    console.log('🚀 Starting training with webhook...')
    
    // Construct webhook URL - use the actual deployment URL
    const baseUrl = request.url.includes('localhost') 
      ? 'https://thumbnex-ejan.vercel.app'  // Always use production URL for webhooks
      : request.url.split('/api')[0]  // Extract base URL from current request
    
    const webhookUrl = `${baseUrl}/api/webhooks/training-complete-v2`
    console.log('🔗 Webhook URL:', webhookUrl)
    
    const response = await fetch('https://api.replicate.com/v1/models/replicate/fast-flux-trainer/versions/8b10794665aed907bb98a1a5324cd1d3a8bea0e9b31e65210967fb9c9e2e08ed/trainings', {
      method: 'POST',
      headers: {
        'Authorization': `Token ${process.env.REPLICATE_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        destination: destination,
        webhook: webhookUrl,
        webhook_events_filter: ["completed"],  // Only notify when training is complete
        input: {
          input_images: zipUrl,
          trigger_word: triggerWord,
          lora_type: 'subject',
          steps: 1000,
          lora_rank: 16,
          optimizer: 'adamw8bit',
          batch_size: 1,
          resolution: '512,768,1024',
          autocaption: true,
          trigger_word_weight: 1.0,
          learning_rate: 0.0004
        }
      })
    })
    
    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ Replicate API error:', errorText)
      return NextResponse.json({
        error: 'Failed to start training',
        details: errorText
      }, { status: 500 })
    }
    
    const result = await response.json()
    
    console.log('✅ Training started successfully with webhook:', result.id)
    
    return NextResponse.json({
      success: true,
      trainingId: result.id,
      status: result.status,
      message: 'Training started successfully with webhook notification',
      estimatedTime: '10-15 minutes',
      estimatedCost: '$6-8 USD',
      modelUrl: createdModel.url,
      destination: destination,
      webhook: {
        url: webhookUrl,
        configured: true,
        events: ["completed"]
      }
    })
    
  } catch (error) {
    console.error('❌ Training error:', error)
    return NextResponse.json({
      error: 'Failed to start training',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const trainingId = searchParams.get('trainingId')
    
    if (!trainingId) {
      return NextResponse.json({
        error: 'Training ID required'
      }, { status: 400 })
    }
    
    if (!process.env.REPLICATE_API_TOKEN) {
      return NextResponse.json({
        error: 'REPLICATE_API_TOKEN not configured'
      }, { status: 500 })
    }
    
    // Check training status using trainings API
    const response = await fetch(`https://api.replicate.com/v1/trainings/${trainingId}`, {
      headers: {
        'Authorization': `Token ${process.env.REPLICATE_API_TOKEN}`,
      }
    })
    
    if (!response.ok) {
      const errorText = await response.text()
      return NextResponse.json({
        error: 'Failed to check status',
        details: errorText
      }, { status: 500 })
    }
    
    const result = await response.json()
    
    return NextResponse.json({
      success: true,
      status: result.status,
      progress: result.status === 'processing' ? 50 : result.status === 'succeeded' ? 100 : 0,
      error: result.error,
      modelUrl: result.output,
      logs: result.logs
    })
    
  } catch (error) {
    console.error('❌ Status check error:', error)
    return NextResponse.json({
      error: 'Failed to check status',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
} 