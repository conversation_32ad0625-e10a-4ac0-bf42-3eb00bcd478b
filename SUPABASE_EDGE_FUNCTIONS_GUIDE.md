# 🚀 Supabase Edge Functions Setup Guide

## 🎯 **When to Use Supabase Edge Functions vs Next.js API Routes**

### **Use Supabase Edge Functions if:**
- You want to keep all backend logic in Supabase
- You need global edge distribution for better performance
- You want to separate API logic from your frontend deployment

### **Use Next.js API Routes if:**
- You want simpler deployment (everything in one place)
- You're already using Vercel/Netlify
- You prefer keeping frontend and backend together

## 🔧 **Setting Up Supabase Edge Functions**

### **Step 1: Install Supabase CLI**
```bash
npm install -g supabase
supabase login
```

### **Step 2: Initialize Supabase Project**
```bash
# In your project root
supabase init
```

### **Step 3: Create Edge Functions**
```bash
# Create the training function
supabase functions new train-lora

# Create the generation function  
supabase functions new generate-thumbnail

# Create the webhook function
supabase functions new training-webhook
```

### **Step 4: Set Environment Variables**
```bash
# Set secrets for your Edge Functions
supabase secrets set REPLICATE_API_TOKEN=your_token_here
supabase secrets set REPLICATE_USERNAME=your_username
supabase secrets set REPLICATE_WEBHOOK_SECRET=your_webhook_secret
```

### **Step 5: Create Edge Function Code**

**File: `supabase/functions/train-lora/index.ts`**
```typescript
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { personaId } = await req.json()
    
    // Get Replicate token from environment
    const replicateToken = Deno.env.get('REPLICATE_API_TOKEN')
    if (!replicateToken) {
      throw new Error('Replicate API token not configured')
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Find persona
    const { data: persona, error } = await supabase
      .from('personas')
      .select('*')
      .eq('id', personaId)
      .single()

    if (error || !persona) {
      throw new Error('Persona not found')
    }

    // Start training with Replicate
    const trainingResponse = await fetch(
      'https://api.replicate.com/v1/models/ostris/flux-dev-lora-trainer/trainings',
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${replicateToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          destination: `${Deno.env.get('REPLICATE_USERNAME')}/${persona.name}-${Date.now()}`,
          webhook: `${Deno.env.get('SUPABASE_URL')}/functions/v1/training-webhook`,
          input: {
            input_images: persona.lora_training.trainingImages,
            trigger_word: persona.lora_training.triggerWord,
            steps: 1000
          }
        })
      }
    )

    const trainingData = await trainingResponse.json()

    if (!trainingResponse.ok) {
      throw new Error(`Training failed: ${trainingData.error}`)
    }

    // Update persona with training ID
    await supabase
      .from('personas')
      .update({
        lora_training: {
          ...persona.lora_training,
          trainingId: trainingData.id,
          status: 'training'
        }
      })
      .eq('id', personaId)

    return new Response(
      JSON.stringify({
        success: true,
        trainingId: trainingData.id,
        message: 'Training started successfully'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    return new Response(
      JSON.stringify({
        error: error.message || 'Training failed to start'
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})
```

### **Step 6: Deploy Edge Functions**
```bash
# Deploy all functions
supabase functions deploy

# Or deploy specific function
supabase functions deploy train-lora
```

### **Step 7: Update Frontend to Use Edge Functions**

**In your React components, change API calls from:**
```typescript
// Old: Next.js API route
const response = await fetch('/api/train-lora', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ personaId })
})
```

**To: Supabase Edge Function**
```typescript
// New: Supabase Edge Function
const response = await fetch(
  `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/train-lora`,
  {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`
    },
    body: JSON.stringify({ personaId })
  }
)
```

## 🔧 **Environment Variables for Edge Functions**

### **In Supabase Dashboard:**
1. Go to **Project Settings** → **Edge Functions**
2. Add these secrets:

```env
REPLICATE_API_TOKEN=your_replicate_token
REPLICATE_USERNAME=your_replicate_username  
REPLICATE_WEBHOOK_SECRET=your_webhook_secret
```

### **In Your Frontend (.env.local):**
```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
```

## ✅ **Benefits of Edge Functions**

1. **🌍 Global Distribution**: Runs on Cloudflare edge network
2. **🔒 Secure**: API tokens stay in Supabase, not exposed to frontend
3. **📊 Integrated**: Direct database access without additional setup
4. **💰 Cost Effective**: Pay only for execution time

## 🔄 **Migration Steps**

If you want to switch from Next.js API routes to Edge Functions:

1. **Create Edge Functions** (as shown above)
2. **Deploy them** to Supabase
3. **Update frontend calls** to use Edge Function URLs
4. **Remove Next.js API routes** from `src/app/api/`
5. **Test thoroughly** before going live

Would you like me to help you set up Edge Functions, or would you prefer to fix the environment variable configuration for your current Next.js setup? 