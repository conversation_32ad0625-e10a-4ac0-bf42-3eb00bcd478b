import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '../../../lib/supabase'

// In-memory cache for training status
interface CachedTrainingStatus {
  data: any
  timestamp: number
  status: string
}

const trainingCache = new Map<string, CachedTrainingStatus>()

// Cache configuration - AGGRESSIVE for real-time updates
const CACHE_CONFIG = {
  // Cache completed trainings for longer (they won't change)
  COMPLETED_TTL: 10 * 60 * 1000, // 10 minutes for completed/failed trainings
  
  // Much more aggressive caching for active trainings
  STARTING_TTL: 3 * 1000,       // 3 seconds for starting (changes very quickly)
  PROCESSING_TTL: 5 * 1000,     // 5 seconds for processing (changes quickly)
  DEFAULT_TTL: 10 * 1000,       // 10 seconds for other states
  
  // Force fresh check after this time regardless of status
  MAX_TTL: 2 * 60 * 1000        // 2 minutes max cache (reduced from 5)
}

function getCacheTTL(status: string): number {
  switch (status) {
    case 'succeeded':
    case 'failed':
    case 'canceled':
      return CACHE_CONFIG.COMPLETED_TTL
    case 'starting':
      return CACHE_CONFIG.STARTING_TTL
    case 'processing':
      return CACHE_CONFIG.PROCESSING_TTL
    default:
      return CACHE_CONFIG.DEFAULT_TTL
  }
}

function isCacheValid(cached: CachedTrainingStatus): boolean {
  const age = Date.now() - cached.timestamp
  const ttl = getCacheTTL(cached.status)
  
  // Never cache longer than MAX_TTL regardless of status
  const maxTTL = Math.min(ttl, CACHE_CONFIG.MAX_TTL)
  
  return age < maxTTL
}

/**
 * Poll Replicate Training Status and Update Database
 * This endpoint provides real-time training status updates with intelligent caching
 * to reduce redundant API calls while maintaining responsiveness.
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const trainingId = searchParams.get('trainingId')
    const forceRefresh = searchParams.get('forceRefresh') === 'true'
    
    if (!trainingId) {
      return NextResponse.json({
        success: false,
        error: 'Training ID is required'
      }, { status: 400 })
    }

    // Step 1: Check cache first (unless force refresh requested)
    const cached = trainingCache.get(trainingId)
    if (!forceRefresh && cached && isCacheValid(cached)) {
      console.log(`💾 Using cached status for ${trainingId} (${cached.status}, age: ${Math.round((Date.now() - cached.timestamp) / 1000)}s)`)
      
      // Return cached response with cache indicator
      const response = buildResponse(trainingId, cached.data, true)
      return NextResponse.json(response)
    }

    // Get Replicate API token
    const replicateToken = process.env.REPLICATE_API_TOKEN
    if (!replicateToken) {
      return NextResponse.json({
        success: false,
        error: 'Replicate API token not configured'
      }, { status: 500 })
    }

    // Step 2: Fetch fresh data from Replicate
    console.log(`🔍 Fetching fresh status from Replicate: ${trainingId}${forceRefresh ? ' (forced)' : ''}`)
    
    const replicateResponse = await fetch(`https://api.replicate.com/v1/trainings/${trainingId}`, {
      headers: {
        'Authorization': `Token ${replicateToken}`,
        'Content-Type': 'application/json'
      }
    })

    if (!replicateResponse.ok) {
      return NextResponse.json({
        success: false,
        error: `Failed to fetch training status: ${replicateResponse.status}`
      }, { status: 500 })
    }

    const trainingData = await replicateResponse.json()
    
    // Step 3: Update cache with fresh data
    trainingCache.set(trainingId, {
      data: trainingData,
      timestamp: Date.now(),
      status: trainingData.status
    })
    
    console.log(`📊 Fresh data cached for ${trainingId}: ${trainingData.status}${trainingData.progress ? ` (${Math.round(trainingData.progress * 100)}%)` : ''}`)
    
    // Step 4: Update database with current status
    if (supabase) {
      try {
        // Update training job with current progress
        const jobUpdate: any = {
          status: trainingData.status === 'succeeded' ? 'completed' : 
                 trainingData.status === 'failed' ? 'failed' :
                 trainingData.status === 'canceled' ? 'failed' : 'training',
          updated_at: new Date().toISOString()
        }

        // Add progress if available
        if (trainingData.progress) {
          jobUpdate.progress = Math.round(trainingData.progress * 100)
        }

        // If training completed successfully, extract model URL
        if (trainingData.status === 'succeeded') {
          let modelUrl = null
          
          // Try different fields for model URL (in priority order)
          if (trainingData.output?.version) {
            modelUrl = trainingData.output.version
          } else if (trainingData.destination) {
            modelUrl = trainingData.destination
          } else if (trainingData.output?.weights) {
            // Convert weights URL to model identifier if needed
            modelUrl = trainingData.output.weights
          }

          if (modelUrl) {
            jobUpdate.model_url = modelUrl
            jobUpdate.completed_at = trainingData.completed_at || new Date().toISOString()
            
            console.log(`✅ Training completed! Model URL: ${modelUrl}`)
          }
        }

        // Update training job
        await supabase
          .from('training_jobs')
          .update(jobUpdate)
          .eq('replicate_training_id', trainingId)

        // Step 5: Update associated persona if training completed
        if (trainingData.status === 'succeeded' && jobUpdate.model_url) {
          // Find the associated persona
          const { data: trainingJob } = await supabase
            .from('training_jobs')
            .select('persona_id, trigger_word')
            .eq('replicate_training_id', trainingId)
            .single()

          if (trainingJob?.persona_id) {
            await supabase
              .from('personas')
              .update({
                model_url: jobUpdate.model_url,
                updated_at: new Date().toISOString()
              })
              .eq('id', trainingJob.persona_id)
            
            console.log(`🎭 Updated persona ${trainingJob.persona_id} with model URL`)
          }
        }

      } catch (dbError) {
        console.error('❌ Database update failed:', dbError)
        // Continue anyway - still return Replicate status
      }
    }

    // Step 6: Return current status to frontend
    const response = buildResponse(trainingId, trainingData, false)
    return NextResponse.json(response)

  } catch (error) {
    console.error('❌ Poll training status error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

function buildResponse(trainingId: string, trainingData: any, fromCache: boolean) {
  return {
    success: true,
    trainingId,
    status: trainingData.status,
    progress: trainingData.progress ? Math.round(trainingData.progress * 100) : null,
    isComplete: ['succeeded', 'failed', 'canceled'].includes(trainingData.status),
    modelUrl: trainingData.status === 'succeeded' ? 
              (trainingData.output?.version || trainingData.destination || null) : null,
    error: trainingData.status === 'failed' ? trainingData.error : null,
    estimatedTime: trainingData.status === 'starting' ? '1-2 minutes' : 
                  trainingData.status === 'processing' ? '30-60 seconds' : null,
    cached: fromCache,
    cacheAge: fromCache ? Math.round((Date.now() - trainingCache.get(trainingId)!.timestamp) / 1000) : 0
  }
}

// Cleanup old cache entries periodically
setInterval(() => {
  const now = Date.now()
  let cleanedCount = 0
  
  for (const [trainingId, cached] of Array.from(trainingCache.entries())) {
    const age = now - cached.timestamp
    if (age > CACHE_CONFIG.MAX_TTL * 2) { // Clean entries older than 10 minutes
      trainingCache.delete(trainingId)
      cleanedCount++
    }
  }
  
  if (cleanedCount > 0) {
    console.log(`🧹 Cleaned ${cleanedCount} old cache entries`)
  }
}, 10 * 60 * 1000) // Run cleanup every 10 minutes 