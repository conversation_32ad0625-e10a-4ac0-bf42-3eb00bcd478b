'use client'

import { useState, useCallback, useEffect } from 'react'
import { X, ImageIcon, AlertCircle, Sparkles, Loader2, CheckCircle2, XCircle } from 'lucide-react'
import { Persona, CreatePersonaRequest } from '../types/persona'
import { <PERSON><PERSON> } from './ui/Button'

interface ImageLoadingState {
  file: File
  preview?: string
  status: 'loading' | 'loaded' | 'error'
  error?: string
}

interface MultiImagePersonaModalProps {
  onClose: () => void
  onCreate: (data: CreatePersonaRequest) => Promise<Persona | null>
}

export function MultiImagePersonaModal({ onClose, onCreate }: MultiImagePersonaModalProps) {
  const [name, setName] = useState('')
  const [description, setDescription] = useState('')
  const [category, setCategory] = useState<Persona['category']>('other')
  const [isDefault, setIsDefault] = useState(false)
  const [imageStates, setImageStates] = useState<ImageLoadingState[]>([])
  const [triggerWord, setTriggerWord] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isTraining, setIsTraining] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [dragActive, setDragActive] = useState(false)
  const [isProcessingImages, setIsProcessingImages] = useState(false)

  // Cleanup blob URLs when component unmounts or images change
  useEffect(() => {
    return () => {
      imageStates.forEach(state => {
        if (state.preview) {
          URL.revokeObjectURL(state.preview)
        }
      })
    }
  }, [imageStates])

  // Generate trigger word from name
  const generateTriggerWord = (personaName: string) => {
    const cleaned = personaName.replace(/[^a-zA-Z0-9]/g, '').toUpperCase()
    return cleaned.length > 0 ? `${cleaned.slice(0, 6)}TOK` : 'PERSONTOK'
  }

  // Update trigger word when name changes
  const handleNameChange = (newName: string) => {
    setName(newName)
    if (!triggerWord || triggerWord.endsWith('TOK')) {
      setTriggerWord(generateTriggerWord(newName))
    }
  }

  // Process images one by one to avoid memory issues on mobile
  const processImageQueue = useCallback(async (files: File[]) => {
    const initialStates: ImageLoadingState[] = files.map(file => ({
      file,
      status: 'loading' as const
    }))
    
    setImageStates(prevStates => [...prevStates, ...initialStates])
    setIsProcessingImages(true)
    
    // Process images sequentially to avoid memory pressure on mobile
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      const stateIndex = imageStates.length + i
      
      try {
        console.log(`📸 Processing image ${i + 1}/${files.length}: ${file.name}`)
        
        // Validate file before processing
        if (!file.type.startsWith('image/')) {
          throw new Error('Invalid file type')
        }
        
        if (file.size > 10 * 1024 * 1024) {
          throw new Error('File too large (max 10MB)')
        }
        
        if (file.size < 1024) {
          throw new Error('File too small (min 1KB)')
        }
        
        // Create preview with blob URL (more memory efficient than base64)
        const preview = URL.createObjectURL(file)
        
        // Update state for this specific image
        setImageStates((prevStates: ImageLoadingState[]) => {
          const newStates = [...prevStates]
          if (newStates[stateIndex]) {
            newStates[stateIndex] = {
              ...newStates[stateIndex],
              preview,
              status: 'loaded'
            }
          }
          return newStates
        })
        
        console.log(`✅ Successfully processed image ${i + 1}/${files.length}`)
        
        // Small delay to prevent UI blocking on mobile
        if (i < files.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 50))
        }
        
      } catch (err) {
        console.error(`❌ Failed to process image ${i + 1}:`, err)
        
        // Update state with error
        setImageStates(prevStates => {
          const newStates = [...prevStates]
          if (newStates[stateIndex]) {
            newStates[stateIndex] = {
              ...newStates[stateIndex],
              status: 'error',
              error: err instanceof Error ? err.message : 'Failed to process image'
            }
          }
          return newStates
        })
      }
    }
    
    setIsProcessingImages(false)
    console.log(`🎯 Finished processing ${files.length} images`)
  }, [imageStates.length])

  const handleImageChange = useCallback((files: FileList | File[]) => {
    const fileArray = Array.from(files)
    const validFiles = fileArray.filter(file => file.type.startsWith('image/'))
    
    if (validFiles.length !== fileArray.length) {
      setError(`${fileArray.length - validFiles.length} files were skipped (not image files)`)
    } else {
      setError(null)
    }

    if (validFiles.length === 0) {
      return
    }

    // Limit total images to 25
    const currentCount = imageStates.length
    const remainingSlots = 25 - currentCount
    const filesToProcess = validFiles.slice(0, remainingSlots)
    
    if (filesToProcess.length < validFiles.length) {
      setError(`Only ${filesToProcess.length} images added (25 max total, ${currentCount} already uploaded)`)
    }
    
    if (filesToProcess.length > 0) {
      processImageQueue(filesToProcess)
    }
  }, [imageStates.length, processImageQueue])

  const removeImage = (index: number) => {
    setImageStates(prevStates => {
      const imageToRemove = prevStates[index]
      // Clean up blob URL before removing
      if (imageToRemove?.preview) {
        URL.revokeObjectURL(imageToRemove.preview)
      }
      return prevStates.filter((_, i) => i !== index)
    })
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleImageChange(e.dataTransfer.files)
    }
  }

  // Get statistics about image loading
  const loadedImages = imageStates.filter((state: ImageLoadingState) => state.status === 'loaded')
  const loadingImages = imageStates.filter((state: ImageLoadingState) => state.status === 'loading')  
  const errorImages = imageStates.filter((state: ImageLoadingState) => state.status === 'error')
  const allImagesProcessed = imageStates.length > 0 && loadingImages.length === 0 && !isProcessingImages
  const hasValidImages = loadedImages.length >= 10
  const canStartTraining = hasValidImages && allImagesProcessed && !isSubmitting && !isTraining

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!name.trim() || !hasValidImages) {
      setError('Please provide a name and upload at least 10 valid images.')
      return
    }

    if (!triggerWord.trim()) {
      setError('Please provide a trigger word.')
      return
    }

    if (!allImagesProcessed) {
      setError('Please wait for all images to finish loading.')
      return
    }

    setIsSubmitting(true)
    setError(null)
    
    try {
      const result = await onCreate({
        name: name.trim(),
        description: description.trim() || undefined,
        category,
        isDefault,
        imageFiles: loadedImages.map(state => state.file),
        triggerWord: triggerWord.trim()
      })
      
      if (result) {
        onClose()
      } else {
        setError('Failed to create persona. Please try again.')
      }
    } catch (err: any) {
      console.error('Error creating persona:', err)
      setError(err.message || 'Failed to create persona. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCreateAndTrain = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!name.trim() || !hasValidImages) {
      setError('Please provide a name and upload at least 10 valid images.')
      return
    }

    if (!triggerWord.trim()) {
      setError('Please provide a trigger word.')
      return
    }

    if (!allImagesProcessed) {
      setError('Please wait for all images to finish loading.')
      return
    }

    setIsSubmitting(true)
    setError(null)
    
    try {
      // First create the persona
      const result = await onCreate({
        name: name.trim(),
        description: description.trim() || undefined,
        category,
        isDefault,
        imageFiles: loadedImages.map(state => state.file),
        triggerWord: triggerWord.trim()
      })
      
      if (result) {
        setIsSubmitting(false)
        setIsTraining(true)
        
        // Add a small delay to ensure persona is fully saved
        console.log('⏳ Waiting for persona to be fully saved...')
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // Start LoRA training with retry mechanism
        const maxRetries = 3
        let retryCount = 0
        
        while (retryCount < maxRetries) {
          try {
            console.log(`🚀 Attempting to start training (attempt ${retryCount + 1}/${maxRetries})...`)
            
            const trainingResponse = await fetch('/api/train-lora-direct', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                personaId: result.id
              })
            })

            const trainingData = await trainingResponse.json()

            if (!trainingResponse.ok) {
              if (trainingData.error === 'Persona not found' && retryCount < maxRetries - 1) {
                console.log(`⚠️ Persona not found, retrying in 2 seconds... (attempt ${retryCount + 1})`)
                retryCount++
                await new Promise(resolve => setTimeout(resolve, 2000))
                continue
              }
              throw new Error(trainingData.error || 'Training failed to start')
            }

            // Success! Break out of retry loop
            console.log('✅ Training started successfully!')
            
            alert(
              `✅ Persona created and LoRA training started!\n\n` +
              `Persona: ${result.name}\n` +
              `Training ID: ${trainingData.trainingId}\n` +
              `Estimated time: ${trainingData.estimatedTime}\n` +
              `Estimated cost: ${trainingData.estimatedCost}\n\n` +
              `You can monitor progress in the persona selector.`
            )

            onClose()
            return
            
          } catch (trainingError: any) {
            if (retryCount === maxRetries - 1) {
              // Last attempt failed
              console.error('❌ Training start failed after all retries:', trainingError)
              setError(`Persona created successfully, but training failed to start: ${trainingError.message}`)
              return
            }
            
            console.log(`⚠️ Training attempt ${retryCount + 1} failed, retrying...`, trainingError.message)
            retryCount++
            await new Promise(resolve => setTimeout(resolve, 2000))
          }
        }
      } else {
        setError('Failed to create persona. Please try again.')
      }
    } catch (err: any) {
      console.error('Error creating persona:', err)
      setError(err.message || 'Failed to create persona. Please try again.')
    } finally {
      setIsSubmitting(false)
      setIsTraining(false)
    }
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50">
      <div className="bg-slate-800 rounded-lg border border-slate-600 p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">Create New Persona</h3>
          <button onClick={onClose} className="text-slate-400 hover:text-white">
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Important Notice */}
        <div className="mb-4 p-4 bg-blue-900/30 border border-blue-600/50 rounded-lg">
          <div className="flex items-start space-x-2">
            <AlertCircle className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-blue-200">
              <p className="font-medium mb-2">📸 Training Requirements (Fast FLUX Trainer):</p>
              <ul className="space-y-1 text-blue-300 text-sm">
                <li>• <strong>Minimum 10 images</strong> (10-20 recommended for best results)</li>
                <li>• <strong>Different expressions</strong> - happy, neutral, surprised, focused, etc.</li>
                <li>• <strong>Varied backgrounds</strong> - different settings and lighting</li>
                <li>• <strong>Same person only</strong> - consistent face across all images</li>
                <li>• <strong>High quality</strong> - clear, well-lit, front-facing preferred</li>
                <li>• <strong>1024x1024 ideal</strong> - system will auto-resize if needed</li>
              </ul>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Error Display */}
          {error && (
            <div className="p-3 bg-red-900/30 border border-red-600/50 rounded-lg">
              <div className="flex items-start space-x-2">
                <AlertCircle className="w-4 h-4 text-red-400 mt-0.5 flex-shrink-0" />
                <p className="text-sm text-red-200">{error}</p>
              </div>
            </div>
          )}

          {/* Multiple Image Upload */}
          <div className="space-y-3">
                          <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-white">Training Images</label>
                <div className="text-right">
                  <span className={`text-sm ${loadedImages.length >= 10 ? 'text-green-300' : 'text-green-300'}`}>
                    {loadedImages.length}/25 loaded {loadedImages.length >= 10 ? '✓' : `(need ${10 - loadedImages.length} more)`}
                  </span>
                  {imageStates.length > loadedImages.length && (
                    <div className="text-xs text-blue-400">
                      {loadingImages.length} loading, {errorImages.length} errors
                    </div>
                  )}
                </div>
              </div>
            
            {/* Upload Area */}
            <div
              className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                dragActive 
                  ? 'border-purple-500 bg-purple-500/10' 
                  : 'border-slate-600 hover:border-slate-500'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <ImageIcon className="w-12 h-12 text-slate-400 mx-auto mb-3" />
              <p className="text-lg text-slate-300 mb-2">Upload Training Images</p>
              <p className="text-sm text-slate-400 mb-4">
                Drag & drop multiple images or click to browse
              </p>
              <input
                type="file"
                accept="image/*"
                multiple
                onChange={(e) => e.target.files && handleImageChange(e.target.files)}
                className="hidden"
                id="training-images"
              />
              <label
                htmlFor="training-images"
                className="inline-block px-6 py-3 bg-purple-600 text-white rounded-lg cursor-pointer hover:bg-purple-700 transition-colors"
              >
                Choose Images
              </label>
            </div>

            {/* Image Loading Status */}
            {imageStates.length > 0 && (
              <div className="space-y-3">
                {/* Loading Progress Bar */}
                {(isProcessingImages || loadingImages.length > 0) && (
                  <div className="p-3 backdrop-blur-xl bg-blue-500/10 border border-blue-400/30 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Loader2 className="w-4 h-4 text-blue-400 animate-spin flex-shrink-0" />
                      <div className="flex-1">
                        <p className="text-sm text-blue-300 font-medium">
                          Processing images... {loadedImages.length}/{imageStates.length} complete
                        </p>
                        <div className="w-full bg-blue-900/30 rounded-full h-2 mt-1">
                          <div 
                            className="bg-gradient-to-r from-blue-500 to-blue-400 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${(loadedImages.length / imageStates.length) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                    <p className="text-xs text-blue-400 mt-2">
                      📱 Processing images sequentially for optimal mobile performance
                    </p>
                  </div>
                )}

                {/* Error Summary */}
                {errorImages.length > 0 && (
                  <div className="p-3 backdrop-blur-xl bg-red-500/10 border border-red-400/30 rounded-lg">
                    <div className="flex items-start space-x-2">
                      <XCircle className="w-4 h-4 text-red-400 mt-0.5 flex-shrink-0" />
                      <div>
                        <p className="text-sm text-red-300 font-medium">
                          {errorImages.length} image(s) failed to load
                        </p>
                        <p className="text-xs text-red-400 mt-1">
                          Remove error images and try uploading different files
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Ready Status */}
                {allImagesProcessed && hasValidImages && (
                  <div className="p-3 backdrop-blur-xl bg-green-500/10 border border-green-400/30 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <CheckCircle2 className="w-4 h-4 text-green-400 flex-shrink-0" />
                      <p className="text-sm text-green-300 font-medium">
                        ✅ All images loaded successfully! Ready to start training.
                      </p>
                    </div>
                  </div>
                )}

                {/* Image Previews Grid */}
                <div className="grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-3">
                  {imageStates.map((state, index) => (
                    <div key={index} className="relative group">
                      <div className="w-full h-20 bg-slate-700 rounded-lg border border-slate-600 flex items-center justify-center">
                        {state.status === 'loading' && (
                          <div className="flex flex-col items-center space-y-1">
                            <Loader2 className="w-4 h-4 text-blue-400 animate-spin" />
                            <span className="text-xs text-blue-400">Loading...</span>
                          </div>
                        )}
                        {state.status === 'loaded' && state.preview && (
                          <img
                            src={state.preview}
                            alt={`Training image ${index + 1}`}
                            className="w-full h-full object-cover rounded-lg"
                          />
                        )}
                        {state.status === 'error' && (
                          <div className="flex flex-col items-center space-y-1">
                            <XCircle className="w-4 h-4 text-red-400" />
                            <span className="text-xs text-red-400" title={state.error || 'Error'}>Error</span>
                          </div>
                        )}
                      </div>
                      
                      {/* Remove Button */}
                      <button
                        type="button"
                        onClick={() => removeImage(index)}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600"
                        title="Remove image"
                      >
                        <X className="w-3 h-3" />
                      </button>
                      
                      {/* Image Number */}
                      <div className="absolute bottom-1 left-1 bg-black/70 text-white text-xs px-1 rounded">
                        {index + 1}
                      </div>
                      
                      {/* Status Indicator */}
                      <div className="absolute top-1 left-1">
                        {state.status === 'loaded' && (
                          <CheckCircle2 className="w-3 h-3 text-green-400" />
                        )}
                        {state.status === 'loading' && (
                          <Loader2 className="w-3 h-3 text-blue-400 animate-spin" />
                        )}
                        {state.status === 'error' && (
                          <XCircle className="w-3 h-3 text-red-400" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Name & Trigger Word Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-white">Persona Name</label>
              <input
                type="text"
                value={name}
                onChange={(e) => handleNameChange(e.target.value)}
                placeholder="e.g., Gaming Avatar"
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                required
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-white">Trigger Word</label>
              <input
                type="text"
                value={triggerWord}
                onChange={(e) => setTriggerWord(e.target.value.toUpperCase())}
                placeholder="e.g., GAMINGTOK"
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                required
              />
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-white">Description (Optional)</label>
            <input
              type="text"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Brief description of this persona"
              className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
          </div>

          {/* Category & Default Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-end">
            <div className="space-y-2">
              <label className="text-sm font-medium text-white">Category</label>
              <select
                value={category}
                onChange={(e) => setCategory(e.target.value as Persona['category'])}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="business">Business</option>
                <option value="gaming">Gaming</option>
                <option value="casual">Casual</option>
                <option value="professional">Professional</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div className="flex items-center space-x-2 pb-2">
              <input
                type="checkbox"
                id="is-default"
                checked={isDefault}
                onChange={(e) => setIsDefault(e.target.checked)}
                className="rounded border-slate-600 text-purple-600 focus:ring-purple-500"
              />
              <label htmlFor="is-default" className="text-sm text-white">
                Set as default persona
              </label>
            </div>
          </div>

          {/* Persona Status Info */}
          <div className="mb-4 p-4 bg-slate-700/50 rounded-lg border border-slate-600">
            <h4 className="text-sm font-medium text-white mb-2">📊 Training Status Information</h4>
            <div className="space-y-2 text-xs text-slate-300">
              <div className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-green-300 rounded-full"></span>
                <span><strong>Pending:</strong> Persona created, ready to start training</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></span>
                <span><strong>Training:</strong> LoRA model being trained (~15-20 min)</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-green-300 rounded-full"></span>
                <span><strong>Ready:</strong> LoRA model trained and ready for use</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-red-400 rounded-full"></span>
                <span><strong>Failed:</strong> Training failed, can retry</span>
              </div>
            </div>
          </div>

          {/* Create & Train Button */}
          <Button
            type="button"
            onClick={handleCreateAndTrain}
            disabled={!canStartTraining || !name.trim() || !triggerWord.trim()}
            className={`w-full text-lg py-4 transition-all duration-300 ${
              canStartTraining 
                ? 'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700' 
                : 'bg-gradient-to-r from-gray-600 to-gray-500 cursor-not-allowed'
            }`}
          >
            {isTraining ? (
              <div className="flex items-center space-x-2">
                <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                <span>Starting LoRA Training...</span>
              </div>
            ) : isSubmitting ? (
              <div className="flex items-center space-x-2">
                <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                <span>Creating persona...</span>
              </div>
            ) : isProcessingImages || loadingImages.length > 0 ? (
              <div className="flex items-center space-x-2">
                <Loader2 className="w-5 h-5 animate-spin" />
                <span>Loading images... {loadedImages.length}/{imageStates.length}</span>
              </div>
            ) : !hasValidImages ? (
              <div className="flex items-center justify-center space-x-2">
                <ImageIcon className="w-5 h-5" />
                <span>Upload at least 10 images to continue</span>
              </div>
            ) : (
              <div className="flex items-center justify-center space-x-2">
                <Sparkles className="w-5 h-5" />
                <span>Create & Train LoRA Model</span>
              </div>
            )}
          </Button>

          {/* Help Text for Button State */}
          {!canStartTraining && (
            <div className="text-center">
              {!name.trim() && (
                <p className="text-xs text-green-300">⚠️ Enter a persona name</p>
              )}
              {!triggerWord.trim() && (
                <p className="text-xs text-green-300">⚠️ Enter a trigger word</p>
              )}
              {!hasValidImages && imageStates.length > 0 && (
                <p className="text-xs text-green-300">⚠️ Need at least 10 valid images ({loadedImages.length}/10 loaded)</p>
              )}
              {(isProcessingImages || loadingImages.length > 0) && (
                <p className="text-xs text-blue-400">📱 Please wait for images to finish loading...</p>
              )}
              {errorImages.length > 0 && (
                <p className="text-xs text-red-400">❌ Remove {errorImages.length} error image(s) or upload replacements</p>
              )}
            </div>
          )}

          {/* Training Info */}
          <div className="mt-3 p-3 bg-purple-900/20 border border-purple-600/30 rounded-lg">
            <div className="text-center text-sm text-purple-200">
              <p className="font-medium">🚀 Complete Training Workflow</p>
              <p className="text-xs text-purple-300 mt-1">
                Creates persona + starts LoRA training immediately
              </p>
              <div className="flex justify-center items-center space-x-4 mt-2 text-xs">
                <span>💰 Cost: $2-4 USD</span>
                <span>⏱️ Time: ~15-20 min</span>
                <span>📸 {loadedImages.length} images ready</span>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  )
} 