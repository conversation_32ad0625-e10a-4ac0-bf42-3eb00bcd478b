const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

async function setupDatabase() {
  console.log('🚀 Setting up Thumbnex Database...\n');
  
  // Check if environment variables are set
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseAnonKey) {
    console.log('❌ Supabase environment variables not set');
    console.log('\n📝 Please create .env.local file with:');
    console.log('NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co');
    console.log('NEXT_PUBLIC_SUPABASE_ANON_KEY=your.anon.key.here\n');
    process.exit(1);
  }

  console.log('🔄 Testing Supabase connection...');
  const supabase = createClient(supabaseUrl, supabaseAnonKey);

  try {
    // Test basic connection
    const { data, error } = await supabase.from('personas').select('count').limit(1);
    
    if (error) {
      if (error.message.includes('relation "personas" does not exist')) {
        console.log('⚠️ Personas table does not exist yet');
        console.log('🔧 Creating database schema...\n');
        await createDatabaseSchema(supabase);
      } else {
        console.log('❌ Supabase connection failed:', error.message);
        console.log('\n💡 Make sure your credentials are correct and your Supabase project is active.');
        process.exit(1);
      }
    } else {
      console.log('✅ Supabase connection successful!');
      console.log('✅ Personas table already exists');
      await testDatabaseOperations(supabase);
    }
  } catch (err) {
    console.log('❌ Connection test failed:', err.message);
    process.exit(1);
  }
}

async function createDatabaseSchema(supabase) {
  console.log('📋 Creating personas table...');
  
  // Read and execute the schema
  const schema = fs.readFileSync('database-schema.sql', 'utf8');
  const statements = schema.split(';').filter(stmt => stmt.trim());
  
  for (const statement of statements) {
    if (statement.trim()) {
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: statement.trim() + ';' });
        if (error) {
          console.log(`⚠️ SQL Statement may have failed: ${error.message}`);
          // Continue anyway as some statements might be expected to fail (like IF NOT EXISTS)
        }
      } catch (err) {
        console.log(`⚠️ Statement execution warning: ${err.message}`);
        // Continue with setup
      }
    }
  }
  
  console.log('✅ Database schema created successfully!');
  await testDatabaseOperations(supabase);
}

async function testDatabaseOperations(supabase) {
  console.log('\n🧪 Testing database operations...');
  
  try {
    // Test reading personas
    const { data: personas, error: readError } = await supabase
      .from('personas')
      .select('*')
      .limit(5);
    
    if (readError) {
      console.log('❌ Read test failed:', readError.message);
      return;
    }
    
    console.log(`✅ Read test passed - Found ${personas.length} personas`);
    
    // Test creating a test persona
    const testPersona = {
      id: 'test-setup-' + Date.now(),
      name: 'Setup Test Persona',
      description: 'Test persona created during setup',
      image_url: '/placeholder-thumbnail.svg',
      image_base64: '',
      category: 'test',
      is_default: false,
      usage_count: 0
    };
    
    const { error: insertError } = await supabase
      .from('personas')
      .insert([testPersona]);
    
    if (insertError) {
      console.log('❌ Write test failed:', insertError.message);
      return;
    }
    
    console.log('✅ Write test passed - Test persona created');
    
    // Clean up test persona
    await supabase.from('personas').delete().eq('id', testPersona.id);
    console.log('✅ Cleanup completed');
    
    console.log('\n🎉 Database setup completed successfully!');
    console.log('🚀 Your LoRA training data will now be stored persistently!');
    
  } catch (err) {
    console.log('❌ Database test failed:', err.message);
  }
}

// Run the setup
setupDatabase().catch(console.error); 