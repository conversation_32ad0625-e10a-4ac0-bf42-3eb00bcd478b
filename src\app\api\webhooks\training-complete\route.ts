import { NextRequest, NextResponse } from 'next/server'
import { loadPersonas, savePersonas, findPersonaById, savePersona } from '../../../../utils/personaStorage'
import { Persona } from '../../../../types/persona'
import { addWebhookNotification } from '../../../../utils/webhookNotifications'
import { supabase } from '../../../../lib/supabase'
import { cleanupTrainingZip } from '../../../../utils/loraTraining'
import crypto from 'crypto'

/**
 * Verify webhook signature from Replicate
 * Based on: https://replicate.com/docs/topics/webhooks#verifying-webhooks
 */
function verifyWebhookSignature(body: string, signature: string, secret: string): boolean {
  try {
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(body)
      .digest('hex')
    
    // Replicate sends signature in format: sha256=<hash>
    const providedSignature = signature.replace('sha256=', '')
    
    return crypto.timingSafeEqual(
      Buffer.from(expectedSignature, 'hex'),
      Buffer.from(providedSignature, 'hex')
    )
  } catch (error) {
    console.error('❌ Webhook signature verification failed:', error)
    return false
  }
}

/**
 * Store webhook event in database for tracking and processing
 */
async function storeWebhookEvent(payload: any, eventType: string, relatedId?: string) {
  try {
    if (!supabase) {
      console.error('❌ Supabase client not initialized')
      return null
    }

    const { data, error } = await supabase
      .from('webhook_events')
      .insert({
        event_type: eventType,
        source: 'replicate',
        payload,
        related_id: relatedId,
        signature_verified: true,
        processed: false
      })
      .select()
      .single()

    if (error) {
      console.error('❌ Failed to store webhook event:', error)
      return null
    }

    console.log('📝 Webhook event stored:', data.id)
    return data
  } catch (error) {
    console.error('❌ Error storing webhook event:', error)
    return null
  }
}

/**
 * Update training job in database
 */
async function updateTrainingJob(trainingId: string, updates: any) {
  try {
    if (!supabase) {
      console.error('❌ Supabase client not initialized')
      return null
    }

    const { data, error } = await supabase
      .from('training_jobs')
      .update(updates)
      .eq('replicate_training_id', trainingId)
      .select()
      .single()

    if (error) {
      console.error('❌ Failed to update training job:', error)
      return null
    }

    console.log('📊 Training job updated:', data.id)
    return data
  } catch (error) {
    console.error('❌ Error updating training job:', error)
    return null
  }
}

/**
 * Mark webhook as processed
 */
async function markWebhookProcessed(webhookId: string, success: boolean, errorMessage?: string) {
  try {
    if (!supabase) {
      console.error('❌ Supabase client not initialized')
      return
    }

    const { error } = await supabase
      .from('webhook_events')
      .update({
        processed: true,
        processed_at: new Date().toISOString(),
        processing_error: errorMessage || null
      })
      .eq('id', webhookId)

    if (error) {
      console.error('❌ Failed to mark webhook as processed:', error)
    }
  } catch (error) {
    console.error('❌ Error marking webhook as processed:', error)
  }
}

/**
 * Safely cleanup training ZIP file after verifying Replicate has processed it
 */
async function safelyCleanupTrainingZip(status: string, zipUrl: string | undefined, trainingId: string, delayMinutes: number = 2) {
  if (!zipUrl) {
    console.log('ℹ️ No ZIP URL to cleanup')
    return
  }

  console.log(`🧹 Scheduling ZIP cleanup for training ${trainingId} in ${delayMinutes} minutes`)
  console.log(`📁 ZIP URL: ${zipUrl}`)
  
  // Schedule cleanup with appropriate delay based on status
  setTimeout(async () => {
    try {
      console.log(`🗑️ Starting ZIP cleanup for training ${trainingId}`)
      await cleanupTrainingZip(zipUrl)
      console.log(`✅ Successfully cleaned up ZIP file for training ${trainingId}`)
    } catch (error) {
      console.warn(`⚠️ Failed to cleanup ZIP file for training ${trainingId}:`, error)
      // Non-critical error - don't throw
    }
  }, delayMinutes * 60 * 1000)
}

/**
 * TEMPORARY REDIRECT: Old webhook endpoint → New v2 endpoint
 * This ensures webhooks work while the Replicate configuration is updated
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Redirecting old webhook to v2 endpoint...')
    
    // Get all headers and body
    const body = await request.text()
    const headers: Record<string, string> = {}
    
    // Copy all headers
    request.headers.forEach((value, key) => {
      headers[key] = value
    })
    
    // Forward to v2 endpoint
    const baseUrl = request.url.split('/api')[0]
    const v2Response = await fetch(`${baseUrl}/api/webhooks/training-complete-v2`, {
      method: 'POST',
      headers: headers,
      body: body
    })
    
    const result = await v2Response.text()
    
    console.log(`✅ Webhook redirected to v2: ${v2Response.status}`)
    
    return new NextResponse(result, {
      status: v2Response.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })
    
  } catch (error) {
    console.error('❌ Webhook redirect failed:', error)
    return NextResponse.json(
      { error: 'Webhook redirect failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
} 