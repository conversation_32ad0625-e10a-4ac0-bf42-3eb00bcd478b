import { NextRequest, NextResponse } from 'next/server';
import Replicate from 'replicate';

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN!,
});

export async function POST(request: NextRequest) {
  try {
    const { modelUrl, prompt } = await request.json();
    
    console.log('🧪 Testing LoRA model directly...');
    console.log('🎯 Model URL:', modelUrl);
    console.log('📝 Prompt:', prompt);
    
    const output = await replicate.run(
      modelUrl,
      {
        input: {
          prompt: prompt,
          model: "dev",
          go_fast: false,
          lora_scale: 1,
          megapixels: "1",
          num_outputs: 1,
          aspect_ratio: "16:9",
          output_format: "png",
          guidance_scale: 3,
          output_quality: 80,
          prompt_strength: 0.8,
          extra_lora_scale: 1,
          num_inference_steps: 28,
          disable_safety_checker: true
        }
      }
    );
    
    console.log('🔍 Raw output:', output);
    console.log('🔍 Output type:', typeof output);
    console.log('🔍 Is array:', Array.isArray(output));
    
    if (Array.isArray(output) && output.length > 0) {
      const firstItem = output[0];
      console.log('🔍 First item type:', typeof firstItem);
      console.log('🔍 First item has url method:', typeof firstItem?.url);
      
      if (typeof firstItem?.url === 'function') {
        const imageUrl = firstItem.url();
        console.log('✅ Got URL from .url():', imageUrl);
        return NextResponse.json({ success: true, imageUrl, rawOutput: output });
      } else {
        console.log('❌ First item does not have .url() method');
        return NextResponse.json({ success: false, error: 'No .url() method', rawOutput: output });
      }
    }
    
    return NextResponse.json({ success: false, error: 'Unexpected output format', rawOutput: output });
    
  } catch (error) {
    console.error('❌ LoRA test error:', error);
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error',
      details: error
    }, { status: 500 });
  }
} 