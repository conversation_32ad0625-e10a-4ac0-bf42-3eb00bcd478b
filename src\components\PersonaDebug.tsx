import React from 'react'
import { Persona } from '@/types/persona'

interface PersonaDebugProps {
  persona: Persona | null
}

export function PersonaDebug({ persona }: PersonaDebugProps) {
  if (!persona) {
    return (
      <div className="p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
        <h3 className="font-bold text-lg mb-2">🔍 Persona Debug</h3>
        <p>No persona selected</p>
      </div>
    )
  }

  return (
    <div className="p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
      <h3 className="font-bold text-lg mb-2">🔍 Persona Debug: {persona.name}</h3>
      
      <div className="space-y-2 text-sm">
        <div>
          <strong>ID:</strong> {persona.id}
        </div>
        
        <div>
          <strong>Has LoRA Training:</strong> {persona.loraTraining ? '✅ Yes' : '❌ No'}
        </div>
        
        {persona.loraTraining && (
          <div className="ml-4 space-y-1">
            <div>
              <strong>Status:</strong> {persona.loraTraining.status}
            </div>
            <div>
              <strong>Trigger Word:</strong> {persona.loraTraining.triggerWord || 'None'}
            </div>
            <div>
              <strong>Training Images:</strong> {persona.loraTraining.trainingImages?.length || 0}
            </div>
            <div>
              <strong>Training ID:</strong> {persona.loraTraining.trainingId || 'None'}
            </div>
            <div>
              <strong>Model URL:</strong> {persona.loraTraining.modelUrl || 'None'}
            </div>
          </div>
        )}
        
        <div>
          <strong>Should Show Training Button:</strong> {
            persona.loraTraining && 
            persona.loraTraining.status === 'pending' && 
            persona.loraTraining.trainingImages && 
            persona.loraTraining.trainingImages.length >= 10
              ? '✅ Yes' 
              : '❌ No'
          }
        </div>
      </div>
    </div>
  )
} 