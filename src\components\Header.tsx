'use client'

import { But<PERSON> } from '@/components/ui/Button'
import { User, Coins } from 'lucide-react'

export function Header() {
  return (
    <header className="sticky top-0 z-50 w-full border-b border-border-primary bg-bg-secondary/80 backdrop-blur-md">
      <div className="container mx-auto flex h-16 items-center justify-between px-4">
        {/* Logo */}
        <div className="flex items-center space-x-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-accent-primary">
            <span className="text-sm font-bold text-white">T</span>
          </div>
          <span className="text-xl font-bold text-text-primary">Thumbnex</span>
        </div>

        {/* Credits and Profile */}
        <div className="flex items-center space-x-4">
          {/* Credits Display */}
          <div className="flex items-center space-x-2 rounded-lg bg-bg-tertiary px-3 py-2">
            <Coins className="h-4 w-4 text-white" />
            <span className="text-sm font-medium text-text-primary">47</span>
            <span className="text-xs text-text-secondary">credits</span>
          </div>

          {/* Profile Button */}
          <Button
            variant="ghost"
            size="sm"
            className="flex items-center space-x-2"
          >
            <User className="h-4 w-4" />
            <span className="hidden sm:inline">Profile</span>
          </Button>
        </div>
      </div>
    </header>
  )
} 