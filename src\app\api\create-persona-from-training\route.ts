import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '../../../lib/supabase'

export async function POST(request: NextRequest) {
  try {
    console.log('🎯 POST /api/create-persona-from-training called')
    
    const { personaName, triggerWord, trainingId, modelUrl, zipUrl, thumbnailImage } = await request.json()
    
    // Validate required parameters
    if (!personaName || !triggerWord || !trainingId) {
      return NextResponse.json({
        error: 'Missing required parameters',
        required: ['personaName', 'triggerWord', 'trainingId']
      }, { status: 400 })
    }
    
    console.log('📋 Creating persona from training:', {
      personaName,
      triggerWord,
      trainingId,
      modelUrl: modelUrl ? modelUrl.substring(0, 50) + '...' : 'none',
      hasZipUrl: !!zipUrl
    })
    
    // Generate unique persona ID
    const personaId = `persona-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`
    
    // Create persona with proper lora_training structure
    const loraTraining = {
      status: modelUrl ? 'completed' : 'training',
      trainingId: trainingId,
      modelUrl: modelUrl || null,
      triggerWord: triggerWord,
      trainingProgress: modelUrl ? 100 : 0,
      createdAt: new Date().toISOString(),
      completedAt: modelUrl ? new Date().toISOString() : null,
      trainingZipUrl: zipUrl || null  // Store ZIP URL for webhook cleanup
    }
    
    // Check if Supabase is configured
    if (!supabase) {
      console.error('❌ Supabase client not configured')
      return NextResponse.json({
        error: 'Database connection not available',
        details: 'Supabase environment variables not configured'
      }, { status: 500 })
    }

    // Insert persona into database
    const { data: persona, error: personaError } = await supabase
      .from('personas')
      .insert({
        id: personaId,
        name: personaName,
        description: `LoRA-trained persona with trigger word: ${triggerWord}`,
        image_base64: thumbnailImage || '',
        image_url: thumbnailImage || '/placeholder-thumbnail.svg',
        model_url: modelUrl || null,
        trigger_word: triggerWord,
        lora_training: loraTraining,
        category: 'other',
        is_default: false,
        usage_count: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        last_used: new Date().toISOString()
      })
      .select()
      .single()
    
    if (personaError) {
      console.error('❌ Persona creation error:', personaError)
      throw new Error(`Failed to save persona: ${personaError.message}`)
    }
    
    console.log('✅ Persona created:', personaId)

    // Create training job entry (supabase is already checked above)
    // This is CRITICAL for webhook processing - the webhook looks for this record!
    const { data: trainingJob, error: trainingError } = await supabase
      .from('training_jobs')
      .insert({
        persona_id: personaId,
        replicate_training_id: trainingId,
        trainable_type: 'persona',
        status: modelUrl ? 'completed' : 'training',
        progress: modelUrl ? 100 : 0,
        trigger_word: triggerWord,
        model_url: modelUrl || null,
        training_images_count: 0, // Will be updated if available
        training_cost: 0.00, // Will be updated from webhook
        started_at: new Date().toISOString(),
        completed_at: modelUrl ? new Date().toISOString() : null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        webhook_data: {}  // Will store webhook payload when received
      })
      .select()
      .single()
    
    if (trainingError) {
      console.error('❌ Training job creation error:', trainingError)
      // This is critical for webhook processing, but don't fail the whole operation
      console.error('⚠️ WARNING: Training job creation failed - webhook processing may not work!')
    } else {
      console.log('✅ Training job created:', trainingJob.id)
    }
    
    console.log('✅ Successfully created persona with training job linkage:', personaId)
    
    return NextResponse.json({
      success: true,
      persona: {
        id: persona.id,
        name: persona.name,
        triggerWord: persona.trigger_word,
        modelUrl: persona.model_url,
        ready: !!persona.model_url,
        status: persona.model_url ? 'completed' : 'training'
      },
      trainingJob: trainingJob ? {
        id: trainingJob.id,
        status: trainingJob.status,
        progress: trainingJob.progress,
        replicateTrainingId: trainingJob.replicate_training_id
      } : null,
      webhook: {
        configured: true,
        note: 'Webhook will automatically update this persona when training completes'
      },
      message: 'Persona created successfully from training data. Webhook will update when training completes.'
    })
    
  } catch (error) {
    console.error('❌ Error creating persona from training:', error)
    return NextResponse.json({
      error: 'Failed to create persona from training',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Create persona from training endpoint',
    method: 'POST',
    requiredFields: ['personaName', 'triggerWord', 'trainingId'],
    optionalFields: ['thumbnailImage', 'modelUrl', 'zipUrl'],
    webhook: {
      supported: true,
      automatic: true,
      note: 'Training completion will automatically update the persona via webhook'
    }
  })
} 