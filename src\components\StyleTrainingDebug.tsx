'use client'

import React, { useState } from 'react'
import { Upload, AlertCircle, CheckCircle, FileImage, Info, Palette, User, BarChart3 } from 'lucide-react'
import { validateTrainingImages } from '../utils/clientZipUpload'

export function StyleTrainingDebug() {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [styleName, setStyleName] = useState('')
  const [triggerWord, setTriggerWord] = useState('')
  const [debugInfo, setDebugInfo] = useState<any>(null)

  const handleFileSelection = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    setSelectedFiles(files)
    
    // Debug the validation process
    const validation = validateTrainingImages(files, 'style')
    
    const debug = {
      totalFiles: files.length,
      fileDetails: files.map(file => ({
        name: file.name,
        size: `${(file.size / 1024 / 1024).toFixed(2)}MB`,
        type: file.type,
        isValidType: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'].includes(file.type),
        sizeOk: file.size >= 1024 && file.size <= 10 * 1024 * 1024
      })),
      validation: {
        valid: validation.valid,
        errors: validation.errors,
        validImageFiles: validation.imageFiles.length
      },
      canStartTraining: files.length >= 3 && files.length <= 6 && styleName && triggerWord
    }
    
    setDebugInfo(debug)
  }

  const generateTriggerWord = () => {
    if (styleName) {
      const sanitized = styleName.toUpperCase().replace(/[^A-Z0-9]/g, '')
      const random = Math.floor(Math.random() * 999).toString().padStart(3, '0')
      setTriggerWord(`STYLE${sanitized.substring(0, 3)}${random}`)
    }
  }

  const testPersonaTraining = () => {
    console.log('🎭 Testing Persona Training Access...')
    window.dispatchEvent(new CustomEvent('navigateToTraining', { 
      detail: { openNewTraining: true, trainingType: 'persona' } 
    }))
  }

  const testStyleTraining = () => {
    console.log('🎨 Testing Style Training Access...')
    window.dispatchEvent(new CustomEvent('navigateToTraining', { 
      detail: { openNewTraining: true, trainingType: 'style' } 
    }))
  }

  const testDefaultTraining = () => {
    console.log('📊 Testing Default Training Access (should default to persona)...')
    window.dispatchEvent(new CustomEvent('navigateToTraining', { 
      detail: { openNewTraining: true } 
    }))
  }

  return (
    <div className="bg-slate-800 rounded-lg p-6 border border-slate-600 max-w-4xl mx-auto">
      <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
        <FileImage className="w-5 h-5 text-blue-400" />
        Style Training Debug Tool
      </h3>
      
      <div className="space-y-6">
        {/* Training Access Test Buttons */}
        <div className="bg-slate-900/50 rounded-lg p-4 border border-slate-700">
          <h4 className="text-lg font-medium text-white mb-3 flex items-center gap-2">
            <Info className="w-4 h-4" />
            Training Access Test (Which button are you clicking?)
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={testPersonaTraining}
              className="flex items-center gap-3 p-4 bg-blue-900/30 border border-blue-600/50 rounded-lg hover:bg-blue-900/50 transition-colors"
            >
              <User className="w-5 h-5 text-blue-400" />
              <div className="text-left">
                <div className="text-white font-medium">Persona Training</div>
                <div className="text-xs text-blue-300">Should open persona mode</div>
              </div>
            </button>
            
            <button
              onClick={testStyleTraining}
              className="flex items-center gap-3 p-4 bg-purple-900/30 border border-purple-600/50 rounded-lg hover:bg-purple-900/50 transition-colors"
            >
              <Palette className="w-5 h-5 text-purple-400" />
              <div className="text-left">
                <div className="text-white font-medium">Style Training</div>
                <div className="text-xs text-purple-300">Should open style mode</div>
              </div>
            </button>
            
            <button
              onClick={testDefaultTraining}
              className="flex items-center gap-3 p-4 bg-orange-900/30 border border-orange-600/50 rounded-lg hover:bg-orange-900/50 transition-colors"
            >
              <BarChart3 className="w-5 h-5 text-orange-400" />
              <div className="text-left">
                <div className="text-white font-medium">Default Training</div>
                <div className="text-xs text-orange-300">Defaults to persona mode</div>
              </div>
            </button>
          </div>
          
          <div className="mt-4 p-3 bg-green-900/20 border border-green-600/50 rounded-lg">
            <div className="text-green-200 text-sm">
              <p className="font-medium mb-2">🔍 How to access Style Training correctly:</p>
                             <ul className="space-y-1 text-green-300 text-sm ml-4">
                 <li>• <strong>Sidebar:</strong> Click &quot;Train Style&quot; button (purple palette icon)</li>
                 <li>• <strong>Style Selector:</strong> Click &quot;Create New Style&quot; in style dropdown</li>
                 <li>• <strong>Don&apos;t use:</strong> General &quot;Training&quot; button (defaults to persona mode)</li>
               </ul>
            </div>
          </div>
        </div>

        {/* File Selection */}
        <div>
          <label className="block text-sm font-medium text-white mb-2">
            Select 4 Training Images (3-6 required for style training)
          </label>
          <div className="relative">
            <input
              type="file"
              multiple
              accept="image/*"
              onChange={handleFileSelection}
              className="hidden"
              id="debug-files"
            />
            <label
              htmlFor="debug-files"
              className="flex items-center justify-center gap-2 w-full px-4 py-6 border-2 border-dashed border-slate-600 rounded-lg hover:border-slate-500 cursor-pointer transition-colors"
            >
              <Upload className="w-5 h-5 text-slate-400" />
              <span className="text-slate-300">
                {selectedFiles.length > 0 
                  ? `${selectedFiles.length} files selected`
                  : 'Select your 4 training images'
                }
              </span>
            </label>
          </div>
        </div>

        {/* Style Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-white mb-2">
              Style Name
            </label>
            <input
              type="text"
              value={styleName}
              onChange={(e) => setStyleName(e.target.value)}
              placeholder="e.g., Retro Gaming"
              className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-white mb-2">
              Trigger Word
            </label>
            <div className="flex gap-2">
              <input
                type="text"
                value={triggerWord}
                onChange={(e) => setTriggerWord(e.target.value.toUpperCase())}
                placeholder="e.g., STYLE123"
                className="flex-1 px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                onClick={generateTriggerWord}
                disabled={!styleName}
                className="px-3 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-slate-600 text-white rounded-lg transition-colors"
              >
                Generate
              </button>
            </div>
          </div>
        </div>

        {/* Debug Information */}
        {debugInfo && (
          <div className="bg-slate-900/50 rounded-lg p-4 border border-slate-700">
            <h4 className="text-lg font-medium text-white mb-3 flex items-center gap-2">
              <Info className="w-4 h-4" />
              Debug Information
            </h4>
            
            <div className="space-y-4">
              {/* Overall Status */}
              <div className="flex items-center gap-2">
                {debugInfo.validation.valid ? (
                  <CheckCircle className="w-5 h-5 text-green-400" />
                ) : (
                  <AlertCircle className="w-5 h-5 text-red-400" />
                )}
                <span className={`font-medium ${debugInfo.validation.valid ? 'text-green-400' : 'text-red-400'}`}>
                  File Validation: {debugInfo.validation.valid ? 'PASSED' : 'FAILED'}
                </span>
              </div>

              {/* File Count Check */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="bg-slate-800 p-3 rounded">
                  <div className="text-slate-300">Total Files</div>
                  <div className={`font-mono text-lg ${debugInfo.totalFiles >= 3 && debugInfo.totalFiles <= 6 ? 'text-green-400' : 'text-red-400'}`}>
                    {debugInfo.totalFiles}
                  </div>
                </div>
                
                <div className="bg-slate-800 p-3 rounded">
                  <div className="text-slate-300">Valid Images</div>
                  <div className={`font-mono text-lg ${debugInfo.validation.validImageFiles >= 3 && debugInfo.validation.validImageFiles <= 6 ? 'text-green-400' : 'text-red-400'}`}>
                    {debugInfo.validation.validImageFiles}
                  </div>
                </div>
                
                <div className="bg-slate-800 p-3 rounded">
                  <div className="text-slate-300">Style Name</div>
                  <div className={`font-mono text-lg ${styleName ? 'text-green-400' : 'text-red-400'}`}>
                    {styleName ? '✓' : '✗'}
                  </div>
                </div>
                
                <div className="bg-slate-800 p-3 rounded">
                  <div className="text-slate-300">Trigger Word</div>
                  <div className={`font-mono text-lg ${triggerWord ? 'text-green-400' : 'text-red-400'}`}>
                    {triggerWord ? '✓' : '✗'}
                  </div>
                </div>
              </div>

              {/* File Details */}
              {debugInfo.fileDetails.length > 0 && (
                <div>
                  <h5 className="text-white font-medium mb-2">File Details:</h5>
                  <div className="space-y-2">
                    {debugInfo.fileDetails.map((file: any, index: number) => (
                      <div key={index} className="flex items-center justify-between bg-slate-800 p-2 rounded text-sm">
                        <span className="text-slate-300">{file.name}</span>
                        <div className="flex items-center gap-4">
                          <span className="text-slate-400">{file.size}</span>
                          <span className={`px-2 py-1 rounded text-xs ${file.isValidType ? 'bg-green-900 text-green-300' : 'bg-red-900 text-red-300'}`}>
                            {file.type}
                          </span>
                          <span className={`px-2 py-1 rounded text-xs ${file.sizeOk ? 'bg-green-900 text-green-300' : 'bg-red-900 text-red-300'}`}>
                            {file.sizeOk ? 'Size OK' : 'Size Issue'}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Validation Errors */}
              {debugInfo.validation.errors.length > 0 && (
                <div>
                  <h5 className="text-red-400 font-medium mb-2">Validation Issues:</h5>
                  <div className="space-y-1">
                    {debugInfo.validation.errors.map((error: string, index: number) => (
                      <div key={index} className="flex items-start gap-2 text-sm">
                        <AlertCircle className="w-4 h-4 text-red-400 mt-0.5 flex-shrink-0" />
                        <span className="text-red-300">{error}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Training Ready Status */}
              <div className={`p-3 rounded border ${debugInfo.canStartTraining ? 'bg-green-900/30 border-green-600/50' : 'bg-red-900/30 border-red-600/50'}`}>
                <div className="flex items-center gap-2">
                  {debugInfo.canStartTraining ? (
                    <CheckCircle className="w-5 h-5 text-green-400" />
                  ) : (
                    <AlertCircle className="w-5 h-5 text-red-400" />
                  )}
                  <span className={`font-medium ${debugInfo.canStartTraining ? 'text-green-400' : 'text-red-400'}`}>
                    {debugInfo.canStartTraining ? 'Ready to Start Training!' : 'Training Blocked'}
                  </span>
                </div>
                {!debugInfo.canStartTraining && (
                  <div className="mt-2 text-sm text-slate-300">
                    <p className="font-medium mb-1">Requirements:</p>
                    <ul className="space-y-1">
                      <li className={debugInfo.validation.validImageFiles >= 3 && debugInfo.validation.validImageFiles <= 6 ? 'text-green-400' : 'text-red-400'}>
                        • {debugInfo.validation.validImageFiles >= 3 && debugInfo.validation.validImageFiles <= 6 ? '✓' : '✗'} 3-6 valid image files
                      </li>
                      <li className={styleName ? 'text-green-400' : 'text-red-400'}>
                        • {styleName ? '✓' : '✗'} Style name filled
                      </li>
                      <li className={triggerWord ? 'text-green-400' : 'text-red-400'}>
                        • {triggerWord ? '✓' : '✗'} Trigger word filled
                      </li>
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
} 