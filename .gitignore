# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Next.js
.next/
out/
build/
dist/

# Environment Variables (IMPORTANT - Contains API keys!)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel

# User Uploads (Large files, user data)
public/uploads/
public/uploads/**/*
data/personas.json

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Temporary files
temp/
tmp/
*.tmp

# Database files (if any)
*.db
*.sqlite
*.sqlite3

# Cache files
.cache/
.parcel-cache/

# Misc
*.tgz
*.tar.gz
.eslintcache

# Local development files
test-*.js
fix-*.js
check-*.js
update-*.js
