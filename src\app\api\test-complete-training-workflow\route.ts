import { NextRequest, NextResponse } from 'next/server'

/**
 * Test Complete Training Workflow
 * This endpoint tests the entire training workflow:
 * 1. Starts training with webhook
 * 2. Creates persona record 
 * 3. Simulates webhook completion
 * 4. Verifies database updates
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Testing complete training workflow...')
    
    const { testPersonaName, testTriggerWord, testZipUrl } = await request.json()
    
    // Use test defaults if not provided
    const personaName = testPersonaName || `TestPersona-${Date.now()}`
    const triggerWord = testTriggerWord || `TEST${Date.now()}`
    const zipUrl = testZipUrl || 'https://example.com/test.zip'
    
    const baseUrl = request.url.includes('localhost') 
      ? 'https://thumbnex-ejan.vercel.app'
      : request.url.split('/api')[0]
    
    console.log('🚀 Step 1: Starting training with webhook...')
    
    // Step 1: Start training (this should now include webhook)
    const trainingResponse = await fetch(`${baseUrl}/api/train-lora-direct`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        zipUrl: zipUrl,
        triggerWord: triggerWord,
        personaName: personaName
      })
    })
    
    if (!trainingResponse.ok) {
      const error = await trainingResponse.text()
      return NextResponse.json({
        error: 'Training start failed',
        details: error,
        step: 1
      }, { status: 500 })
    }
    
    const trainingResult = await trainingResponse.json()
    console.log('✅ Training started:', trainingResult.trainingId)
    
    console.log('👤 Step 2: Creating persona record...')
    
    // Step 2: Create persona (this should create training job record for webhook)
    const personaResponse = await fetch(`${baseUrl}/api/create-persona-from-training`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        personaName: personaName,
        triggerWord: triggerWord,
        trainingId: trainingResult.trainingId,
        zipUrl: zipUrl
        // No modelUrl - should be 'training' status
      })
    })
    
    if (!personaResponse.ok) {
      const error = await personaResponse.text()
      return NextResponse.json({
        error: 'Persona creation failed',
        details: error,
        step: 2
      }, { status: 500 })
    }
    
    const personaResult = await personaResponse.json()
    console.log('✅ Persona created:', personaResult.persona.id)
    
    console.log('🔔 Step 3: Simulating webhook completion...')
    
    // Step 3: Simulate webhook (testing webhook processing)
    const mockWebhookPayload = {
      id: trainingResult.trainingId,
      type: 'training',
      status: 'succeeded',
      destination: trainingResult.destination,
      output: {
        version: `${trainingResult.destination}:abc123def456`,
        weights: `https://replicate.delivery/weights/${trainingResult.trainingId}`
      },
      completed_at: new Date().toISOString(),
      created_at: new Date(Date.now() - 900000).toISOString(), // 15 minutes ago
      logs: 'Training completed successfully'
    }
    
    // Create a proper webhook signature (simplified for testing)
    const webhookSecret = process.env.REPLICATE_WEBHOOK_SECRET || 'test-secret'
    const crypto = require('crypto')
    const signature = crypto.createHmac('sha256', webhookSecret)
      .update(JSON.stringify(mockWebhookPayload))
      .digest('hex')
    
    const webhookResponse = await fetch(`${baseUrl}/api/webhooks/training-complete`, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'replicate-signature': `sha256=${signature}`
      },
      body: JSON.stringify(mockWebhookPayload)
    })
    
    if (!webhookResponse.ok) {
      const error = await webhookResponse.text()
      return NextResponse.json({
        error: 'Webhook processing failed',
        details: error,
        step: 3
      }, { status: 500 })
    }
    
    const webhookResult = await webhookResponse.json()
    console.log('✅ Webhook processed:', webhookResult)
    
    console.log('🔍 Step 4: Verifying final state...')
    
    // Step 4: Verify persona is now ready
    const verifyResponse = await fetch(`${baseUrl}/api/personas`)
    const personasData = await verifyResponse.json()
    const updatedPersona = personasData.personas?.find((p: any) => p.id === personaResult.persona.id)
    
    return NextResponse.json({
      success: true,
      workflow: {
        step1_training: {
          status: 'completed',
          trainingId: trainingResult.trainingId,
          webhookConfigured: trainingResult.webhook?.configured || false,
          webhookUrl: trainingResult.webhook?.url
        },
        step2_persona: {
          status: 'completed',
          personaId: personaResult.persona.id,
          initialStatus: personaResult.persona.status,
          trainingJobCreated: !!personaResult.trainingJob
        },
        step3_webhook: {
          status: 'completed',
          processed: webhookResult.success || false,
          simulatedPayload: mockWebhookPayload
        },
        step4_verification: {
          personaReady: updatedPersona?.model_url ? true : false,
          modelUrl: updatedPersona?.model_url,
          finalStatus: updatedPersona?.lora_training?.status
        }
      },
      testData: {
        personaName,
        triggerWord,
        trainingId: trainingResult.trainingId,
        personaId: personaResult.persona.id
      },
      verdict: updatedPersona?.model_url ? 'PASS - Complete workflow working!' : 'FAIL - Webhook processing incomplete'
    })
    
  } catch (error) {
    console.error('❌ Workflow test error:', error)
    return NextResponse.json({
      error: 'Workflow test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Complete Training Workflow Test',
    description: 'Tests the entire training workflow with webhooks',
    usage: 'POST with optional testPersonaName, testTriggerWord, testZipUrl',
    steps: [
      '1. Start training with webhook configuration',
      '2. Create persona record with training job linkage', 
      '3. Simulate webhook completion',
      '4. Verify persona is updated and ready'
    ]
  })
} 