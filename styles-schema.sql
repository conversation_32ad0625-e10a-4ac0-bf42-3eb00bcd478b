-- Supabase Database Schema for Thumbnex Style Storage
-- Run this SQL in your Supabase SQL Editor to create the styles table

-- Create the styles table
CREATE TABLE IF NOT EXISTS styles (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  category TEXT NOT NULL,
  description TEXT,
  image_url TEXT NOT NULL,
  image_base64 TEXT NOT NULL, -- One preview image
  tags TEXT[] DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  usage_count INTEGER DEFAULT 0,
  is_default BOOLEAN DEFAULT FALSE,
  -- LoRA training fields (same as personas but for style training)
  lora_training JSONB
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_styles_created_at ON styles(created_at);
CREATE INDEX IF NOT EXISTS idx_styles_is_default ON styles(is_default);
CREATE INDEX IF NOT EXISTS idx_styles_usage_count ON styles(usage_count);
CREATE INDEX IF NOT EXISTS idx_styles_category ON styles(category);

-- Enable Row Level Security (RLS) for security
ALTER TABLE styles ENABLE ROW LEVEL SECURITY;

-- Create policy to allow all operations for now
-- In production, you might want to restrict this based on user authentication
CREATE POLICY "Allow all operations on styles" ON styles
  FOR ALL USING (true);

-- Insert default styles from existing data/styles.json
INSERT INTO styles (
  id, name, category, description, image_url, image_base64, 
  tags, is_default, usage_count
) VALUES 
(
  'gaming-1',
  'Gaming Action',
  'gaming',
  'High-energy gaming style with neon colors and dramatic lighting',
  'https://images.unsplash.com/photo-1542751371-adc38448a05e?w=400&h=225&fit=crop',
  '', -- Will be populated later with actual preview images
  ARRAY['neon', 'dramatic', 'action', 'colorful'],
  true,
  0
),
(
  'tech-1',
  'Tech Review',
  'tech',
  'Clean, professional tech review style with modern aesthetics',
  'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=225&fit=crop',
  '',
  ARRAY['clean', 'professional', 'modern', 'minimal'],
  true,
  0
),
(
  'vlog-1',
  'Lifestyle Vlog',
  'vlog',
  'Warm, casual vlog style with natural lighting and cozy aesthetics',
  'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=225&fit=crop',
  '',
  ARRAY['warm', 'casual', 'natural', 'cozy'],
  true,
  0
),
(
  'educational-1',
  'Educational',
  'educational',
  'Clear, informative style perfect for tutorials and educational content',
  'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=400&h=225&fit=crop',
  '',
  ARRAY['clear', 'informative', 'academic', 'readable'],
  true,
  0
),
(
  'fitness-1',
  'Fitness Motivation',
  'fitness',
  'High-energy fitness style with bold colors and motivational aesthetics',
  'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=225&fit=crop',
  '',
  ARRAY['energetic', 'bold', 'motivational', 'dynamic'],
  true,
  0
)
ON CONFLICT (id) DO NOTHING;

-- Function to automatically update the updated_at column
CREATE OR REPLACE FUNCTION update_styles_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at when row is modified
CREATE TRIGGER update_styles_updated_at 
  BEFORE UPDATE ON styles 
  FOR EACH ROW 
  EXECUTE FUNCTION update_styles_updated_at_column();

-- =============================================
-- STYLE TRAINING JOBS TABLE
-- =============================================

-- Create style_training_jobs table for tracking LoRA training
CREATE TABLE IF NOT EXISTS style_training_jobs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  style_id TEXT NOT NULL REFERENCES styles(id) ON DELETE CASCADE,
  replicate_training_id TEXT NOT NULL UNIQUE,
  status TEXT NOT NULL CHECK (status IN ('pending', 'training', 'completed', 'failed')) DEFAULT 'pending',
  progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
  training_images_count INTEGER NOT NULL,
  trigger_word TEXT NOT NULL,
  model_url TEXT,
  training_cost DECIMAL(10,2) DEFAULT 0.00,
  estimated_completion TIMESTAMPTZ,
  started_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  error_message TEXT,
  webhook_data JSONB DEFAULT '{}'::jsonb
);

-- Create indexes for style_training_jobs
CREATE INDEX IF NOT EXISTS idx_style_training_jobs_style_id ON style_training_jobs(style_id);
CREATE INDEX IF NOT EXISTS idx_style_training_jobs_status ON style_training_jobs(status);
CREATE INDEX IF NOT EXISTS idx_style_training_jobs_replicate_id ON style_training_jobs(replicate_training_id);
CREATE INDEX IF NOT EXISTS idx_style_training_jobs_started_at ON style_training_jobs(started_at);

-- Enable RLS for style_training_jobs
ALTER TABLE style_training_jobs ENABLE ROW LEVEL SECURITY;

-- Create policy for style_training_jobs
CREATE POLICY "Allow all operations on style_training_jobs" ON style_training_jobs
  FOR ALL USING (true);

-- Trigger for style_training_jobs updated_at
CREATE TRIGGER update_style_training_jobs_updated_at 
  BEFORE UPDATE ON style_training_jobs 
  FOR EACH ROW 
  EXECUTE FUNCTION update_styles_updated_at_column();

-- =============================================
-- AUTOMATIC STATUS UPDATE TRIGGER FOR STYLES
-- =============================================

-- Function to automatically update style status when training job completes
CREATE OR REPLACE FUNCTION update_style_training_status()
RETURNS TRIGGER AS $$
BEGIN
  -- Only process status changes
  IF OLD.status IS DISTINCT FROM NEW.status THEN
    
    -- Update style LoRA training status when job completes
    IF NEW.status = 'completed' AND NEW.model_url IS NOT NULL THEN
      UPDATE styles 
      SET 
        lora_training = jsonb_set(
          jsonb_set(
            jsonb_set(
              COALESCE(lora_training, '{}'::jsonb),
              '{status}', 
              '"completed"'
            ),
            '{modelUrl}', 
            to_jsonb(NEW.model_url)
          ),
          '{completedAt}',
          to_jsonb(NEW.completed_at)
        ),
        updated_at = NOW()
      WHERE id = NEW.style_id;
      
    ELSIF NEW.status = 'failed' THEN
      UPDATE styles 
      SET 
        lora_training = jsonb_set(
          jsonb_set(
            COALESCE(lora_training, '{}'::jsonb),
            '{status}', 
            '"failed"'
          ),
          '{errorMessage}',
          to_jsonb(COALESCE(NEW.error_message, 'Training failed'))
        ),
        updated_at = NOW()
      WHERE id = NEW.style_id;
      
    ELSIF NEW.status = 'training' THEN
      UPDATE styles 
      SET 
        lora_training = jsonb_set(
          jsonb_set(
            COALESCE(lora_training, '{}'::jsonb),
            '{status}', 
            '"training"'
          ),
          '{trainingProgress}',
          to_jsonb(COALESCE(NEW.progress, 0))
        ),
        updated_at = NOW()
      WHERE id = NEW.style_id;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for automatic style status updates
CREATE TRIGGER update_style_on_training_status_change
  AFTER UPDATE ON style_training_jobs
  FOR EACH ROW 
  EXECUTE FUNCTION update_style_training_status(); 