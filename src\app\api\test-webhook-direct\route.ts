import { NextRequest, NextResponse } from 'next/server'

/**
 * Direct Webhook Test - No Signature Verification
 * This endpoint tests webhook accessibility without signature verification
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Direct webhook test called')
    
    const body = await request.text()
    const headers = Object.fromEntries(request.headers.entries())
    
    console.log('📋 Webhook test details:', {
      bodyLength: body.length,
      contentType: headers['content-type'],
      userAgent: headers['user-agent'],
      hasSignature: !!headers['replicate-signature'],
      signature: headers['replicate-signature']?.substring(0, 20) + '...' || 'none',
      bodyPreview: body.substring(0, 200) + '...'
    })
    
    let payload
    try {
      payload = JSON.parse(body)
      console.log('✅ Payload parsed successfully:', {
        id: payload.id,
        type: payload.type,
        status: payload.status
      })
    } catch (e) {
      console.error('❌ Failed to parse payload:', e)
      payload = null
    }
    
    return NextResponse.json({
      success: true,
      message: 'Webhook test endpoint accessible',
      received: {
        bodyLength: body.length,
        hasPayload: !!payload,
        hasSignature: !!headers['replicate-signature'],
        timestamp: new Date().toISOString()
      },
      payload: payload ? {
        id: payload.id,
        type: payload.type,
        status: payload.status
      } : null
    })
    
  } catch (error) {
    console.error('❌ Webhook test error:', error)
    return NextResponse.json({
      error: 'Webhook test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Direct webhook test endpoint',
    description: 'Tests webhook accessibility without signature verification',
    url: '/api/test-webhook-direct',
    method: 'POST',
    note: 'This endpoint accepts any POST request to test webhook accessibility'
  })
} 