import { NextRequest, NextResponse } from 'next/server'

/**
 * Webhook Simulation Test
 * Simulates a Replicate webhook for testing the new webhook handler
 */
export async function POST(request: NextRequest) {
  try {
    const { trainingId, status = 'succeeded' } = await request.json()
    
    if (!trainingId) {
      return NextResponse.json({
        error: 'trainingId required'
      }, { status: 400 })
    }
    
    // Get base URL
    const baseUrl = request.url.includes('localhost') 
      ? 'https://thumbnex-ejan.vercel.app'
      : request.url.split('/api')[0]
    
    // Create mock webhook payload matching Replicate's format
    const webhookPayload = {
      id: trainingId,
      status: status,
      type: 'training',
      created_at: new Date().toISOString(),
      completed_at: status === 'succeeded' ? new Date().toISOString() : null,
      error: status === 'failed' ? 'Simulated training failure' : null,
      output: status === 'succeeded' ? {
        version: `zeddora/trained-model-${Date.now()}:simulated-success-${trainingId.substring(0, 8)}`
      } : null,
      destination: status === 'succeeded' ? `zeddora/trained-model-${Date.now()}` : null,
      input: {
        trigger_word: 'SIMULATION',
        steps: 1000
      },
      logs: `Training ${status}. This is a simulated webhook for testing.`
    }
    
    // Generate a mock signature for testing (not secure, only for simulation)
    const mockSignature = 'sha256=test-signature-' + Date.now()
    
    console.log('🧪 Simulating webhook for training:', trainingId)
    console.log('📦 Payload:', webhookPayload)
    
    // Send the webhook to the new handler
    const webhookResponse = await fetch(`${baseUrl}/api/webhooks/training-complete-v2`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'replicate-signature': mockSignature
      },
      body: JSON.stringify(webhookPayload)
    })
    
    const webhookResult = await webhookResponse.json()
    
    return NextResponse.json({
      success: true,
      simulation: {
        trainingId,
        status,
        payload: webhookPayload,
        signature: mockSignature
      },
      webhookResponse: {
        status: webhookResponse.status,
        result: webhookResult
      },
      message: 'Webhook simulation completed'
    })
    
  } catch (error) {
    console.error('❌ Webhook simulation error:', error)
    return NextResponse.json({
      error: 'Simulation failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Webhook Simulation Test',
    usage: {
      endpoint: 'POST /api/test-webhook-simulation',
      body: {
        trainingId: 'string (required)',
        status: 'string (optional, default: "succeeded")'
      },
      description: 'Simulates a Replicate webhook to test the webhook handler'
    },
    examples: {
      success: {
        trainingId: 'ynr8zb3e6hrma0cqqfs8z3mt7c',
        status: 'succeeded'
      },
      failure: {
        trainingId: 'abc123def456',
        status: 'failed'
      }
    }
  })
} 