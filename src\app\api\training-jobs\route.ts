import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '../../../lib/supabase'

// GET - Fetch all training jobs with statistics
export async function GET(request: NextRequest) {
  try {
    console.log('📊 Fetching training jobs and statistics...')
    
    if (!supabase) {
      return NextResponse.json(
        { error: 'Database not initialized' },
        { status: 500 }
      )
    }

    // Fetch all training jobs with persona information
    const { data: jobs, error: jobsError } = await supabase
      .from('training_jobs')
      .select(`
        *,
        personas:persona_id (
          name,
          image_base64
        )
      `)
      .order('started_at', { ascending: false })

    if (jobsError) {
      console.error('❌ Error fetching training jobs:', jobsError)
      return NextResponse.json(
        { error: 'Failed to fetch training jobs' },
        { status: 500 }
      )
    }

    // Transform data to include persona information
    const transformedJobs = (jobs || []).map(job => ({
      ...job,
      persona_name: job.personas?.name || 'Unknown Persona',
      persona_image: job.personas?.image_base64 || null
    }))

    // Calculate statistics
    const totalJobs = transformedJobs.length
    const completedJobs = transformedJobs.filter(job => job.status === 'completed').length
    const failedJobs = transformedJobs.filter(job => job.status === 'failed').length
    const activeJobs = transformedJobs.filter(job => 
      job.status === 'training' || job.status === 'pending'
    ).length
    
    const totalCost = transformedJobs.reduce((sum, job) => sum + (job.training_cost || 0), 0)
    
    // Calculate average training time for completed jobs
    const completedJobsWithTime = transformedJobs.filter(job => 
      job.status === 'completed' && job.started_at && job.completed_at
    )
    
    let avgTrainingTime = 0
    if (completedJobsWithTime.length > 0) {
      const totalTime = completedJobsWithTime.reduce((sum, job) => {
        const start = new Date(job.started_at)
        const end = new Date(job.completed_at!)
        return sum + (end.getTime() - start.getTime())
      }, 0)
      avgTrainingTime = totalTime / completedJobsWithTime.length / (1000 * 60) // Convert to minutes
    }

    const stats = {
      totalJobs,
      completedJobs,
      failedJobs,
      activeJobs,
      totalCost,
      avgTrainingTime
    }

    console.log(`✅ Retrieved ${totalJobs} training jobs`)
    console.log(`📊 Stats: ${activeJobs} active, ${completedJobs} completed, ${failedJobs} failed`)

    return NextResponse.json({
      success: true,
      jobs: transformedJobs,
      stats,
      count: totalJobs
    })

  } catch (error) {
    console.error('❌ Error in training jobs API:', error)
    return NextResponse.json(
      { 
        error: 'Failed to fetch training jobs',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// POST - Create a new training job (called by train-lora API)
export async function POST(request: NextRequest) {
  try {
    console.log('🆕 Creating new training job...')
    
    if (!supabase) {
      return NextResponse.json(
        { error: 'Database not initialized' },
        { status: 500 }
      )
    }

    const body = await request.json()
    const {
      persona_id,
      replicate_training_id,
      training_images_count,
      trigger_word,
      training_cost = 3.00,
      estimated_completion
    } = body

    if (!persona_id || !replicate_training_id) {
      return NextResponse.json(
        { error: 'persona_id and replicate_training_id are required' },
        { status: 400 }
      )
    }

    const { data: job, error } = await supabase
      .from('training_jobs')
      .insert({
        persona_id,
        replicate_training_id,
        status: 'training',
        progress: 0,
        training_images_count,
        trigger_word,
        training_cost,
        estimated_completion,
        started_at: new Date().toISOString(),
        webhook_data: {}
      })
      .select()
      .single()

    if (error) {
      console.error('❌ Error creating training job:', error)
      return NextResponse.json(
        { error: 'Failed to create training job' },
        { status: 500 }
      )
    }

    console.log(`✅ Training job created: ${job.id}`)

    return NextResponse.json({
      success: true,
      job,
      message: 'Training job created successfully'
    })

  } catch (error) {
    console.error('❌ Error creating training job:', error)
    return NextResponse.json(
      { 
        error: 'Failed to create training job',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
} 