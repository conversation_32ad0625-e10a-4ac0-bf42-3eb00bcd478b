import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '../../../../lib/supabase'

// GET - Fetch specific training job details
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    console.log(`📊 Fetching training job details: ${id}`)
    
    if (!supabase) {
      return NextResponse.json(
        { error: 'Database not initialized' },
        { status: 500 }
      )
    }

    const { data: job, error } = await supabase
      .from('training_jobs')
      .select(`
        *,
        personas:persona_id (
          name,
          image_base64,
          description
        )
      `)
      .eq('id', id)
      .single()

    if (error) {
      console.error('❌ Error fetching training job:', error)
      return NextResponse.json(
        { error: 'Training job not found' },
        { status: 404 }
      )
    }

    // Transform data to include persona information
    const transformedJob = {
      ...job,
      persona_name: job.personas?.name || 'Unknown Persona',
      persona_image: job.personas?.image_base64 || null,
      persona_description: job.personas?.description || null
    }

    console.log(`✅ Retrieved training job: ${transformedJob.persona_name}`)

    return NextResponse.json({
      success: true,
      job: transformedJob
    })

  } catch (error) {
    console.error('❌ Error in training job details API:', error)
    return NextResponse.json(
      { 
        error: 'Failed to fetch training job details',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// DELETE - Delete a training job
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    console.log(`🗑️ Deleting training job: ${id}`)
    
    if (!supabase) {
      return NextResponse.json(
        { error: 'Database not initialized' },
        { status: 500 }
      )
    }

    // First check if the job exists and get its status
    const { data: job, error: fetchError } = await supabase
      .from('training_jobs')
      .select('status, replicate_training_id')
      .eq('id', id)
      .single()

    if (fetchError) {
      console.error('❌ Error finding training job:', fetchError)
      return NextResponse.json(
        { error: 'Training job not found' },
        { status: 404 }
      )
    }

    // Don't allow deletion of active training jobs
    if (job.status === 'training') {
      return NextResponse.json(
        { error: 'Cannot delete active training job. Please wait for completion or failure.' },
        { status: 400 }
      )
    }

    // Delete the training job record
    const { error: deleteError } = await supabase
      .from('training_jobs')
      .delete()
      .eq('id', id)

    if (deleteError) {
      console.error('❌ Error deleting training job:', deleteError)
      return NextResponse.json(
        { error: 'Failed to delete training job' },
        { status: 500 }
      )
    }

    console.log(`✅ Training job deleted: ${id}`)

    return NextResponse.json({
      success: true,
      message: 'Training job deleted successfully'
    })

  } catch (error) {
    console.error('❌ Error deleting training job:', error)
    return NextResponse.json(
      { 
        error: 'Failed to delete training job',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// PATCH - Update training job status or details
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const body = await request.json()
    console.log(`🔄 Updating training job: ${id}`)
    
    if (!supabase) {
      return NextResponse.json(
        { error: 'Database not initialized' },
        { status: 500 }
      )
    }

    const updates = {
      ...body,
      updated_at: new Date().toISOString()
    }

    const { data: job, error } = await supabase
      .from('training_jobs')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('❌ Error updating training job:', error)
      return NextResponse.json(
        { error: 'Failed to update training job' },
        { status: 500 }
      )
    }

    console.log(`✅ Training job updated: ${id}`)

    return NextResponse.json({
      success: true,
      job,
      message: 'Training job updated successfully'
    })

  } catch (error) {
    console.error('❌ Error updating training job:', error)
    return NextResponse.json(
      { 
        error: 'Failed to update training job',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
} 