# Thumbnex Storage Architecture Migration Plan

## Current System Analysis

**Personas (LoRA Training)**:
- ✅ Database schema exists in Supabase (`personas` table)
- ❌ Still using Vercel Blob for persona storage (causing current errors)
- ✅ Training workflow works via `/api/train-lora-direct`

**Styles**:
- ❌ Currently static JSON file (`data/styles.json`)
- ❌ No training capability yet
- ❌ No database storage

## Implementation Plan

### Phase 1: Migrate Personas to Supabase-Only Storage

**1. Fix Persona Storage Issues**
- Update `src/utils/personaStorage.ts` to prioritize Supabase over Vercel Blob
- Remove Blob fallback for persona metadata (keep only for temporary ZIP files)
- Store only ONE preview image in Supabase (base64 in `image_base64` field)

**2. Update Persona API**
- Modify `/api/personas/route.ts` to work purely with Supabase
- Remove Vercel Blob persona listing logic
- Keep only ZIP upload functionality for training

**3. Add ZIP Cleanup**
- After successful training, delete ZIP files from Vercel Blob
- Keep only the model URL and preview image

### Phase 2: Add Styles Table to Supabase

**1. Create Styles Database Schema**
```sql
CREATE TABLE styles (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  category TEXT NOT NULL,
  description TEXT,
  image_url TEXT NOT NULL,
  image_base64 TEXT NOT NULL, -- One preview image
  tags TEXT[] DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  usage_count INTEGER DEFAULT 0,
  is_default BOOLEAN DEFAULT FALSE,
  -- LoRA training fields (same as personas)
  lora_training JSONB
);
```

**2. Migrate Static Styles**
- Convert `data/styles.json` to database records
- Add placeholder images for existing styles
- Create `/api/styles` endpoint similar to `/api/personas`

### Phase 3: Unified Training System

**1. Create Shared Training Infrastructure**
- Both personas and styles use same training workflow
- Same ZIP creation and Replicate API calls
- Same webhook handling for completion

**2. Update Training Components**
- Modify `ClientSideTraining.tsx` to support both types
- Add type parameter: `type: 'persona' | 'style'`
- Update UI labels accordingly

**3. Style Training UI**
- Create `StyleTrainingModal` similar to persona training
- Add "Train Custom Style" buttons in style selector
- Same 10+ image requirement, different trigger words

### Phase 4: Storage Optimization

**1. Image Storage Strategy**
- **Supabase**: One preview image per persona/style (base64, ~50KB each)
- **Vercel Blob**: Temporary ZIP files only (deleted after training)
- **Replicate**: Stores trained model files permanently

**2. Data Structure**
```typescript
// Both personas and styles share this structure
interface TrainableItem {
  id: string
  name: string
  type: 'persona' | 'style'
  image_base64: string // ONE preview image only
  lora_training?: {
    status: 'pending' | 'training' | 'completed' | 'failed'
    modelUrl?: string
    triggerWord: string
    trainingId?: string
    // ... other training metadata
  }
}
```

### Phase 5: UI Updates

**1. Unified Selectors**
- `PersonaSelector` and `StyleSelector` use same pattern
- Both show training status and "Train" buttons
- Both navigate to training modal

**2. Generation Integration**
- Both personas and styles can have custom LoRA models
- Use appropriate trigger words in prompts
- Handle model URLs consistently

## Benefits of This Architecture

1. **Cost Efficient**: Only one preview image per item in database
2. **Fast Loading**: Database queries much faster than Blob listing
3. **Scalable**: Structured data, proper indexing
4. **Unified**: Same training system for both personas and styles
5. **Clean Storage**: Automatic ZIP cleanup, no permanent blob storage
6. **Consistent UX**: Same training flow for both content types

## Implementation Order

1. **Fix current persona storage** (immediate - fixes your current errors)
2. **Add styles database** (foundation for style training)
3. **Create unified training system** (enables style training)
4. **Add cleanup automation** (cost optimization)
5. **Polish UI/UX** (user experience improvements)

## Key Differences: Persona vs Style Training

### Persona Training
- `loraType: 'subject'` - Trains on faces/people
- Trigger word format: `PERSON123`, `GAMER456`
- Training images: 10-20 recommended (no hard limit, just guidance)
- Use case: Face consistency in thumbnails
- Cost: $2-4, Time: 15-20 minutes

### Style Training  
- `loraType: 'style'` - Trains on visual style/aesthetic
- Trigger word format: `STYLE123`, `RETRO456`
- Training images: 3-6 required (enforced validation)
- Use case: Consistent visual style across thumbnails
- Cost: $1-2, Time: 8-12 minutes (fewer images = faster training)

## Current Priority: Style Training Implementation

Starting with style training system to match persona functionality, with the key difference being the `loraType` parameter set to 'style' instead of 'subject' during training. 