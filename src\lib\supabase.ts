import { createClient } from '@supabase/supabase-js'

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

// Create Supabase client only if environment variables are set
export const supabase = supabaseUrl && supabaseAnonKey 
  ? createClient(supabaseUrl, supabaseAnonKey)
  : null

// Database types for type safety
export interface PersonaRow {
  id: string
  name: string
  description: string
  image_url: string
  image_base64: string
  created_at: string
  updated_at: string
  usage_count: number
  category: string
  is_default: boolean
  last_used: string
  lora_training: any // JSON column for LoRA training data
}

// Check if Supabase is configured
export function isSupabaseConfigured(): boolean {
  return !!(supabase && supabaseUrl && supabaseAnonKey)
}

// Helper function to check connection
export async function testSupabaseConnection(): Promise<boolean> {
  try {
    if (!isSupabaseConfigured() || !supabase) return false
    
    const { data, error } = await supabase
      .from('personas')
      .select('count')
      .limit(1)
    
    return !error
  } catch (error) {
    console.warn('Supabase connection failed:', error)
    return false
  }
} 