import { NextRequest, NextResponse } from 'next/server'

/**
 * Debug Webhook Delivery Endpoint
 * This endpoint helps diagnose webhook delivery issues by:
 * 1. Testing webhook URL accessibility
 * 2. Checking environment configuration
 * 3. Simulating webhook calls
 */

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action') || 'status'
    
    const baseUrl = request.url.split('/api')[0]
    const webhookUrl = `${baseUrl}/api/webhooks/training-complete-v2`
    
    const result: any = {
      timestamp: new Date().toISOString(),
      webhookUrl,
      baseUrl,
      action
    }
    
    // Check environment configuration
    result.environment = {
      replicateToken: !!process.env.REPLICATE_API_TOKEN,
      replicateUsername: !!process.env.REPLICATE_USERNAME,
      webhookSecret: !!process.env.REPLICATE_WEBHOOK_SECRET,
      vercelUrl: process.env.VERCEL_URL || 'not-set',
      nodeEnv: process.env.NODE_ENV
    }
    
    switch (action) {
      case 'test-webhook-url':
        // Test if our webhook URL is accessible
        try {
          const testResponse = await fetch(webhookUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'Webhook-Debug-Test'
            },
            body: JSON.stringify({
              test: true,
              message: 'Webhook accessibility test'
            })
          })
          
          result.webhookTest = {
            accessible: true,
            status: testResponse.status,
            statusText: testResponse.statusText,
            headers: Object.fromEntries(testResponse.headers.entries())
          }
        } catch (error) {
          result.webhookTest = {
            accessible: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          }
        }
        break
        
      case 'check-recent-trainings':
        // Check if recent trainings have webhook configured
        if (process.env.REPLICATE_API_TOKEN) {
          try {
            const response = await fetch('https://api.replicate.com/v1/trainings?limit=5', {
              headers: {
                'Authorization': `Token ${process.env.REPLICATE_API_TOKEN}`
              }
            })
            
            if (response.ok) {
              const data = await response.json()
              result.recentTrainings = data.results?.map((training: any) => ({
                id: training.id,
                status: training.status,
                created_at: training.created_at,
                webhook: training.webhook,
                webhook_completed: training.webhook_completed
              })) || []
            }
          } catch (error) {
            result.replicateError = error instanceof Error ? error.message : 'Unknown error'
          }
        }
        break
        
      case 'simulate-webhook':
        // Simulate a webhook call to our endpoint
        const simulatedPayload = {
          id: 'test-training-' + Date.now(),
          status: 'succeeded',
          output: {
            version: 'test-model-url'
          },
          completed_at: new Date().toISOString()
        }
        
        try {
          const simulateResponse = await fetch(webhookUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'webhook-id': 'test-webhook-id',
              'webhook-timestamp': Math.floor(Date.now() / 1000).toString(),
              'webhook-signature': 'test-signature',
              'User-Agent': 'Replicate-Webhook-Simulator'
            },
            body: JSON.stringify(simulatedPayload)
          })
          
          const responseText = await simulateResponse.text()
          
          result.webhookSimulation = {
            success: simulateResponse.ok,
            status: simulateResponse.status,
            response: responseText,
            payload: simulatedPayload
          }
        } catch (error) {
          result.webhookSimulation = {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          }
        }
        break
        
      default:
        result.availableActions = [
          'test-webhook-url',
          'check-recent-trainings', 
          'simulate-webhook'
        ]
        break
    }
    
    return NextResponse.json(result, { 
      headers: {
        'Cache-Control': 'no-cache'
      }
    })
    
  } catch (error) {
    return NextResponse.json({
      error: 'Debug endpoint failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    return NextResponse.json({
      message: 'Debug webhook received',
      timestamp: new Date().toISOString(),
      headers: Object.fromEntries(request.headers.entries()),
      body,
      userAgent: request.headers.get('user-agent')
    })
    
  } catch (error) {
    return NextResponse.json({
      error: 'Failed to process debug webhook',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
} 