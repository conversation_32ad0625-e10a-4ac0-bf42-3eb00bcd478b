import { useState, useEffect, useRef, useCallback } from 'react'

interface TrainingStatus {
  trainingId: string
  status: 'starting' | 'processing' | 'succeeded' | 'failed' | 'canceled'
  progress: number | null
  isComplete: boolean
  modelUrl: string | null
  error: string | null
  estimatedTime: string | null
  cached?: boolean
  cacheAge?: number
  startedAt?: number // Timestamp when training was added
  willStartPollingAt?: number // Timestamp when polling will start
  isWaitingToStartPolling?: boolean // Whether we're in the 1-minute wait period
}

interface UseTrainingPollingOptions {
  pollingInterval?: number // milliseconds, default 5 seconds
  initialDelayMinutes?: number // minutes to wait before starting polling, default 1
  onStatusUpdate?: (status: TrainingStatus) => void
  onComplete?: (status: TrainingStatus) => void
  onError?: (error: string) => void
}

/**
 * Hook for efficient training status polling
 * Waits 1 minute before starting to poll (since most trainings take 1-2 minutes)
 * Then polls every 5 seconds with intelligent caching for efficiency
 */
export function useTrainingPolling(options: UseTrainingPollingOptions = {}) {
  const {
    pollingInterval = 5000, // 5 seconds - reasonable interval
    initialDelayMinutes = 1, // Wait 1 minute before starting
    onStatusUpdate,
    onComplete,
    onError
  } = options

  const [activeTrainings, setActiveTrainings] = useState<Map<string, TrainingStatus>>(new Map())
  const [isPolling, setIsPolling] = useState(false)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const lastPollRef = useRef<number>(0)

  // Configuration - lighter since server handles main updates
  const POLLING_INTERVAL = 15000 // 15 seconds (lighter than before)
  const INITIAL_DELAY = 2000 // 2 seconds
  const WAITING_PERIOD = 60000 // 1 minute before starting polling

  // Poll a single training status
  const pollTrainingStatus = useCallback(async (trainingId: string, forceRefresh: boolean = false): Promise<TrainingStatus | null> => {
    try {
      const url = `/api/poll-training-status?trainingId=${trainingId}${forceRefresh ? '&forceRefresh=true' : ''}`
      const response = await fetch(url)
      const data = await response.json()

      if (!data.success) {
        console.error('❌ Polling failed:', data.error)
        onError?.(data.error)
        return null
      }

      const status: TrainingStatus = {
        trainingId: data.trainingId,
        status: data.status,
        progress: data.progress,
        isComplete: data.isComplete,
        modelUrl: data.modelUrl,
        error: data.error,
        estimatedTime: data.estimatedTime,
        cached: data.cached,
        cacheAge: data.cacheAge
      }

      const cacheInfo = status.cached ? ` (cached ${status.cacheAge}s)` : ' (fresh)'
      console.log(`📊 Training ${trainingId}: ${status.status}${status.progress ? ` (${status.progress}%)` : ''}${cacheInfo}`)

      return status
    } catch (error) {
      console.error('❌ Poll request failed:', error)
      onError?.(error instanceof Error ? error.message : 'Polling failed')
      return null
    }
  }, [onError])

  // Poll all active trainings (only those ready for polling)
  const pollAllTrainings = useCallback(async (forceRefresh: boolean = false) => {
    const now = Date.now()
    
    // Filter trainings that are ready to be polled (past the initial delay)
    const readyTrainings = Array.from(activeTrainings.entries()).filter(([_, training]) => {
      return !training.isWaitingToStartPolling && !training.isComplete
    })

    if (readyTrainings.length === 0) return

    console.log(`🔄 Polling ${readyTrainings.length} active trainings${forceRefresh ? ' (force refresh)' : ''}...`)
    lastPollRef.current = now

    const updatedTrainings = new Map(activeTrainings)
    const completedTrainings: string[] = []

    // Poll each ready training
    for (const [trainingId, currentStatus] of readyTrainings) {
      if (currentStatus.isComplete) continue

      const newStatus = await pollTrainingStatus(trainingId, forceRefresh)
      if (newStatus) {
        // Preserve timing information from the current status
        const updatedStatus = {
          ...newStatus,
          startedAt: currentStatus.startedAt,
          willStartPollingAt: currentStatus.willStartPollingAt,
          isWaitingToStartPolling: false
        }
        
        updatedTrainings.set(trainingId, updatedStatus)
        onStatusUpdate?.(updatedStatus)

        if (updatedStatus.isComplete) {
          completedTrainings.push(trainingId)
          onComplete?.(updatedStatus)
          
          // Emit event for PersonaSelector to refresh
          if (updatedStatus.status === 'succeeded') {
            window.dispatchEvent(new CustomEvent('trainingCompleted', {
              detail: { trainingId, modelUrl: updatedStatus.modelUrl }
            }))
          }
        }
      }
    }

    setActiveTrainings(updatedTrainings)

    // Remove completed trainings from active polling after a delay
    if (completedTrainings.length > 0) {
      setTimeout(() => {
        setActiveTrainings(prev => {
          const updated = new Map(prev)
          completedTrainings.forEach(id => updated.delete(id))
          return updated
        })
      }, 60000) // Keep completed trainings visible for 1 minute
    }

  }, [activeTrainings, pollTrainingStatus, onStatusUpdate, onComplete])

  // Start polling interval
  useEffect(() => {
    console.log('🔄 Training polling hook mounted')
    
    // Listen for browser events (from server-side updates)
    const handleTrainingUpdate = () => {
      console.log('📢 Received browser training update event')
      pollNow(true) // Force refresh from server
    }
    
    window.addEventListener('training-updated', handleTrainingUpdate)
    
    return () => {
      window.removeEventListener('training-updated', handleTrainingUpdate)
    }
  }, [])

  useEffect(() => {
    const now = Date.now()
    
    // Check if we have trainings ready for polling
    const readyTrainings = Array.from(activeTrainings.values()).filter(
      training => !training.isWaitingToStartPolling && !training.isComplete
    )
    
    // Check if we have trainings waiting to start polling
    const waitingTrainings = Array.from(activeTrainings.values()).filter(
      training => training.isWaitingToStartPolling && training.willStartPollingAt && now >= training.willStartPollingAt
    )
    
    // Move waiting trainings to ready state
    if (waitingTrainings.length > 0) {
      setActiveTrainings(prev => {
        const updated = new Map(prev)
        waitingTrainings.forEach(training => {
          const updatedTraining = {
            ...training,
            isWaitingToStartPolling: false,
            estimatedTime: '1-2 minutes remaining'
          }
          updated.set(training.trainingId, updatedTraining)
          console.log(`⏰ Training ${training.trainingId} is now ready for polling after 1-minute delay`)
        })
        return updated
      })
    }

    if (readyTrainings.length > 0 && !intervalRef.current) {
      console.log(`🚀 Starting efficient polling for ${readyTrainings.length} training(s) after 1-minute delay...`)
      setIsPolling(true)
      
      // Poll immediately
      pollAllTrainings()
      
      // Then poll every 5 seconds (efficient polling)
      intervalRef.current = setInterval(() => pollAllTrainings(false), pollingInterval)
    } else if (readyTrainings.length === 0 && intervalRef.current) {
      console.log('⏹️ Stopping training polling (no ready trainings)')
      clearInterval(intervalRef.current)
      intervalRef.current = null
      setIsPolling(false)
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
        setIsPolling(false)
      }
    }
  }, [activeTrainings, pollAllTrainings, pollingInterval])

  // Add a training to polling with 1-minute delay
  const addTraining = useCallback((trainingId: string, initialStatus: Partial<TrainingStatus> = {}) => {
    const now = Date.now()
    const delayMs = initialDelayMinutes * 60 * 1000 // Convert minutes to milliseconds
    const willStartPollingAt = now + delayMs
    
    const status: TrainingStatus = {
      trainingId,
      status: 'starting',
      progress: null,
      isComplete: false,
      modelUrl: null,
      error: null,
      estimatedTime: `Polling will start in ${initialDelayMinutes} minute${initialDelayMinutes === 1 ? '' : 's'}`,
      startedAt: now,
      willStartPollingAt,
      isWaitingToStartPolling: true,
      ...initialStatus
    }

    console.log(`➕ Adding training to polling queue: ${trainingId} (will start polling in ${initialDelayMinutes} minute${initialDelayMinutes === 1 ? '' : 's'})`)
    setActiveTrainings(prev => new Map(prev.set(trainingId, status)))
    
    // Schedule the start of polling
    setTimeout(() => {
      setActiveTrainings(prev => {
        const updated = new Map(prev)
        const currentTraining = updated.get(trainingId)
        if (currentTraining && currentTraining.isWaitingToStartPolling) {
          const readyTraining = {
            ...currentTraining,
            isWaitingToStartPolling: false,
            estimatedTime: '1-2 minutes remaining'
          }
          updated.set(trainingId, readyTraining)
          console.log(`⏰ Starting polling for training: ${trainingId}`)
        }
        return updated
      })
    }, delayMs)
    
  }, [initialDelayMinutes])

  // Remove a training from polling
  const removeTraining = useCallback((trainingId: string) => {
    console.log(`➖ Removing training from polling: ${trainingId}`)
    setActiveTrainings(prev => {
      const updated = new Map(prev)
      updated.delete(trainingId)
      return updated
    })
  }, [])

  // Get status of a specific training
  const getTrainingStatus = useCallback((trainingId: string): TrainingStatus | null => {
    return activeTrainings.get(trainingId) || null
  }, [activeTrainings])

  // Manual poll trigger with force refresh option
  const pollNow = useCallback((forceRefresh: boolean = false) => {
    console.log(`🔄 Manual poll triggered${forceRefresh ? ' (force refresh)' : ''}`)
    pollAllTrainings(forceRefresh)
  }, [pollAllTrainings])

  // Get time remaining before polling starts for waiting trainings
  const getTimeUntilPolling = useCallback((trainingId: string): number => {
    const training = activeTrainings.get(trainingId)
    if (!training || !training.isWaitingToStartPolling || !training.willStartPollingAt) {
      return 0
    }
    const remaining = Math.max(0, training.willStartPollingAt - Date.now())
    return Math.ceil(remaining / 1000) // Return seconds
  }, [activeTrainings])

  return {
    // State
    activeTrainings: Array.from(activeTrainings.values()),
    isPolling,
    lastPoll: lastPollRef.current,
    
    // Actions
    addTraining,
    removeTraining,
    getTrainingStatus,
    pollNow,
    getTimeUntilPolling,
    
    // Utils
    hasActiveTrainings: activeTrainings.size > 0,
    getActiveTrainingCount: () => activeTrainings.size,
    getPollingTrainingCount: () => Array.from(activeTrainings.values()).filter(t => !t.isWaitingToStartPolling && !t.isComplete).length,
    getWaitingTrainingCount: () => Array.from(activeTrainings.values()).filter(t => t.isWaitingToStartPolling).length
  }
} 