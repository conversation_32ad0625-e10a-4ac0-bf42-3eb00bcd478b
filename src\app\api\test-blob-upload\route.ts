import { NextRequest, NextResponse } from 'next/server'
import { put } from '@vercel/blob'

export async function GET() {
  try {
    console.log('🧪 Testing Vercel Blob ZIP upload...')
    
    // Create a simple test ZIP file content
    const testContent = 'PK\x03\x04test zip content'
    const testBlob = new Blob([testContent], { type: 'application/zip' })
    const testFilename = `test-upload-${Date.now()}.zip`
    
    console.log(`📦 Creating test ZIP: ${testFilename}`)
    console.log(`📋 Test file details:`)
    console.log(`  - Name: ${testFilename}`)
    console.log(`  - Size: ${testBlob.size} bytes`)
    console.log(`  - Type: ${testBlob.type}`)
    
    // Upload to Vercel Blob
    const blob = await put(testFilename, testBlob, {
      access: 'public',
      contentType: 'application/zip'
    })
    
    console.log('✅ Test ZIP uploaded successfully!')
    console.log(`📍 Result:`)
    console.log(`  - URL: ${blob.url}`)
    console.log(`  - Filename: ${testFilename}`)
    console.log(`  - Should be visible in Vercel dashboard as: ${testFilename}`)
    
    return NextResponse.json({
      success: true,
      message: 'Test ZIP uploaded to Vercel Blob',
      details: {
        filename: testFilename,
        url: blob.url,
        size: testBlob.size,
        contentType: 'application/zip',
        instructions: [
          '1. Check your Vercel dashboard',
          '2. Go to Storage > Blob',
          `3. Look for file: ${testFilename}`,
          '4. The file should be visible and downloadable'
        ]
      }
    })
    
  } catch (error) {
    console.error('❌ Test upload failed:', error)
    return NextResponse.json({
      error: 'Test upload failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
} 