import { NextRequest, NextResponse } from 'next/server'

/**
 * Test endpoint to manually trigger background training monitor
 * Useful for testing and immediate polling
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Manual test trigger for background monitor')

    // Call the background monitor endpoint
    const baseUrl = process.env.VERCEL_URL 
      ? `https://${process.env.VERCEL_URL}` 
      : `http://localhost:3000`
    
    const monitorResponse = await fetch(`${baseUrl}/api/background-training-monitor`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    const result = await monitorResponse.json()

    console.log('📊 Background monitor result:', result)

    return NextResponse.json({
      success: true,
      message: 'Background monitor triggered manually',
      monitorResult: result,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ Error triggering background monitor:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    status: 'ready',
    endpoint: 'test-background-monitor',
    description: 'Manually trigger background training monitor',
    usage: 'POST to trigger monitoring',
    timestamp: new Date().toISOString()
  })
} 