# 🔍 Storage Analysis Guide - Where is your 0.032 GB used?

## 📊 Quick Storage Breakdown

Your **0.032 GB (32 MB)** of Supabase storage is likely distributed across:

### 1. **Database Storage** (~10-20% of total)
- **Personas table**: Base64 encoded images in `image_base64` field
- **Generated Images table**: Metadata and file references  
- **Training Jobs table**: LoRA training history and webhooks
- **Webhook Events table**: Replicate callback logs

### 2. **File Storage** (~80-90% of total)  
- **generated-thumbnails bucket**: Actual PNG/JPG thumbnail files
- **Training files**: ZIP uploads for LoRA training (if any)

## 🚀 **IMMEDIATE: Check Your Storage Usage**

### Option 1: Supabase Dashboard (Recommended)
1. Go to your **Supabase Dashboard**: https://supabase.com/dashboard
2. Select your **Thumbnex project**
3. Navigate to **Settings** → **Usage**
4. Check the **Storage** section for:
   - **Database size**: Table data (personas, images metadata)
   - **Storage size**: Actual files (thumbnails, uploads)

### Option 2: Run Storage Analysis API
```bash
# Set up your environment first
cp .env.example .env.local

# Add your Supabase credentials to .env.local:
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your.anon.key.here
SUPABASE_SERVICE_ROLE_KEY=your.service.role.key.here

# Then run the analysis
npm run dev
curl http://localhost:3000/api/analyze-storage
```

## 📂 **Storage Breakdown Analysis**

### **Expected Storage Distribution:**

| **Component** | **Size Range** | **Typical Usage** |
|---------------|----------------|-------------------|
| **Database Tables** | 1-5 MB | Personas, metadata, training logs |
| **Generated Thumbnails** | 10-25 MB | PNG files (~200-500KB each) |
| **Training ZIPs** | 5-15 MB | LoRA training data (temporary) |
| **Cached Images** | 1-5 MB | Base64 persona images |

### **Common Storage Consumers:**

#### 🔸 **Generated Thumbnails** (Likely biggest)
- **Location**: `generated-thumbnails` bucket in Supabase Storage
- **Size per file**: ~200-500 KB per thumbnail
- **32 MB = ~64-160 thumbnails** (depending on resolution)
- **SQL to check**:
```sql
SELECT 
  COUNT(*) as total_images,
  SUM(file_size) as total_bytes,
  AVG(file_size) as avg_size_bytes,
  SUM(file_size) / 1024 / 1024 as total_mb
FROM generated_images 
WHERE file_size IS NOT NULL;
```

#### 🔸 **Persona Images** (Base64 in database)
- **Location**: `personas` table, `image_base64` column
- **Size per persona**: ~100-500 KB (base64 encoded)
- **SQL to check**:
```sql
SELECT 
  COUNT(*) as persona_count,
  SUM(LENGTH(image_base64)) as total_base64_chars,
  AVG(LENGTH(image_base64)) as avg_base64_chars,
  SUM(LENGTH(image_base64)) * 0.75 / 1024 / 1024 as estimated_mb
FROM personas 
WHERE image_base64 IS NOT NULL;
```

#### 🔸 **Training Data** (Temporary storage)
- **Location**: Vercel Blob or temporary storage
- **Size per training**: ~20-50 MB ZIP files
- **Usually cleaned up automatically**

## 🧹 **Storage Optimization Recommendations**

### **1. Immediate Actions** (Reduce by 50-70%)
```sql
-- Delete old generated images (keep last 50)
DELETE FROM generated_images 
WHERE id NOT IN (
  SELECT id FROM generated_images 
  ORDER BY created_at DESC 
  LIMIT 50
);

-- Clean up failed training jobs older than 7 days
DELETE FROM training_jobs 
WHERE status = 'failed' 
AND created_at < NOW() - INTERVAL '7 days';
```

### **2. Automated Cleanup** (Ongoing maintenance)
- **Enable the cleanup API**: Already exists at `/api/cleanup-training-zips`
- **Set up cron job**: Runs every 15 minutes to clean temporary files
- **Configure retention**: Keep only recent thumbnails (30 days)

### **3. Storage Efficiency**
```javascript
// Optimize persona image storage
const optimizePersonaImages = async () => {
  // Move base64 images to Supabase Storage
  // Keep only URLs in database
  // Reduce database size by 80-90%
}
```

## 🎯 **Most Likely Cause of Your 32 MB**

Based on typical usage patterns:

1. **~20-25 MB**: Generated thumbnail files in `generated-thumbnails` bucket
2. **~5-8 MB**: Base64 persona images in database  
3. **~3-5 MB**: Training job metadata and webhook logs
4. **~1-2 MB**: Miscellaneous database records

## ⚡ **Quick Fix Script**

Create this file as `scripts/analyze-storage.js`:

```javascript
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'YOUR_SUPABASE_URL'
const supabaseKey = 'YOUR_SERVICE_ROLE_KEY'
const supabase = createClient(supabaseUrl, supabaseKey)

async function analyzeStorage() {
  console.log('🔍 Analyzing storage usage...')
  
  // Check database tables
  const { data: personas } = await supabase.from('personas').select('*')
  const { data: images } = await supabase.from('generated_images').select('*')
  const { data: training } = await supabase.from('training_jobs').select('*')
  
  // Calculate sizes
  const personaSize = personas?.reduce((sum, p) => 
    sum + (p.image_base64?.length || 0), 0) * 0.75 / 1024 / 1024
  
  const imageSize = images?.reduce((sum, img) => 
    sum + (img.file_size || 0), 0) / 1024 / 1024
  
  console.log('📊 Storage breakdown:')
  console.log(`  Personas (base64): ${personaSize?.toFixed(2)} MB`)
  console.log(`  Generated images: ${imageSize?.toFixed(2)} MB`)
  console.log(`  Training jobs: ${training?.length} records`)
  
  // Check file storage
  const { data: files } = await supabase.storage
    .from('generated-thumbnails').list()
  
  const totalFiles = files?.length || 0
  console.log(`  Storage files: ${totalFiles} files`)
  
  return {
    personaSize,
    imageSize,
    totalFiles,
    totalEstimated: personaSize + imageSize
  }
}

analyzeStorage().then(console.log)
```

## 🚨 **Action Plan**

1. **Check Supabase Dashboard** → Settings → Usage
2. **Set up environment variables** → Create `.env.local` 
3. **Run storage analysis** → `curl localhost:3000/api/analyze-storage`
4. **Implement cleanup** → Enable automated cleanup APIs
5. **Monitor usage** → Set up alerts for storage limits

Your **32 MB is perfectly normal** for a thumbnail generation app with some usage history! 🎉 