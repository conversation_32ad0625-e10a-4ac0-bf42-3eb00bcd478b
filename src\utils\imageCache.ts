/**
 * Image Cache Management Utility
 * Prevents memory accumulation by managing browser image cache
 */

interface CachedImage {
  url: string
  timestamp: number
  size?: number
}

class ImageCacheManager {
  private cache = new Map<string, CachedImage>()
  private readonly MAX_CACHE_SIZE = 10 // Maximum number of images to keep in memory
  private readonly MAX_AGE_MS = 5 * 60 * 1000 // 5 minutes

  /**
   * Add an image to the cache with automatic cleanup
   */
  addImage(url: string, size?: number): void {
    // Clean up old images first
    this.cleanup()

    // Add new image
    this.cache.set(url, {
      url,
      timestamp: Date.now(),
      size
    })

    console.log(`📸 Added image to cache. Total: ${this.cache.size}/${this.MAX_CACHE_SIZE}`)

    // If cache is full, remove oldest images
    if (this.cache.size > this.MAX_CACHE_SIZE) {
      this.removeOldest()
    }
  }

  /**
   * Remove an image from cache and clean up blob URL if needed
   */
  removeImage(url: string): void {
    const cached = this.cache.get(url)
    if (cached) {
      // Clean up blob URL
      if (url.startsWith('blob:')) {
        URL.revokeObjectURL(url)
        console.log('🧹 Revoked blob URL:', url.substring(0, 50) + '...')
      }
      
      this.cache.delete(url)
      console.log(`🗑️ Removed image from cache. Remaining: ${this.cache.size}`)
    }
  }

  /**
   * Clean up expired images
   */
  private cleanup(): void {
    const now = Date.now()
    const expiredUrls: string[] = []

    for (const [url, cached] of this.cache.entries()) {
      if (now - cached.timestamp > this.MAX_AGE_MS) {
        expiredUrls.push(url)
      }
    }

    expiredUrls.forEach(url => this.removeImage(url))

    if (expiredUrls.length > 0) {
      console.log(`🧹 Cleaned up ${expiredUrls.length} expired images`)
    }
  }

  /**
   * Remove oldest images when cache is full
   */
  private removeOldest(): void {
    const sortedEntries = Array.from(this.cache.entries())
      .sort(([, a], [, b]) => a.timestamp - b.timestamp)

    const toRemove = sortedEntries.slice(0, this.cache.size - this.MAX_CACHE_SIZE + 1)
    
    toRemove.forEach(([url]) => this.removeImage(url))
  }

  /**
   * Clear all cached images
   */
  clearAll(): void {
    const urls = Array.from(this.cache.keys())
    urls.forEach(url => this.removeImage(url))
    console.log('🧹 Cleared all cached images')
  }

  /**
   * Get cache statistics
   */
  getStats(): { count: number; maxSize: number; oldestAge: number } {
    const now = Date.now()
    let oldestAge = 0

    for (const cached of this.cache.values()) {
      const age = now - cached.timestamp
      if (age > oldestAge) {
        oldestAge = age
      }
    }

    return {
      count: this.cache.size,
      maxSize: this.MAX_CACHE_SIZE,
      oldestAge: Math.round(oldestAge / 1000) // in seconds
    }
  }

  /**
   * Force cleanup - useful for manual memory management
   */
  forceCleanup(): void {
    this.cleanup()
    
    // If still over limit, remove more aggressively
    if (this.cache.size > Math.floor(this.MAX_CACHE_SIZE / 2)) {
      const toKeep = Math.floor(this.MAX_CACHE_SIZE / 2)
      const sortedEntries = Array.from(this.cache.entries())
        .sort(([, a], [, b]) => b.timestamp - a.timestamp) // Keep newest
      
      const toRemove = sortedEntries.slice(toKeep)
      toRemove.forEach(([url]) => this.removeImage(url))
      
      console.log(`🧹 Force cleanup: kept ${toKeep} newest images`)
    }
  }
}

// Global instance
export const imageCache = new ImageCacheManager()

/**
 * Hook for React components to manage image lifecycle
 */
export function useImageCache() {
  return {
    addImage: (url: string, size?: number) => imageCache.addImage(url, size),
    removeImage: (url: string) => imageCache.removeImage(url),
    clearAll: () => imageCache.clearAll(),
    forceCleanup: () => imageCache.forceCleanup(),
    getStats: () => imageCache.getStats()
  }
}

/**
 * Preload image with cache management
 */
export function preloadImageWithCache(url: string): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    
    img.onload = () => {
      // Estimate image size (rough calculation)
      const estimatedSize = img.width * img.height * 4 // 4 bytes per pixel (RGBA)
      imageCache.addImage(url, estimatedSize)
      resolve(img)
    }
    
    img.onerror = () => {
      reject(new Error(`Failed to load image: ${url}`))
    }
    
    img.src = url
  })
}
