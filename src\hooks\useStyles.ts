import { useState, useEffect, useCallback } from 'react';
import { StyleTemplate } from '../types/persona';

const STORAGE_KEY = 'thumbnex_styles';

export function useStyles() {
  const [styles, setStyles] = useState<StyleTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadStylesFromStorage = useCallback(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (err) {
      console.error('Error loading styles from storage:', err);
      return [];
    }
  }, []);

  const saveStylesToStorage = useCallback((stylesToSave: StyleTemplate[]) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(stylesToSave));
    } catch (err) {
      console.error('Error saving styles to storage:', err);
    }
  }, []);

  const fetchStyles = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      // First try to fetch from API (database)
      try {
        const response = await fetch('/api/styles');
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.styles) {
            setStyles(data.styles);
            console.log('✅ Loaded styles from API:', data.styles.length);
            return;
          }
        }
        console.warn('⚠️ API fetch failed, falling back to localStorage');
      } catch (apiError) {
        console.warn('⚠️ API unavailable, using localStorage:', apiError);
      }
      
      // Fallback to localStorage if API fails
      const localStyles = loadStylesFromStorage();
      setStyles(localStyles);
      console.log('📱 Loaded styles from localStorage:', localStyles.length);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      console.error('Error fetching styles:', err);
    } finally {
      setLoading(false);
    }
  }, [loadStylesFromStorage]);

  const createStyle = useCallback(async (
    name: string,
    category: StyleTemplate['category'],
    description: string,
    tags: string[],
    imageFile: File
  ) => {
    try {
      // First try to create via API
      try {
        const formData = new FormData();
        formData.append('name', name);
        formData.append('category', category);
        formData.append('description', description);
        formData.append('tags', tags.join(','));
        formData.append('imageFile_0', imageFile);
        formData.append('imageCount', '1');
        formData.append('triggerWord', `STYLE${Date.now()}`); // Generate unique trigger word
        
        const response = await fetch('/api/styles', {
          method: 'POST',
          body: formData
        });
        
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.style) {
            // Add to state
            const updatedStyles = [...styles, data.style];
            setStyles(updatedStyles);
            console.log('✅ Created style via API:', data.style.name);
            return data.style;
          }
        }
        console.warn('⚠️ API create failed, falling back to localStorage');
      } catch (apiError) {
        console.warn('⚠️ API unavailable for creation, using localStorage:', apiError);
      }

      // Fallback to localStorage creation
      const base64 = await new Promise<string>((resolve) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target?.result as string);
        reader.readAsDataURL(imageFile);
      });

      const newStyle: StyleTemplate = {
        id: 'style-' + Date.now(),
        name,
        category,
        description,
        tags,
        imageUrl: base64,
        createdAt: new Date().toISOString(),
        usageCount: 0
      };

      // Update state and localStorage (fallback)
      const updatedStyles = [...styles, newStyle];
      setStyles(updatedStyles);
      saveStylesToStorage(updatedStyles);
      
      console.log('📱 Created style in localStorage:', newStyle.name);
      return newStyle;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [styles, saveStylesToStorage]);

  const updateStyleUsage = useCallback(async (styleId: string) => {
    try {
      const style = styles.find(s => s.id === styleId);
      if (!style) return;

      const response = await fetch('/api/styles', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: styleId,
          usageCount: style.usageCount + 1,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update style usage');
      }

      const updatedStyle = await response.json();
      setStyles(prev => prev.map(s => s.id === styleId ? updatedStyle : s));
    } catch (err) {
      console.error('Error updating style usage:', err);
    }
  }, [styles]);

  const deleteStyle = useCallback(async (styleId: string) => {
    try {
      const response = await fetch(`/api/styles?id=${styleId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete style');
      }

      setStyles(prev => prev.filter(s => s.id !== styleId));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  const refreshStyles = useCallback(async () => {
    await fetchStyles();
  }, [fetchStyles]);

  const clearAllStyles = useCallback(() => {
    setStyles([]);
    saveStylesToStorage([]);
    console.log('Cleared all styles');
  }, [saveStylesToStorage]);

  const resetError = useCallback(() => {
    setError(null);
  }, []);

  useEffect(() => {
    fetchStyles();
  }, [fetchStyles]);

  return {
    styles,
    loading,
    error,
    createStyle,
    updateStyleUsage,
    deleteStyle,
    refreshStyles,
    clearAllStyles,
    resetError,
  };
} 