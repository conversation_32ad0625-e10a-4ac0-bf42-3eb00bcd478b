import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 === TEST ENDPOINT CALLED ===');
    const requestBody = await request.json();
    console.log('📦 Test request body:', JSON.stringify(requestBody, null, 2));
    
    const { prompt } = requestBody;
    
    if (!prompt) {
      return NextResponse.json(
        { error: 'Prompt is required for testing' },
        { status: 400 }
      );
    }
    
    console.log('📝 Test prompt received:', prompt);
    console.log('📏 Test prompt length:', prompt.length, 'characters');
    
    // Just echo back the prompt without calling Replicate
    return NextResponse.json({
      success: true,
      message: 'Test endpoint working!',
      receivedPrompt: prompt,
      promptLength: prompt.length,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Test endpoint error:', error);
    return NextResponse.json(
      { error: 'Test endpoint failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
} 