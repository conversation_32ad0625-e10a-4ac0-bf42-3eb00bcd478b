import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with proper error handling
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Create client only if environment variables are available
const supabase = supabaseUrl && supabaseServiceKey 
  ? createClient(supabaseUrl, supabaseServiceKey)
  : null;

export interface GeneratedImageRecord {
  id?: string;
  persona_id?: string;
  prompt: string;
  image_url: string;
  replicate_url?: string;
  file_name: string;
  file_size?: number;
  mime_type?: string;
  generation_params?: Record<string, any>;
}

/**
 * Downloads an image from a URL and uploads it to Supabase Storage
 * @param imageUrl - The temporary URL from Replicate
 * @param fileName - The desired filename for the stored image
 * @param bucketName - The Supabase storage bucket name
 * @returns The permanent Supabase storage URL
 */
export async function saveImageToSupabase(
  imageUrl: string,
  fileName: string,
  bucketName: string = 'generated-thumbnails'
): Promise<string> {
  try {
    // Check if Supabase client is available
    if (!supabase) {
      throw new Error('Supabase client not initialized. Missing SUPABASE_SERVICE_ROLE_KEY environment variable.');
    }

    console.log('📥 Downloading image from:', imageUrl);
    
    // Handle data URLs (base64 images)
    if (imageUrl.startsWith('data:')) {
      const [header, base64Data] = imageUrl.split(',');
      const mimeType = header.match(/data:([^;]+)/)?.[1] || 'image/png';
      const buffer = Buffer.from(base64Data, 'base64');
      
      // Upload buffer directly to Supabase
      const { data, error } = await supabase.storage
        .from(bucketName)
        .upload(fileName, buffer, {
          contentType: mimeType,
          upsert: true
        });

      if (error) throw error;
      
      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from(bucketName)
        .getPublicUrl(fileName);
      
      console.log('✅ Uploaded base64 image to:', publicUrl);
      return publicUrl;
    }
    
    // Handle regular HTTP URLs
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to download image: ${response.status} ${response.statusText}`);
    }
    
    const arrayBuffer = await response.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    const mimeType = response.headers.get('content-type') || 'image/png';
    
    console.log('📊 Image details:', {
      size: buffer.length,
      mimeType,
      fileName
    });
    
    // Upload to Supabase Storage
    const { data, error } = await supabase.storage
      .from(bucketName)
      .upload(fileName, buffer, {
        contentType: mimeType,
        upsert: true
      });

    if (error) {
      console.error('❌ Supabase upload error:', error);
      throw error;
    }
    
    // Get the public URL
    const { data: { publicUrl } } = supabase.storage
      .from(bucketName)
      .getPublicUrl(fileName);
    
    console.log('✅ Image uploaded successfully to:', publicUrl);
    return publicUrl;
    
  } catch (error) {
    console.error('❌ Failed to save image to Supabase:', error);
    throw error;
  }
}

/**
 * Saves a generated image record to the database
 * @param record - The image record to save
 * @returns The saved record with ID
 */
export async function saveGeneratedImageRecord(
  record: GeneratedImageRecord
): Promise<GeneratedImageRecord> {
  try {
    // Check if Supabase client is available
    if (!supabase) {
      throw new Error('Supabase client not initialized. Missing SUPABASE_SERVICE_ROLE_KEY environment variable.');
    }

    console.log('💾 Saving image record to database:', {
      persona_id: record.persona_id,
      prompt: record.prompt.substring(0, 50) + '...',
      file_name: record.file_name
    });
    
    const { data, error } = await supabase
      .from('generated_images')
      .insert([record])
      .select()
      .single();

    if (error) {
      console.error('❌ Database save error:', error);
      throw error;
    }
    
    console.log('✅ Image record saved with ID:', data.id);
    return data;
    
  } catch (error) {
    console.error('❌ Failed to save image record:', error);
    throw error;
  }
}

/**
 * Complete image storage workflow: download, upload, and save record
 * @param tempImageUrl - Temporary URL from Replicate
 * @param prompt - The generation prompt
 * @param personaId - Optional persona ID
 * @param generationParams - Optional generation parameters
 * @returns The permanent image URL and database record
 */
export async function storeGeneratedImage(
  tempImageUrl: string,
  prompt: string,
  personaId?: string,
  generationParams?: Record<string, any>
): Promise<{ permanentUrl: string; record: GeneratedImageRecord }> {
  try {
    // Generate unique filename
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 8);
    const fileName = `thumbnail_${timestamp}_${randomId}.png`;
    
    // Upload image to storage
    const permanentUrl = await saveImageToSupabase(tempImageUrl, fileName);
    
    // Create database record
    const record: GeneratedImageRecord = {
      persona_id: personaId,
      prompt,
      image_url: permanentUrl,
      replicate_url: tempImageUrl.startsWith('data:') ? undefined : tempImageUrl,
      file_name: fileName,
      file_size: undefined, // Could calculate this if needed
      mime_type: 'image/png',
      generation_params: generationParams || {}
    };
    
    // Save to database
    const savedRecord = await saveGeneratedImageRecord(record);
    
    console.log('🎉 Complete image storage workflow completed successfully');
    return { permanentUrl, record: savedRecord };
    
  } catch (error) {
    console.error('❌ Complete image storage workflow failed:', error);
    throw error;
  }
} 