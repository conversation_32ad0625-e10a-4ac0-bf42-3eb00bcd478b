import { NextRequest, NextResponse } from 'next/server'
import { Persona } from '../../../types/persona'
import { createClient } from '@supabase/supabase-js'

// Hardcoded Supabase credentials for testing
const supabaseUrl = 'https://xujzgjeoawhxkauzcvgj.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh1anpnamVvYXdoeGthdXpjdmdqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxNjE1MDEsImV4cCI6MjA2NTczNzUwMX0.cLh7dA4SbE0W0PNXWEq2uGcZ4bokd8d6EhhKRiPUvyg'

const supabase = createClient(supabaseUrl, supabaseKey)

// Simplified image validation - just check basic file properties
function validateImageFile(file: File): { valid: boolean; error?: string } {
  // Check file size (max 10MB)
  if (file.size > 10 * 1024 * 1024) {
    return {
      valid: false,
      error: 'File size too large. Maximum 10MB allowed.'
    }
  }

  // Check minimum size (1KB)
  if (file.size < 1024) {
    return {
      valid: false,
      error: 'File too small. Minimum 1KB required.'
    }
  }

  // Check MIME type (normalize image/jpg to image/jpeg)
  const normalizedMimeType = file.type === 'image/jpg' ? 'image/jpeg' : file.type
  const validMimeTypes = ['image/jpeg', 'image/png', 'image/webp']
  
  if (validMimeTypes.indexOf(normalizedMimeType) === -1) {
    return {
      valid: false,
      error: `Invalid file type: ${file.type}. Only JPEG, PNG, and WebP are supported.`
    }
  }

  // Check file extension
  const extension = file.name.split('.').pop()?.toLowerCase()
  const validExtensions = ['jpg', 'jpeg', 'png', 'webp']
  
  if (!extension || validExtensions.indexOf(extension) === -1) {
    return {
      valid: false,
      error: `Invalid file extension: .${extension}. Only .jpg, .jpeg, .png, and .webp are supported.`
    }
  }

  return { valid: true }
}

// Convert file to base64 for preview (server-side)
async function fileToBase64(file: File): Promise<string> {
  try {
    const bytes = await file.arrayBuffer()
    const buffer = new Uint8Array(bytes)
    
    // Convert to base64 using built-in btoa
    let binary = ''
    for (let i = 0; i < buffer.length; i++) {
      binary += String.fromCharCode(buffer[i])
    }
    const base64 = btoa(binary)
    
    return `data:${file.type};base64,${base64}`
  } catch (error) {
    console.error('Error converting file to base64:', error)
    throw new Error('Failed to process image file')
  }
}

// GET - Fetch all personas from simplified database
export async function GET(request: NextRequest) {
  try {
    console.log('📋 Fetching all personas from simplified database...')
    
    const { data, error } = await supabase
      .from('personas')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (error) {
      console.error('❌ Database error:', error)
      throw new Error(`Database error: ${error.message}`)
    }
    
    console.log(`✅ Retrieved ${data?.length || 0} personas from database`)
    
    return NextResponse.json({
      success: true,
      personas: data || [],
      count: data?.length || 0
    })
  } catch (error) {
    console.error('❌ Error fetching personas:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch personas' },
      { status: 500 }
    )
  }
}

// POST - Create new persona with multiple training images (NO PROCESSING - Replicate handles everything)
export async function POST(request: NextRequest) {
  try {
    console.log('👤 POST /api/personas called - Creating persona (simplified - no Sharp processing)...')
    
    const formData = await request.formData()
    const name = formData.get('name') as string
    const description = formData.get('description') as string
    const category = formData.get('category') as Persona['category']
    const isDefault = formData.get('isDefault') === 'true'
    const triggerWord = formData.get('triggerWord') as string
    const imageCount = parseInt(formData.get('imageCount') as string || '0')

    console.log(`📋 Processing ${imageCount} images for persona: ${name}`)

    // Validate required fields
    if (!name || !triggerWord || imageCount < 10) {
      return NextResponse.json(
        { error: 'Name, trigger word, and at least 10 images are required' },
        { status: 400 }
      )
    }

    // Collect all image files
    const imageFiles: File[] = []
    for (let i = 0; i < imageCount; i++) {
      const imageFile = formData.get(`imageFile_${i}`) as File
      if (imageFile && imageFile.type.startsWith('image/')) {
        imageFiles.push(imageFile)
      }
    }

    if (imageFiles.length < 10) {
      return NextResponse.json(
        { error: `Insufficient images. Got ${imageFiles.length}, need at least 10.` },
        { status: 400 }
      )
    }

    console.log(`✅ Collected ${imageFiles.length} valid image files`)

    // Validate images (but don't process them - Replicate will handle that)
    const validImages: File[] = []
    const invalidImages: { file: File; reason: string }[] = []

    for (let i = 0; i < imageFiles.length; i++) {
      const imageFile = imageFiles[i]
      console.log(`🔄 Validating image ${i + 1}/${imageFiles.length}: ${imageFile.name}`)

      // Simple validation only
      const validation = validateImageFile(imageFile)
      if (!validation.valid) {
        console.warn(`⚠️ Skipping invalid image ${i + 1}: ${validation.error}`)
        invalidImages.push({ file: imageFile, reason: validation.error! })
        continue
      }

      validImages.push(imageFile)
      console.log(`✅ Image ${i + 1} is valid`)
    }

    if (validImages.length < 10) {
      return NextResponse.json(
        { 
          error: `Only ${validImages.length} images are valid. Need at least 10.`,
          invalidImages: invalidImages.map(img => ({ name: img.file.name, reason: img.reason }))
        },
        { status: 400 }
      )
    }

    console.log(`✅ Successfully validated ${validImages.length} images`)
    
    // Generate unique ID
    const id = `persona_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    // Get first image for preview (no processing)
    const firstImageBase64 = await fileToBase64(validImages[0])
    
    // Save directly to simplified database schema
    const { data, error: dbError } = await supabase
      .from('personas')
      .insert({
        id,
        name,
        image_base64: firstImageBase64,
        model_url: null, // Will be set when training completes
        trigger_word: triggerWord.toUpperCase(),
        created_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (dbError) {
      console.error('❌ Database error:', dbError)
      return NextResponse.json(
        { 
          error: 'Failed to save persona to database',
          details: dbError.message
        },
        { status: 500 }
      )
    }
    
    // Create the complex persona object for backward compatibility
    const newPersona: Persona = {
      id,
      name,
      description,
      imageUrl: firstImageBase64,
      imageBase64: firstImageBase64,
      createdAt: new Date(),
      updatedAt: new Date(),
      usageCount: 0,
      category,
      isDefault,
      lastUsed: new Date(),
      loraTraining: {
        status: 'pending',
        triggerWord: triggerWord.toUpperCase(),
                 trainingImages: validImages.map((file, index) => ({
           id: `img_${Date.now()}_${index}`,
           originalName: file.name,
           processedImageBase64: '', // Empty since Replicate handles processing
           uploadedAt: new Date(),
           expression: `Expression ${index + 1}`,
           setting: `Setting ${index + 1}`
         })),
        steps: 1000,
        resolution: 1024,
        loraType: 'subject',
        createdAt: new Date()
      }
    }
    
    console.log(`✅ Persona "${name}" created successfully`)
    console.log(`🎯 Trigger word: ${triggerWord}`)
    console.log(`📊 Persona saved to database: ${data.id}`)
    console.log(`📸 Ready for training with ${validImages.length} images`)
    
    return NextResponse.json({
      success: true,
      persona: newPersona,
      message: `Persona created successfully with ${validImages.length} images. Ready for training.`,
      note: 'No image processing performed - Replicate will handle all image processing during training.',
      trainingInfo: {
        imageCount: validImages.length,
        triggerWord: triggerWord.toUpperCase(),
        estimatedTrainingCost: '$2-4 USD',
        estimatedTrainingTime: '15-20 minutes',
        processingNote: 'Replicate automatically handles image resizing, cropping, and optimization'
      }
    })
    
  } catch (error) {
    console.error('❌ Error creating persona:', error)
    return NextResponse.json(
      { 
        error: 'Failed to create persona',
        details: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    )
  }
}

// PUT - Update persona
export async function PUT(request: NextRequest) {
  try {
    console.log('📝 Updating persona...')
    
    const { id, ...updates } = await request.json()
    
    if (!id) {
      return NextResponse.json(
        { error: 'Persona ID is required' },
        { status: 400 }
      )
    }

    // Update in database
    const { data, error } = await supabase
      .from('personas')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('❌ Database error:', error)
      return NextResponse.json(
        { error: 'Failed to update persona' },
        { status: 500 }
      )
    }

    console.log(`✅ Updated persona: ${data.name}`)
    
    return NextResponse.json({
      success: true,
      persona: data,
      message: 'Persona updated successfully'
    })
  } catch (error) {
    console.error('❌ Error updating persona:', error)
    return NextResponse.json(
      { error: 'Failed to update persona' },
      { status: 500 }
    )
  }
}

// DELETE - Delete persona
export async function DELETE(request: NextRequest) {
  try {
    console.log('🗑️ Deleting persona...')
    
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    
    if (!id) {
      return NextResponse.json(
        { error: 'Persona ID is required' },
        { status: 400 }
      )
    }

    // Delete from database
    const { error } = await supabase
      .from('personas')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('❌ Database error:', error)
      return NextResponse.json(
        { error: 'Failed to delete persona' },
        { status: 500 }
      )
    }

    console.log(`✅ Deleted persona: ${id}`)
    
    return NextResponse.json({
      success: true,
      message: 'Persona deleted successfully'
    })
  } catch (error) {
    console.error('❌ Error deleting persona:', error)
    return NextResponse.json(
      { error: 'Failed to delete persona' },
      { status: 500 }
    )
  }
} 