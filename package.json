{"name": "thumbnex", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "vercel-build": "npm install && next build", "dev-helper": "node scripts/local-dev-helper.js", "test-persona-flow": "node scripts/test-persona-flow.js"}, "dependencies": {"@mediapipe/camera_utils": "^0.3.1675466862", "@mediapipe/face_detection": "^0.4.1646425229", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@supabase/supabase-js": "^2.50.0", "@types/jszip": "^3.4.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@vercel/blob": "^1.1.1", "autoprefixer": "^10.0.1", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "dotenv": "^16.5.0", "eslint": "^8", "eslint-config-next": "14.2.5", "jszip": "^3.10.1", "lucide-react": "^0.263.1", "next": "14.2.5", "postcss": "^8", "react": "^18", "react-dom": "^18", "react-image-crop": "^11.0.10", "replicate": "^1.0.1", "sharp": "^0.34.2", "tailwindcss": "^3.3.0", "typescript": "^5"}, "devDependencies": {"atob": "^2.1.2", "blob-polyfill": "^9.0.20240710", "cross-fetch": "^4.1.0", "form-data": "^4.0.3", "node-fetch": "^2.6.7"}}