import type { Metada<PERSON>, Viewport } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Thumbnex - AI Thumbnail Generator',
  description: 'Create stunning YouTube thumbnails with AI-powered persona and style customization',
  keywords: 'YouTube, thumbnails, AI, generator, creator tools',
  authors: [{ name: 'Thumbnex Team' }],
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark">
      <body className={`${inter.className} antialiased min-h-screen bg-bg-primary text-text-primary`}>
        {children}
      </body>
    </html>
  )
} 