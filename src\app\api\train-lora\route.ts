import { NextRequest, NextResponse } from 'next/server'

/**
 * DEPRECATED: Legacy LoRA Training Endpoint
 * 
 * This endpoint has been deprecated in favor of the new direct training system.
 * Please use the Training Dashboard interface for all new persona training.
 */

export async function POST(request: NextRequest) {
  console.log('⚠️ DEPRECATED: /api/train-lora called - redirecting to new system')
  
  try {
    const requestBody = await request.json()
    const { personaId } = requestBody
    
    return NextResponse.json({
      error: 'This training endpoint has been deprecated',
      message: 'The persona-based LoRA training system has been replaced with a more reliable direct training approach.',
      solution: 'Please use the Training Dashboard to create new personas with LoRA training.',
      migration: {
        oldEndpoint: '/api/train-lora',
        newEndpoint: '/api/train-lora-direct',
        newWorkflow: 'Training Dashboard → Upload Images → Direct Training → Auto-Persona Creation',
        benefits: [
          'A100 GPU for 33% faster training',
          'Direct API with no database dependencies', 
          'Automatic persona creation on completion',
          'Better error handling and reliability'
        ]
      },
      deprecated: true,
      timestamp: new Date().toISOString()
    }, { status: 410 }) // 410 Gone - resource permanently removed
    
  } catch (error) {
    return NextResponse.json({
      error: 'Deprecated endpoint - cannot process request',
      message: 'Please use the new Training Dashboard interface',
      deprecated: true
    }, { status: 410 })
  }
}

export async function GET(request: NextRequest) {
  console.log('⚠️ DEPRECATED: /api/train-lora GET called')
  
  const { searchParams } = new URL(request.url)
  const trainingId = searchParams.get('trainingId')
  
  if (trainingId) {
    // Redirect status checks to new endpoint
    try {
      const response = await fetch(`${request.nextUrl.origin}/api/train-lora-direct?trainingId=${trainingId}`)
      const data = await response.json()
      
      return NextResponse.json({
        ...data,
        deprecated: true,
        message: 'Status check redirected from deprecated endpoint',
        newEndpoint: '/api/train-lora-direct'
      })
    } catch (error) {
      return NextResponse.json({
        error: 'Failed to check status via deprecated endpoint',
        message: 'Please use /api/train-lora-direct for status checks',
        deprecated: true
      }, { status: 500 })
    }
  }
  
  return NextResponse.json({
    error: 'Deprecated endpoint',
    message: 'Please use the new Training Dashboard interface',
    deprecated: true
  }, { status: 410 })
} 