'use client'

import { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from './ui/Button'
import {
  <PERSON><PERSON>les,
  Users,
  Mic,
  Wand2,
  Image as ImageIcon,
  Type,
  RotateCcw,
  Zap,
  Plus,
  Link,
  Upload,
  Settings,
  X,
  Download,
  Edit3
} from 'lucide-react'
import { IPAdapterInterface } from './IPAdapterInterface'
import { useIPAdapter } from '../hooks/useIPAdapter'
import { PersonaSelector } from './PersonaSelector'
import { StyleSelector } from './StyleSelector'
import { Persona, StyleTemplate } from '../types/persona'

export function ThumbnailGenerator() {
  const [activeTab, setActiveTab] = useState('thumbnail')
  const [prompt, setPrompt] = useState('Earth in space cracked down the middle, one side lush and green, and the other side scorched and on fire')
  const [isGenerating, setIsGenerating] = useState(false)
  const [recreateOption, setRecreateOption] = useState<'link' | 'upload' | null>(null)
  const [youtubeUrl, setYoutubeUrl] = useState('')
  const [generatedImage, setGeneratedImage] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [showPersonaDropdown, setShowPersonaDropdown] = useState(false)
  const [selectedPersona, setSelectedPersona] = useState<Persona | null>(null)
  const [selectedStyle, setSelectedStyle] = useState<StyleTemplate | null>(null)

  // Handlers for mutual exclusion between persona and style
  const handlePersonaSelect = (persona: Persona | null) => {
    setSelectedPersona(persona)
    if (persona) {
      setSelectedStyle(null) // Clear style when persona is selected
    }
  }

  const handleStyleSelect = (style: StyleTemplate | null) => {
    setSelectedStyle(style)
    if (style) {
      setSelectedPersona(null) // Clear persona when style is selected
    }
  }
  const [speedTier] = useState<'balanced'>('balanced')
  const [aspectRatio, setAspectRatio] = useState<'16:9' | '4:3' | '1:1' | '9:16'>('16:9')
  const [showOptionsModal, setShowOptionsModal] = useState(false)
  const personaDropdownRef = useRef<HTMLDivElement>(null)

  // Close dropdowns when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (personaDropdownRef.current && !personaDropdownRef.current.contains(event.target as Node)) {
        setShowPersonaDropdown(false)
      }
    }

    if (showPersonaDropdown) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showPersonaDropdown])

  // IP-Adapter integration
  const { generateThumbnail: generateWithIPAdapter, isGenerating: isIPAdapterGenerating } = useIPAdapter()

  const tabs = [
    { id: 'thumbnail', label: 'Thumbnail', icon: ImageIcon },
    { id: 'recreate', label: 'Recreate', icon: RotateCcw },
    { id: 'faceswap', label: 'FaceSwap', icon: Users },
    { id: 'title', label: 'Title', icon: Type },
  ]

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      setError('Please enter a prompt to generate a thumbnail')
      return
    }

    console.log('🚀 Starting generation with:', {
      prompt: prompt.trim(),
      hasPersona: !!selectedPersona,
      hasStyle: !!selectedStyle,
      speedTier
    })

    // 🔍 DETAILED PERSONA DEBUG
    if (selectedPersona) {
      console.log('👤 PERSONA DATA BEING SENT:', {
        personaId: selectedPersona.id,
        personaName: selectedPersona.name,
        hasLoraTraining: !!selectedPersona.loraTraining,
        loraStatus: selectedPersona.loraTraining?.status,
        loraModelUrl: selectedPersona.loraTraining?.modelUrl,
        loraTriggerWord: selectedPersona.loraTraining?.triggerWord,
        fullPersonaData: selectedPersona
      })
    } else {
      console.log('❌ NO PERSONA SELECTED - will use standard FLUX generation')
    }

    setIsGenerating(true)
    setError(null)
    setGeneratedImage(null)
    
    try {
      const response = await fetch('/api/ip-adapter-generation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: prompt.trim(),
          model: 'flux-dev',
          persona: selectedPersona,
          styleTemplate: selectedStyle,
          speedTier: speedTier,
          aspectRatio: aspectRatio
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.details || errorData.error || 'Generation failed')
      }

      const data = await response.json()
      
      if (data.success && data.imageUrl) {
        setGeneratedImage(data.imageUrl)
        console.log('✅ Generation successful!')
      } else {
        throw new Error(data.error || 'No image generated')
      }
    } catch (error) {
      console.error('❌ Generation failed:', error)
      setError(error instanceof Error ? error.message : 'Generation failed. Please try again.')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleStartNew = () => {
    setIsGenerating(false)
    setPrompt('Earth in space cracked down the middle, one side lush and green, and the other side scorched and on fire')
    setRecreateOption(null)
    setYoutubeUrl('')
    setActiveTab('thumbnail')
    setGeneratedImage(null)
    setError(null)
    setSelectedStyle(null)
  }

  const handleDownload = async () => {
    if (!generatedImage) return
    
    try {
      // Fetch the image to ensure we get the original resolution
      const response = await fetch(generatedImage)
      const blob = await response.blob()
      
      // Create download link with proper filename
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `thumbnail-${speedTier}-${Date.now()}.webp`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Download failed:', error)
      // Fallback to simple download
      const link = document.createElement('a')
      link.href = generatedImage
      link.download = `thumbnail-${speedTier}-${Date.now()}.webp`
      link.click()
    }
  }

  const OptionsModal = () => {
    if (!showOptionsModal) return null

    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[100] flex items-end md:items-center justify-center p-0 md:p-4">
                    <div className="w-full max-w-lg border border-green-400/30 rounded-t-2xl md:rounded-2xl max-h-[90vh] overflow-hidden">
          {/* Modal Header */}
                      <div className="flex items-center justify-between p-4 border-b border-green-400/20">
            <h3 className="text-lg font-semibold text-neon">Options</h3>
            <Button
              onClick={() => setShowOptionsModal(false)}
              variant="ghost"
              className="p-2 rounded-lg glass-neon border border-green-400/30 hover:border-green-300/50"
            >
              <X className="h-5 w-5 text-green-300" />
            </Button>
          </div>

          {/* Modal Content */}
          <div className="p-4 space-y-4 overflow-y-auto max-h-[80vh]">
            {/* Tab Selection */}
            <div className="space-y-3">
              <label className="text-sm font-medium text-green-200">Mode</label>
              <div className="grid grid-cols-2 gap-2">
                {tabs.map((tab) => (
                  <Button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    active={activeTab === tab.id}
                    variant="outline"
                    className={`group relative flex items-center justify-center space-x-2 p-3 rounded-lg transition-all duration-300 min-h-[48px] touch-manipulation glass-neon border-green-400/30 hover:border-green-300/50 ${
                      activeTab === tab.id 
                        ? 'glass-neon-strong border-green-300/60 text-green-200' 
                        : 'text-green-300/70 hover:text-green-200'
                    }`}
                  >
                    <tab.icon className="h-4 w-4" />
                    <span className="font-medium text-sm">{tab.label}</span>
                  </Button>
                ))}
              </div>
            </div>

            {/* Aspect Ratio */}
            <div className="space-y-3">
              <label className="text-sm font-medium text-green-200">Aspect Ratio</label>
              <div className="grid grid-cols-4 gap-1 glass-neon border border-green-400/30 rounded-lg p-1">
                {(['16:9', '4:3', '1:1', '9:16'] as const).map((ratio) => (
                  <button
                    key={ratio}
                    onClick={() => setAspectRatio(ratio)}
                    className={`px-3 py-2 rounded text-sm font-medium transition-all duration-300 min-h-[44px] touch-manipulation ${
                      aspectRatio === ratio
                        ? 'bg-green-400/30 text-green-200 border border-green-300/30'
                        : 'text-green-300/70 hover:text-green-200 hover:bg-green-400/10'
                    }`}
                  >
                    {ratio}
                  </button>
                ))}
              </div>
            </div>

            

            {/* Tab-specific content */}
            {activeTab === 'recreate' && (
              <div className="space-y-4">
                <div className="space-y-3">
                  <label className="text-sm font-medium text-green-200">Source</label>
                  <div className="grid grid-cols-2 gap-2">
                    <button
                      onClick={() => setRecreateOption('link')}
                      className={`flex items-center justify-center space-x-2 px-3 py-3 rounded-lg border transition-all duration-300 min-h-[44px] touch-manipulation glass-neon ${
                        recreateOption === 'link'
                          ? 'border-green-300/60 text-green-200 bg-green-400/10'
                          : 'border-green-400/30 text-green-300/70 hover:border-green-300/50 hover:text-green-200'
                      }`}
                    >
                      <Link className="h-4 w-4" />
                      <span className="text-sm font-medium">Link</span>
                    </button>
                    
                    <button
                      onClick={() => setRecreateOption('upload')}
                      className={`flex items-center justify-center space-x-2 px-3 py-3 rounded-lg border transition-all duration-300 min-h-[44px] touch-manipulation glass-neon ${
                        recreateOption === 'upload'
                          ? 'border-green-300/60 text-green-200 bg-green-400/10'
                          : 'border-green-400/30 text-green-300/70 hover:border-green-300/50 hover:text-green-200'
                      }`}
                    >
                      <Upload className="h-4 w-4" />
                      <span className="text-sm font-medium">Upload</span>
                    </button>
                  </div>
                </div>

                {recreateOption === 'link' && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-green-200">YouTube Video URL</label>
                    <input
                      type="url"
                      value={youtubeUrl}
                      onChange={(e) => setYoutubeUrl(e.target.value)}
                      placeholder="https://www.youtube.com/watch?v=..."
                      className="w-full p-3 glass-neon border border-green-400/30 rounded-lg text-white placeholder-green-300/50 focus:outline-none focus:border-green-300/60 text-sm min-h-[44px] touch-manipulation"
                    />
                  </div>
                )}

                {recreateOption === 'upload' && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-green-200">Upload Image</label>
                    <div className="border-4 border-dashed border-green-500/40 rounded-lg p-4 text-center hover:border-green-400/60 transition-all duration-300 glass-neon">
                      <Upload className="h-6 w-6 text-green-300 mx-auto mb-2 animate-float" />
                      <p className="text-sm text-green-300/70 mb-3">Drop image here or click to browse</p>
                      <input
                        type="file"
                        accept="image/*"
                        className="hidden"
                        id="image-upload"
                      />
                      <label
                        htmlFor="image-upload"
                        className="inline-block px-4 py-2 glass-neon border border-green-400/30 text-green-300 hover:border-green-300/50 hover:text-green-200 rounded-lg text-sm font-medium cursor-pointer transition-all duration-300 min-h-[44px] flex items-center justify-center touch-manipulation"
                      >
                        Choose File
                      </label>
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'faceswap' && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-green-200">Thumbnail Upload</label>
                  <div className="border-4 border-dashed border-green-500/40 rounded-lg p-4 text-center hover:border-green-400/60 transition-all duration-300 glass-neon">
                    <Upload className="h-6 w-6 text-green-300 mx-auto mb-2 animate-float" />
                    <p className="text-sm text-green-200 mb-1">Upload or drag & drop</p>
                    <p className="text-xs text-green-300/70 mb-3">PNG, JPG, JPEG & WebP</p>
                    <input
                      type="file"
                      accept="image/png,image/jpg,image/jpeg,image/webp"
                      className="hidden"
                      id="thumbnail-upload"
                    />
                    <label
                      htmlFor="thumbnail-upload"
                                              className="inline-block px-4 py-2 glass-neon border border-green-400/30 text-green-300 hover:border-green-300/50 hover:text-green-200 rounded-lg text-sm font-medium cursor-pointer transition-all duration-300 min-h-[44px] flex items-center justify-center touch-manipulation"
                    >
                      Choose File
                    </label>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col relative h-full">
      {/* Image Display Area - Takes most of the screen */}
      <div className="flex-1 flex flex-col p-2 md:p-4 min-h-0">
        <div className="flex-1 flex items-center justify-center">
          {isGenerating ? (
            <div className="flex flex-col items-center justify-center space-y-4 glass-neon-strong border border-green-400/30 rounded-2xl p-8">
              <div className="animate-spin h-12 w-12 border-3 border-green-300 border-t-transparent rounded-full" />
              <h3 className="text-xl font-semibold text-neon text-center">Creating Your Thumbnail...</h3>
              <p className="text-green-200/80 text-center max-w-md">
                Creating your custom thumbnail...
              </p>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center space-y-4 px-4">
              <div 
                className="w-full max-w-lg backdrop-blur-xl bg-red-500/10 border-2 border-red-400/40 rounded-xl p-6 shadow-[0_0_30px_rgba(239,68,68,0.3)]"
                style={{
                  background: 'linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.05) 50%, rgba(239, 68, 68, 0.1) 100%)',
                  boxShadow: '0 0 30px rgba(239, 68, 68, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1), inset 0 -1px 0 rgba(239, 68, 68, 0.2)'
                }}
              >
                <div className="text-red-400 text-center">
                  <Zap className="h-12 w-12 mx-auto mb-3 animate-subtle-pulse" />
                  <h3 className="text-lg font-semibold mb-2">Generation Failed</h3>
                  <p className="text-sm">{error}</p>
                </div>
              </div>
              <Button
                onClick={handleGenerate}
                className="bg-neon-green hover:bg-neon-green-strong text-white px-6 py-3 rounded-lg min-h-[44px] touch-manipulation border-0 group"
              >
                <span className="group-hover:animate-pulse">Try Again</span>
              </Button>
            </div>
          ) : generatedImage ? (
            <div className="w-full max-w-4xl space-y-4">
              {/* Image Display */}
              <div className="relative group flex items-center justify-center">
                <img
                  src={generatedImage}
                  alt="Generated thumbnail"
                  className={`border-4 border-green-500/40 rounded-xl object-cover object-center image-glow ${
                    aspectRatio === '16:9' ? 'aspect-video w-full max-w-2xl' :
                    aspectRatio === '4:3' ? 'aspect-[4/3] w-full max-w-xl' :
                    aspectRatio === '1:1' ? 'aspect-square w-full max-w-lg' :
                    aspectRatio === '9:16' ? 'aspect-[9/16] w-full max-w-sm max-h-[70vh]' : 'aspect-video w-full max-w-2xl'
                  }`}
                />
              </div>
              
              {/* Image Actions - Icon only buttons */}
              <div className="flex gap-3 justify-center items-center mt-4">
                <button
                  onClick={handleDownload}
                  className="p-3 rounded-full bg-neon-green hover:bg-neon-green-strong text-black transition-all duration-300 min-h-[44px] min-w-[44px] touch-manipulation group"
                  title="Download"
                >
                  <Download className="h-5 w-5 text-black group-hover:animate-bounce" />
                </button>

                <button
                  onClick={() => {
                    // TODO: Implement edit functionality
                    console.log('Edit image clicked')
                  }}
                  className="p-3 rounded-full glass-neon border border-green-500/40 text-green-300 hover:border-green-400/60 hover:text-green-200 transition-all duration-300 min-h-[44px] min-w-[44px] touch-manipulation group"
                  title="Edit Image"
                >
                  <Edit3 className="h-5 w-5 group-hover:scale-110 transition-transform duration-300" />
                </button>

                <button
                  onClick={handleGenerate}
                  className="p-3 rounded-full glass-neon border border-green-500/40 text-green-300 hover:border-green-400/60 hover:text-green-200 transition-all duration-300 min-h-[44px] min-w-[44px] touch-manipulation group"
                  title="Generate Another"
                >
                  <RotateCcw className="h-5 w-5 group-hover:rotate-180 transition-transform duration-300" />
                </button>
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center space-y-6 px-4">
              <div className="text-6xl mb-4 animate-float">🎨</div>
              <h3 className="text-2xl font-semibold text-neon text-center">Ready to Create</h3>
              <p className="text-green-200/80 text-center max-w-md">
                Enter your prompt below and generate stunning AI thumbnails in seconds.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Bottom Controls - Always visible */}
      <div className="flex-shrink-0 p-3 md:p-4">
        <div className="w-full max-w-4xl mx-auto space-y-3">
          {/* Prompt Input */}
          <div className="relative">
                          {activeTab === 'title' ? (
                <textarea
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  placeholder="Enter a title for your thumbnail..."
                  className="w-full h-16 md:h-20 p-4 bg-black/25 border-2 border-green-500/40 rounded-xl text-white placeholder-green-400/50 focus:outline-none focus:border-green-400/70 text-sm md:text-base leading-relaxed touch-manipulation resize-none"
                />
              ) : (
                <textarea
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  placeholder="Describe your vision in vivid detail..."
                  className="w-full h-16 md:h-20 p-4 bg-black/25 border-2 border-green-500/40 rounded-xl text-white placeholder-green-400/50 focus:outline-none focus:border-green-400/70 text-sm md:text-base leading-relaxed touch-manipulation resize-none"
                />
              )}
          </div>

          {/* Controls Row */}
          <div className="flex flex-col sm:flex-row gap-3 sm:items-center sm:justify-between">
            {/* Left: Persona & Style */}
            <div className="flex gap-2 order-2 sm:order-1 relative">
              <div className="relative flex-1 min-w-[140px]">
                <PersonaSelector
                  onPersonaSelect={handlePersonaSelect}
                  showCreateButton={true}
                  className="min-h-[48px] w-full"
                  selectedPersona={selectedPersona}
                  disabled={!!selectedStyle}
                />
              </div>
              <div className="relative flex-1 min-w-[140px]">
                <StyleSelector
                  selectedStyle={selectedStyle}
                  onStyleSelect={handleStyleSelect}
                  className="min-h-[48px] w-full"
                  disabled={!!selectedPersona}
                />
              </div>
            </div>

            {/* Center: Generate Button */}
            <div className="order-1 sm:order-2">
              <Button
                onClick={handleGenerate}
                disabled={isGenerating || !prompt.trim()}
                                  className={`group relative flex items-center justify-center space-x-3 px-6 py-3 w-full sm:w-auto text-base font-bold rounded-xl overflow-hidden transition-all duration-500 min-h-[48px] touch-manipulation border-0 ${
                    isGenerating
                      ? 'bg-slate-600 cursor-not-allowed text-white'
                      : 'btn-neon-generate text-black'
                  }`}
              >
                {isGenerating ? (
                  <>
                    <div className="animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full" />
                    <span>Creating...</span>
                  </>
                ) : (
                  <>
                    <Sparkles className="h-5 w-5 text-black group-hover:animate-pulse" />
                    <span className="hidden sm:inline text-black font-bold">
                      {selectedPersona && selectedStyle
                        ? 'Generate with Persona + Style'
                        : selectedPersona
                        ? `Generate with ${selectedPersona.name}`
                        : selectedStyle
                        ? `Generate with ${selectedStyle.name} Style`
                        : 'Generate'
                      }
                    </span>
                    <span className="sm:hidden text-black font-bold">Generate</span>
                    <Zap className="h-4 w-4 text-black opacity-70 group-hover:animate-bounce" />
                  </>
                )}
                
                {/* Button shine effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />
              </Button>
            </div>

            {/* Right: Options & Clear */}
            <div className="order-3 flex gap-2">
              <Button
                onClick={() => setShowOptionsModal(true)}
                variant="outline"
                className="group flex items-center justify-center space-x-2 px-4 py-3 min-h-[48px] w-full sm:w-auto touch-manipulation glass-neon border-green-500/30 text-green-300 hover:border-green-400/50 hover:text-green-200"
              >
                <Settings className="h-4 w-4 text-green-400" />
                <span className="text-green-300 font-medium text-sm">Options</span>
              </Button>
              
              <Button
                onClick={handleStartNew}
                variant="ghost"
                className="group flex items-center justify-center space-x-2 px-4 py-3 min-h-[48px] w-full sm:w-auto touch-manipulation glass-neon border-green-500/20 hover:border-green-400/30"
              >
                <RotateCcw className="h-4 w-4 text-green-400 group-hover:rotate-180 transition-transform duration-300" />
                <span className="font-medium text-sm text-green-300 group-hover:text-green-200 hidden sm:inline">Clear</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Options Modal */}
      <OptionsModal />
    </div>
  )
} 