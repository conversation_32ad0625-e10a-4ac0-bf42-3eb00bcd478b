import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Testing Replicate API connection...')
    
    // Check environment variables
    const hasToken = !!process.env.REPLICATE_API_TOKEN
    const tokenLength = process.env.REPLICATE_API_TOKEN?.length || 0
    const appUrl = process.env.NEXT_PUBLIC_APP_URL
    
    console.log('🔍 Environment check:', {
      hasToken,
      tokenLength,
      appUrl
    })
    
    if (!hasToken) {
      return NextResponse.json({
        error: 'REPLICATE_API_TOKEN not configured',
        hasToken: false,
        tokenLength: 0
      }, { status: 500 })
    }
    
    // Test Replicate API with simple GET request
    const response = await fetch('https://api.replicate.com/v1/models', {
      method: 'GET',
      headers: {
        'Authorization': `Token ${process.env.REPLICATE_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
    })
    
    const isWorking = response.ok
    let responseData = null
    
    try {
      if (response.ok) {
        responseData = await response.json()
      } else {
        responseData = await response.text()
      }
    } catch (parseError) {
      responseData = 'Could not parse response'
    }
    
    console.log('🔍 Replicate API test result:', {
      status: response.status,
      ok: response.ok,
      statusText: response.statusText
    })
    
    return NextResponse.json({
      success: isWorking,
      replicate: {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        hasToken,
        tokenLength,
        responsePreview: typeof responseData === 'string' 
          ? responseData.substring(0, 200) 
          : `${Object.keys(responseData || {}).length} models found`
      },
      environment: {
        hasToken,
        tokenLength,
        appUrl
      },
      timestamp: new Date().toISOString()
    })
    
  } catch (error) {
    console.error('🔍 Replicate connection test failed:', error)
    
    return NextResponse.json({
      error: 'Connection test failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  return GET(request)
} 