-- Supabase Database Schema for Thumbnex Persona Storage
-- Run this SQL in your Supabase SQL Editor to create the personas table

-- Create the personas table
CREATE TABLE IF NOT EXISTS personas (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  image_url TEXT NOT NULL,
  image_base64 TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  usage_count INTEGER DEFAULT 0,
  category TEXT DEFAULT 'other',
  is_default BOOLEAN DEFAULT FALSE,
  last_used TIMESTAMPTZ DEFAULT NOW(),
  lora_training JSONB -- Stores LoRA training configuration and status
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_personas_created_at ON personas(created_at);
CREATE INDEX IF NOT EXISTS idx_personas_is_default ON personas(is_default);
CREATE INDEX IF NOT EXISTS idx_personas_usage_count ON personas(usage_count);
CREATE INDEX IF NOT EXISTS idx_personas_category ON personas(category);

-- Enable Row Level Security (RLS) for security
ALTER TABLE personas ENABLE ROW LEVEL SECURITY;

-- Create policy to allow all operations for now
-- In production, you might want to restrict this based on user authentication
CREATE POLICY "Allow all operations on personas" ON personas
  FOR ALL USING (true);

-- Insert default personas (optional)
INSERT INTO personas (
  id, name, description, image_url, image_base64, 
  category, is_default, usage_count
) VALUES 
(
  'default-casual',
  'Casual Look',
  'Relaxed, everyday appearance',
  '/placeholder-thumbnail.svg',
  '',
  'casual',
  true,
  0
),
(
  'default-professional',
  'Professional Look', 
  'Business-ready appearance',
  '/placeholder-thumbnail.svg',
  '',
  'professional',
  false,
  0
)
ON CONFLICT (id) DO NOTHING;

-- Function to automatically update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at when row is modified
CREATE TRIGGER update_personas_updated_at 
  BEFORE UPDATE ON personas 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- TRAINING JOBS TABLE
-- =============================================

-- Create training_jobs table for tracking LoRA training
CREATE TABLE IF NOT EXISTS training_jobs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  persona_id TEXT NOT NULL REFERENCES personas(id) ON DELETE CASCADE,
  replicate_training_id TEXT NOT NULL UNIQUE,
  status TEXT NOT NULL CHECK (status IN ('pending', 'training', 'completed', 'failed')) DEFAULT 'pending',
  progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
  training_images_count INTEGER NOT NULL,
  trigger_word TEXT NOT NULL,
  model_url TEXT,
  training_cost DECIMAL(10,2) DEFAULT 0.00,
  estimated_completion TIMESTAMPTZ,
  started_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  error_message TEXT,
  webhook_data JSONB DEFAULT '{}'::jsonb
);

-- Create indexes for training_jobs
CREATE INDEX IF NOT EXISTS idx_training_jobs_persona_id ON training_jobs(persona_id);
CREATE INDEX IF NOT EXISTS idx_training_jobs_status ON training_jobs(status);
CREATE INDEX IF NOT EXISTS idx_training_jobs_replicate_id ON training_jobs(replicate_training_id);
CREATE INDEX IF NOT EXISTS idx_training_jobs_started_at ON training_jobs(started_at);

-- Enable RLS for training_jobs
ALTER TABLE training_jobs ENABLE ROW LEVEL SECURITY;

-- Create policy for training_jobs
CREATE POLICY "Allow all operations on training_jobs" ON training_jobs
  FOR ALL USING (true);

-- Trigger for training_jobs updated_at
CREATE TRIGGER update_training_jobs_updated_at 
  BEFORE UPDATE ON training_jobs 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- WEBHOOK EVENTS TABLE
-- =============================================

-- Create webhook_events table for audit trail
CREATE TABLE IF NOT EXISTS webhook_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_type TEXT NOT NULL,
  source TEXT NOT NULL DEFAULT 'replicate',
  payload JSONB NOT NULL,
  signature_verified BOOLEAN DEFAULT false,
  processed BOOLEAN DEFAULT false,
  retry_count INTEGER DEFAULT 0,
  training_job_id UUID REFERENCES training_jobs(id) ON DELETE SET NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  processed_at TIMESTAMPTZ
);

-- Create indexes for webhook_events
CREATE INDEX IF NOT EXISTS idx_webhook_events_event_type ON webhook_events(event_type);
CREATE INDEX IF NOT EXISTS idx_webhook_events_source ON webhook_events(source);
CREATE INDEX IF NOT EXISTS idx_webhook_events_processed ON webhook_events(processed);
CREATE INDEX IF NOT EXISTS idx_webhook_events_training_job_id ON webhook_events(training_job_id);
CREATE INDEX IF NOT EXISTS idx_webhook_events_created_at ON webhook_events(created_at);

-- Enable RLS for webhook_events
ALTER TABLE webhook_events ENABLE ROW LEVEL SECURITY;

-- Create policy for webhook_events
CREATE POLICY "Allow all operations on webhook_events" ON webhook_events
  FOR ALL USING (true);

-- =============================================
-- AUTOMATIC STATUS UPDATE TRIGGER
-- =============================================

-- Function to automatically update persona status when training job completes
CREATE OR REPLACE FUNCTION update_persona_training_status()
RETURNS TRIGGER AS $$
BEGIN
  -- Only process status changes
  IF OLD.status IS DISTINCT FROM NEW.status THEN
    
    -- Update persona LoRA training status when job completes
    IF NEW.status = 'completed' AND NEW.model_url IS NOT NULL THEN
      UPDATE personas 
      SET 
        lora_training = jsonb_set(
          jsonb_set(
            jsonb_set(
              COALESCE(lora_training, '{}'::jsonb),
              '{status}', 
              '"completed"'
            ),
            '{modelUrl}', 
            to_jsonb(NEW.model_url)
          ),
          '{completedAt}',
          to_jsonb(NEW.completed_at)
        ),
        updated_at = NOW()
      WHERE id = NEW.persona_id;
      
    ELSIF NEW.status = 'failed' THEN
      UPDATE personas 
      SET 
        lora_training = jsonb_set(
          jsonb_set(
            COALESCE(lora_training, '{}'::jsonb),
            '{status}', 
            '"failed"'
          ),
          '{errorMessage}',
          to_jsonb(COALESCE(NEW.error_message, 'Training failed'))
        ),
        updated_at = NOW()
      WHERE id = NEW.persona_id;
      
    ELSIF NEW.status = 'training' THEN
      UPDATE personas 
      SET 
        lora_training = jsonb_set(
          jsonb_set(
            COALESCE(lora_training, '{}'::jsonb),
            '{status}', 
            '"training"'
          ),
          '{trainingProgress}',
          to_jsonb(NEW.progress)
        ),
        updated_at = NOW()
      WHERE id = NEW.persona_id;
    END IF;
    
  END IF;
  
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for automatic persona updates
CREATE TRIGGER update_persona_on_training_status_change
  AFTER UPDATE ON training_jobs
  FOR EACH ROW 
  EXECUTE FUNCTION update_persona_training_status(); 
>>>>>>> 1a9ae100159d3d3ed24d9535a871596fe3a17d3d
