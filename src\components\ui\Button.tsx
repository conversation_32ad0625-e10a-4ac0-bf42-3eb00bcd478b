'use client'

import * as React from 'react'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '../../lib/utils'

const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-accent-primary/50 focus-visible:ring-offset-2 focus-visible:ring-offset-bg-primary disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default: 'bg-bg-secondary text-white border border-white/20 hover:border-accent-primary hover:bg-bg-tertiary hover:shadow-[0_0_10px_rgba(251,86,7,0.2)] active:bg-accent-primary active:border-accent-primary active:shadow-[0_0_15px_rgba(251,86,7,0.4)] data-[state=active]:bg-accent-primary data-[state=active]:border-accent-primary data-[state=active]:shadow-[0_0_15px_rgba(251,86,7,0.4)]',
        secondary: 'bg-bg-secondary text-white border border-white/10 hover:border-accent-primary hover:bg-bg-tertiary hover:shadow-[0_0_10px_rgba(251,86,7,0.2)] active:bg-accent-primary active:border-accent-primary active:shadow-[0_0_15px_rgba(251,86,7,0.4)] data-[state=active]:bg-accent-primary data-[state=active]:border-accent-primary data-[state=active]:shadow-[0_0_15px_rgba(251,86,7,0.4)]',
        ghost: 'bg-transparent text-white hover:bg-bg-tertiary hover:border-accent-primary hover:shadow-[0_0_10px_rgba(251,86,7,0.2)] active:bg-accent-primary active:border-accent-primary active:text-white active:shadow-[0_0_15px_rgba(251,86,7,0.4)] data-[state=active]:bg-accent-primary data-[state=active]:border-accent-primary data-[state=active]:text-white data-[state=active]:shadow-[0_0_15px_rgba(251,86,7,0.4)]',
        outline: 'bg-transparent text-white border border-white/30 hover:bg-accent-primary/10 hover:border-accent-primary hover:shadow-[0_0_10px_rgba(251,86,7,0.2)] active:bg-accent-primary active:border-accent-primary active:text-white active:shadow-[0_0_15px_rgba(251,86,7,0.4)] data-[state=active]:bg-accent-primary data-[state=active]:border-accent-primary data-[state=active]:text-white data-[state=active]:shadow-[0_0_15px_rgba(251,86,7,0.4)]',
        success: 'bg-accent-success text-white border border-accent-success hover:bg-accent-success/90 hover:shadow-[0_0_10px_rgba(16,185,129,0.3)] active:bg-accent-success/80 active:shadow-[0_0_15px_rgba(16,185,129,0.4)]',
        warning: 'bg-accent-warning text-white border border-accent-warning hover:bg-accent-warning/90 hover:shadow-[0_0_10px_rgba(251,191,36,0.3)] active:bg-accent-warning/80 active:shadow-[0_0_15px_rgba(251,191,36,0.4)]',
        danger: 'bg-accent-danger text-white border border-accent-danger hover:bg-accent-danger/90 hover:shadow-[0_0_10px_rgba(239,68,68,0.3)] active:bg-accent-danger/80 active:shadow-[0_0_15px_rgba(239,68,68,0.4)]',
        primary: 'bg-accent-primary text-white border border-accent-primary hover:bg-accent-primary/90 hover:shadow-[0_0_10px_rgba(251,86,7,0.3)] active:bg-accent-primary/80 active:shadow-[0_0_15px_rgba(251,86,7,0.4)]',
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8',
        icon: 'h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  active?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, active, ...props }, ref) => {
    return (
      <button
        className={cn(
          buttonVariants({ variant, size }),
          active && 'bg-accent-primary border-accent-primary text-white shadow-[0_0_15px_rgba(251,86,7,0.4)]',
          className
        )}
        data-state={active ? 'active' : undefined}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = 'Button'

export { Button, buttonVariants } 