import { useState, useCallback, useEffect } from 'react'
import { Persona, CreatePersonaRequest } from '../types/persona'

interface UsePersonasReturn {
  personas: Persona[]
  selectedPersona: Persona | null
  isLoading: boolean
  error: string | null
  
  // Actions
  createPersona: (request: CreatePersonaRequest) => Promise<Persona | null>
  updatePersona: (id: string, updates: Partial<Persona>) => Promise<Persona | null>
  deletePersona: (id: string) => Promise<boolean>
  selectPersona: (persona: Persona | null) => void
  setDefaultPersona: (id: string) => Promise<boolean>
  refreshPersonas: () => Promise<void>
  incrementUsage: (id: string) => Promise<void>
  resetError: () => void
}

export function usePersonas(): UsePersonasReturn {
  const [personas, setPersonas] = useState<Persona[]>([])
  const [selectedPersona, setSelectedPersona] = useState<Persona | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch all personas
  const refreshPersonas = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      const response = await fetch('/api/personas')
      
      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ API Error Response:', errorText)
        throw new Error(`API Error: ${response.status}`)
      }
      
      const responseText = await response.text()
      // Only log when there are actual changes or errors, not every time
      
      let data
      try {
        data = JSON.parse(responseText)
      } catch (parseError) {
        console.error('❌ JSON Parse Error:', parseError)
        console.error('❌ Response text:', responseText)
        throw new Error('Invalid response format from API')
      }
      
      setPersonas(data.personas || [])
      
      // Auto-select default persona if none selected
      if (!selectedPersona && data.personas?.length > 0) {
        const defaultPersona = data.personas.find((p: Persona) => p.isDefault) || data.personas[0]
        setSelectedPersona(defaultPersona)
      }
      
      console.log(`✅ Loaded ${data.personas?.length || 0} personas`)
    } catch (error) {
      console.error('❌ Error fetching personas:', error)
      setError(error instanceof Error ? error.message : 'Unknown error occurred')
    } finally {
      setIsLoading(false)
    }
  }, [selectedPersona])

  // Create new persona
  const createPersona = useCallback(async (request: CreatePersonaRequest): Promise<Persona | null> => {
    try {
      setError(null)
      console.log('➕ Creating persona with multiple images:', request.name, `${request.imageFiles.length} images`)
      
      const formData = new FormData()
      formData.append('name', request.name)
      if (request.description) formData.append('description', request.description)
      if (request.category) formData.append('category', request.category)
      if (request.isDefault) formData.append('isDefault', 'true')
      if (request.triggerWord) formData.append('triggerWord', request.triggerWord)
      
      // Append all image files
      request.imageFiles.forEach((file, index) => {
        formData.append(`imageFile_${index}`, file)
      })
      formData.append('imageCount', request.imageFiles.length.toString())
      
      const response = await fetch('/api/personas', {
        method: 'POST',
        body: formData
      })
      
      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ Create API Error Response:', errorText)
        
        // Try to parse error message
        try {
          const errorData = JSON.parse(errorText)
          throw new Error(errorData.error || `Failed to create persona: ${response.status}`)
        } catch {
          throw new Error(`Failed to create persona: ${response.status}`)
        }
      }
      
      const responseText = await response.text()
      console.log('📄 Create API Response:', responseText)
      
      let data
      try {
        data = JSON.parse(responseText)
      } catch (parseError) {
        console.error('❌ Create JSON Parse Error:', parseError)
        console.error('❌ Response text:', responseText)
        throw new Error('Invalid response format from create API')
      }
      
      // Add to local state
      setPersonas(prev => [data.persona, ...prev])
      
      // Select as default if it's the first persona or explicitly set as default
      if (request.isDefault || personas.length === 0) {
        setSelectedPersona(data.persona)
      }
      
      console.log('✅ Created persona with multiple images:', data.persona.name)
      return data.persona
    } catch (error) {
      console.error('❌ Error creating persona:', error)
      setError(error instanceof Error ? error.message : 'Failed to create persona')
      return null
    }
  }, [personas.length])

  // Update persona
  const updatePersona = useCallback(async (id: string, updates: Partial<Persona>): Promise<Persona | null> => {
    try {
      setError(null)
      console.log('📝 Updating persona:', id)
      
      const response = await fetch('/api/personas', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ id, ...updates })
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to update persona')
      }
      
      // Update local state
      setPersonas(prev => 
        prev.map(p => p.id === id ? data.persona : p)
      )
      
      // Update selected persona if it's the one being updated
      if (selectedPersona?.id === id) {
        setSelectedPersona(data.persona)
      }
      
      console.log('✅ Updated persona:', data.persona.name)
      return data.persona
    } catch (error) {
      console.error('❌ Error updating persona:', error)
      setError(error instanceof Error ? error.message : 'Failed to update persona')
      return null
    }
  }, [selectedPersona])

  // Delete persona
  const deletePersona = useCallback(async (id: string): Promise<boolean> => {
    try {
      setError(null)
      console.log('🗑️ Deleting persona:', id)
      
      const response = await fetch(`/api/personas?id=${id}`, {
        method: 'DELETE'
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete persona')
      }
      
      // Remove from local state
      setPersonas(prev => prev.filter(p => p.id !== id))
      
      // Clear selection if deleted persona was selected
      if (selectedPersona?.id === id) {
        const remainingPersonas = personas.filter(p => p.id !== id)
        const newDefault = remainingPersonas.find(p => p.isDefault) || remainingPersonas[0] || null
        setSelectedPersona(newDefault)
      }
      
      console.log('✅ Deleted persona')
      return true
    } catch (error) {
      console.error('❌ Error deleting persona:', error)
      setError(error instanceof Error ? error.message : 'Failed to delete persona')
      return false
    }
  }, [selectedPersona, personas])

  // Select persona
  const selectPersona = useCallback((persona: Persona | null) => {
    setSelectedPersona(persona)
    console.log('👤 Selected persona:', persona?.name || 'None')
  }, [])

  // Set default persona
  const setDefaultPersona = useCallback(async (id: string): Promise<boolean> => {
    const result = await updatePersona(id, { isDefault: true })
    return result !== null
  }, [updatePersona])

  // Increment usage count
  const incrementUsage = useCallback(async (id: string) => {
    const persona = personas.find(p => p.id === id)
    if (persona) {
      await updatePersona(id, { usageCount: persona.usageCount + 1 })
    }
  }, [personas, updatePersona])

  // Reset error
  const resetError = useCallback(() => {
    setError(null)
  }, [])

  // Load personas on mount
  useEffect(() => {
    refreshPersonas()
  }, [refreshPersonas])

  // Listen for refresh events (e.g., from training status updates)
  useEffect(() => {
    const handleRefresh = () => {
      console.log('🔄 Received refresh event, updating personas...')
      refreshPersonas()
    }

    window.addEventListener('refreshPersonas', handleRefresh)
    
    return () => {
      window.removeEventListener('refreshPersonas', handleRefresh)
    }
  }, [refreshPersonas])

  return {
    personas,
    selectedPersona,
    isLoading,
    error,
    createPersona,
    updatePersona,
    deletePersona,
    selectPersona,
    setDefaultPersona,
    refreshPersonas,
    incrementUsage,
    resetError
  }
} 