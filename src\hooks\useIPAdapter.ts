import { useState, useCallback } from 'react'
import { Persona, StyleTemplate } from '../types/persona'

interface GenerationOptions {
  prompt: string
  persona?: Persona
  styleTemplate?: StyleTemplate
  faceStrength?: number
  styleStrength?: number
  expressionOverride?: string
}

interface GenerationResult {
  imageUrl: string
  model: string
  steps?: {
    baseGeneration: string
    styleTransfer: string | null
    faceSwap: string | null
  }
  metadata?: {
    prompt: string
    faceStrength: number
    styleStrength: number
    expressionOverride: string
    hasStyle: boolean
    hasFace: boolean
  }
}

export function useIPAdapter() {
  const [isGenerating, setIsGenerating] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastResult, setLastResult] = useState<GenerationResult | null>(null)

  const generateThumbnail = useCallback(async (options: GenerationOptions): Promise<GenerationResult> => {
    const {
      prompt,
      persona,
      styleTemplate,
      faceStrength = 0.8,
      styleStrength = 0.6,
      expressionOverride = 'auto'
    } = options

    setIsGenerating(true)
    setError(null)

    try {
      // Prepare face image data if persona is provided
      let faceImageData: string | undefined
      if (persona) {
        // Convert persona image to base64 if it's not already
        if (persona.imageUrl.startsWith('data:')) {
          faceImageData = persona.imageUrl
        } else {
          // Fetch and convert external image to base64
          try {
            const response = await fetch(persona.imageUrl)
            const blob = await response.blob()
            faceImageData = await new Promise<string>((resolve) => {
              const reader = new FileReader()
              reader.onloadend = () => resolve(reader.result as string)
              reader.readAsDataURL(blob)
            })
          } catch (fetchError) {
            console.warn('Failed to fetch persona image, proceeding without face:', fetchError)
          }
        }
      }

      // Prepare style image data if style template is provided
      let styleImageData: string | undefined
      if (styleTemplate) {
        // Convert style template image to base64 if it's not already
        if (styleTemplate.imageUrl.startsWith('data:')) {
          styleImageData = styleTemplate.imageUrl
        } else {
          // Fetch and convert external image to base64
          try {
            const response = await fetch(styleTemplate.imageUrl)
            const blob = await response.blob()
            styleImageData = await new Promise<string>((resolve) => {
              const reader = new FileReader()
              reader.onloadend = () => resolve(reader.result as string)
              reader.readAsDataURL(blob)
            })
          } catch (fetchError) {
            console.warn('Failed to fetch style image, proceeding without style:', fetchError)
          }
        }
      }

      // Make API request
      const response = await fetch('/api/ip-adapter-generation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt,
          model: 'flux-lora-enhanced',
          faceStrength,
          styleStrength,
          expressionOverride,
          faceImageData,
          styleImageData,
          persona
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.details || errorData.error || 'Generation failed')
      }

      const result: GenerationResult = await response.json()
      setLastResult(result)
      
      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setIsGenerating(false)
    }
  }, [])

  const generateWithPersonaOnly = useCallback(async (
    prompt: string,
    persona: Persona,
    faceStrength: number = 0.8,
    expressionOverride: string = 'auto'
  ): Promise<GenerationResult> => {
    return generateThumbnail({
      prompt,
      persona,
      faceStrength,
      expressionOverride
    })
  }, [generateThumbnail])

  const generateWithStyleOnly = useCallback(async (
    prompt: string,
    styleTemplate: StyleTemplate,
    styleStrength: number = 0.6
  ): Promise<GenerationResult> => {
    return generateThumbnail({
      prompt,
      styleTemplate,
      styleStrength
    })
  }, [generateThumbnail])

  const generateWithPersonaAndStyle = useCallback(async (
    prompt: string,
    persona: Persona,
    styleTemplate: StyleTemplate,
    faceStrength: number = 0.8,
    styleStrength: number = 0.6,
    expressionOverride: string = 'auto'
  ): Promise<GenerationResult> => {
    return generateThumbnail({
      prompt,
      persona,
      styleTemplate,
      faceStrength,
      styleStrength,
      expressionOverride
    })
  }, [generateThumbnail])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  const clearLastResult = useCallback(() => {
    setLastResult(null)
  }, [])

  return {
    isGenerating,
    error,
    lastResult,
    generateThumbnail,
    generateWithPersonaOnly,
    generateWithStyleOnly,
    generateWithPersonaAndStyle,
    clearError,
    clearLastResult,
  }
}

 