# 🗄️ Supabase Setup Guide for Thumbnex

## 🚨 **Problem Solved**
**In-memory storage doesn't work on serverless functions!** Each API call runs in a separate function instance, so personas created in one call disappear when accessed from another call.

## ✅ **Solution: Persistent Database Storage**

### **Step 1: Create Supabase Account**
1. Go to [supabase.com](https://supabase.com)
2. Sign up for free
3. Create a new project

### **Step 2: Get Your Credentials**
1. Go to **Settings** → **API**
2. Copy these values:
   - **Project URL**: `https://your-project-ref.supabase.co`
   - **Anon Public Key**: `eyJhbG...` (long string)

### **Step 3: Configure Environment Variables**
Create `.env.local` file in your project root:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your.anon.key.here

# Your existing variables
REPLICATE_API_TOKEN=your_replicate_api_token_here
WEBHOOK_SECRET=your_webhook_secret_here
```

### **Step 4: Create Database Table**
1. In Supabase dashboard, go to **SQL Editor**
2. Copy and paste the content from `database-schema.sql`
3. Click **Run** to create the table

### **Step 5: Test the Connection**
```bash
npm run dev
```

The app will now:
- ✅ **Local Development**: Use in-memory storage (shows in console)
- ✅ **Production**: Use Supabase database automatically
- ✅ **Fallback**: If Supabase fails, falls back to memory

## 🔍 **How It Works Now**

### **Before (Broken):**
```
Create Persona → Memory Instance A ✅
Train LoRA → Memory Instance B ❌ (persona not found)
```

### **After (Fixed):**
```
Create Persona → Supabase Database ✅
Train LoRA → Supabase Database ✅ (persona found)
```

## 🎯 **Benefits**

1. **✅ Persistent Storage**: Personas survive between API calls
2. **🔄 Automatic Fallback**: Works locally without Supabase
3. **🚀 Production Ready**: Scales with your app
4. **💾 Data Backup**: Supabase handles backups automatically
5. **🔒 Secure**: Row Level Security enabled

## 🛠️ **Development Mode**

Without Supabase configured, the app uses in-memory storage for local development:
```
💾 Using in-memory storage (local development mode)
🔄 Initializing personas cache with defaults...
```

## 🌟 **Production Mode**

With Supabase configured:
```
🗄️ Loading personas from Supabase database...
✅ Loaded 3 personas from database
🗄️ Saving 4 personas to Supabase database...
✅ Successfully saved 4 personas to database
```

## 🆘 **Troubleshooting**

### **Problem: "Persona not found" error**
- ✅ **Solution**: Set up Supabase (this guide)

### **Problem: Connection errors**
- Check your `.env.local` credentials
- Verify Supabase project is active
- Check network connection

### **Problem: Permission denied**
- Database policy allows all operations by default
- Check if RLS is properly configured

## 🎉 **You're Done!**

Your persona system now has persistent storage and LoRA training will work correctly! 🚀 