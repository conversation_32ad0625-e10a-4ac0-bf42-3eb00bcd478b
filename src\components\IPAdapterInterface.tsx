'use client'

import React, { useState, useCallback, useEffect } from 'react'
import { Upload, User, Settings, Sparkles, Image as ImageIcon } from 'lucide-react'
import { Button } from './ui/Button'
import { PersonaSelector } from './PersonaSelector'
import { Persona } from '../types/persona'

interface IPAdapterInterfaceProps {
  onGenerate: (params: IPAdapterParams) => void
  isGenerating: boolean
}

interface IPAdapterParams {
  prompt: string
  faceImage: File | string
  model: string
  faceStrength: number
  expressionOverride: string
  stylePrompt?: string
}

const LORA_MODELS = [
  { 
    id: 'flux-lora-enhanced', 
    name: 'FLUX + Custom LoRA', 
    description: 'Custom trained model for perfect face consistency - requires persona training' 
  }
];

const EXPRESSION_OPTIONS = [
  { value: 'auto', label: 'Auto (from prompt)' },
  { value: 'neutral', label: 'Neutral' },
  { value: 'happy', label: 'Happy/Excited' },
  { value: 'surprised', label: 'Surprised/Shocked' },
  { value: 'focused', label: 'Focused/Serious' },
  { value: 'smiling', label: 'Smiling' },
  { value: 'confident', label: 'Confident' }
]

export function IPAdapterInterface({ onGenerate, isGenerating }: IPAdapterInterfaceProps) {
  const [prompt, setPrompt] = useState('')
  const [selectedPersona, setSelectedPersona] = useState<Persona | null>(null)
  const [selectedModel, setSelectedModel] = useState('flux-lora-enhanced')
  const [faceStrength, setFaceStrength] = useState(80)
  const [expressionOverride, setExpressionOverride] = useState('auto')
  const [stylePrompt, setStylePrompt] = useState('')
  const [showAdvanced, setShowAdvanced] = useState(false)

  // Polling for training status updates
  useEffect(() => {
    if (!selectedPersona?.loraTraining?.trainingId || 
        selectedPersona?.loraTraining?.status !== 'training') {
      return
    }

    console.log('🔄 Starting training status polling for:', selectedPersona.loraTraining.trainingId)
    let lastKnownStatus = selectedPersona.loraTraining.status
    let lastKnownProgress = selectedPersona.loraTraining.trainingProgress || 0

    const pollTrainingStatus = async () => {
      try {
        const response = await fetch(
          `/api/train-lora-direct?trainingId=${selectedPersona.loraTraining?.trainingId}`
        )
        
        if (response.ok) {
          const data = await response.json()
          
          // Only refresh personas if status or significant progress change occurred
          const statusChanged = data.status !== lastKnownStatus
          const significantProgressChange = Math.abs((data.progress || 0) - lastKnownProgress) >= 10
          
          if (statusChanged || significantProgressChange) {
            console.log('📊 Training status changed:', {
              status: `${lastKnownStatus} → ${data.status}`,
              progress: `${lastKnownProgress}% → ${data.progress || 0}%`
            })
            
            // Update tracking variables
            lastKnownStatus = data.status
            lastKnownProgress = data.progress || 0
            
            // Force refresh personas to get updated status
            window.dispatchEvent(new CustomEvent('refreshPersonas'))
          }
        }
      } catch (error) {
        console.error('❌ Failed to poll training status:', error)
      }
    }

    // Poll every 30 seconds while training
    const interval = setInterval(pollTrainingStatus, 30000)
    
    // Initial poll
    pollTrainingStatus()

    return () => {
      console.log('🛑 Stopping training status polling')
      clearInterval(interval)
    }
  }, [selectedPersona?.loraTraining?.trainingId, selectedPersona?.loraTraining?.status])

  // Handle persona selection
  const handlePersonaSelect = useCallback((persona: Persona | null) => {
    console.log('👤 Persona selected:', persona?.name || 'None')
    setSelectedPersona(persona)
  }, [])

  // Handle training initiation
  const handleTrainLora = useCallback(async () => {
    if (!selectedPersona) {
      alert('Please select a persona to train')
      return
    }

    if (!selectedPersona.loraTraining) {
      alert('This persona has no training data. Please recreate the persona with 10+ images.')
      return
    }

    if (selectedPersona.loraTraining.status === 'training') {
      alert('LoRA training is already in progress for this persona')
      return
    }

    if (selectedPersona.loraTraining.status === 'completed') {
      alert('LoRA training is already completed for this persona')
      return
    }

    const confirmed = confirm(
      `Start LoRA training for "${selectedPersona.name}"?\n\n` +
      `• ${selectedPersona.loraTraining.trainingImages.length} training images\n` +
      `• Trigger word: ${selectedPersona.loraTraining.triggerWord}\n` +
      `• Estimated cost: $2-4 USD\n` +
      `• Estimated time: 15-20 minutes\n\n` +
      `This will create a ZIP file and send it to Replicate for training.`
    )

    if (!confirmed) return

    try {
      console.log('🚀 Starting LoRA training for persona:', selectedPersona.name)
      
      const response = await fetch('/api/train-lora-direct', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          personaId: selectedPersona.id
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Training failed to start')
      }

      alert(
        `✅ LoRA training started successfully!\n\n` +
        `Training ID: ${data.trainingId}\n` +
        `Estimated time: ${data.estimatedTime}\n` +
        `Estimated cost: ${data.estimatedCost}\n\n` +
        `You can monitor progress in the persona selector.`
      )

    } catch (error) {
      console.error('❌ Training start failed:', error)
      alert(`Failed to start training: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }, [selectedPersona])

  // Handle generation
  const handleGenerate = useCallback(() => {
    if (!prompt.trim()) {
      alert('Please enter a prompt')
      return
    }
    
    if (!selectedPersona) {
      alert('Please select a persona')
      return
    }

    const params: IPAdapterParams = {
      prompt: prompt.trim(),
      faceImage: selectedPersona.imageBase64,
      model: selectedModel,
      faceStrength: faceStrength / 100, // Convert to 0-1 range
      expressionOverride,
      stylePrompt: stylePrompt.trim()
    }

    onGenerate(params)
  }, [prompt, selectedPersona, selectedModel, faceStrength, expressionOverride, stylePrompt, onGenerate])

  // Get training status display
  const getTrainingStatusDisplay = () => {
    if (!selectedPersona?.loraTraining) {
      return { icon: '✗', text: 'Not Trained', color: 'text-slate-400' }
    }

    switch (selectedPersona.loraTraining.status) {
      case 'completed':
        return { icon: '✓', text: 'Ready', color: 'text-green-400' }
      case 'training':
        const progress = selectedPersona.loraTraining.trainingProgress || 0
        return { icon: '⏳', text: `Training ${progress}%`, color: 'text-green-300' }
      case 'failed':
        return { icon: '✗', text: 'Failed', color: 'text-red-400' }
      default:
        return { icon: '⏸️', text: 'Pending', color: 'text-slate-400' }
    }
  }

  const trainingStatus = getTrainingStatusDisplay()

  return (
    <div className="space-y-6 p-6 bg-gradient-to-br from-slate-900 to-slate-800 rounded-lg border border-slate-700">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
          <Sparkles className="w-5 h-5 text-white" />
        </div>
        <div>
          <h2 className="text-xl font-bold text-white">FLUX LoRA Generation</h2>
          <p className="text-sm text-slate-400">Generate thumbnails with custom trained models for perfect face consistency</p>
        </div>
      </div>

      {/* Persona Selection */}
      <PersonaSelector
        onPersonaSelect={handlePersonaSelect}
        showCreateButton={true}
        className="space-y-3"
      />



      {/* LoRA Training Status & Controls */}
      {selectedPersona && (
        <div className="bg-slate-800/50 rounded-lg p-4 border border-slate-600">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-white">Custom LoRA Model</h3>
            <span className={`text-sm font-medium ${trainingStatus.color}`}>
              {trainingStatus.icon} {trainingStatus.text}
            </span>
          </div>
          
          {selectedPersona.loraTraining ? (
            <div className="space-y-3">
              <div className="text-xs text-slate-400">
                <p>Trigger word: <span className="text-white font-mono">{selectedPersona.loraTraining.triggerWord}</span></p>
                <p>Training images: {selectedPersona.loraTraining.trainingImages.length}</p>
                {selectedPersona.loraTraining.trainingId && (
                  <p>Training ID: <span className="text-white font-mono text-xs">{selectedPersona.loraTraining.trainingId}</span></p>
                )}
              </div>
              
              {selectedPersona.loraTraining.status === 'pending' && (
                <Button
                  onClick={handleTrainLora}
                  size="sm"
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  <Sparkles className="w-3 h-3 mr-1" />
                  Train Custom LoRA (~$3, 15 min)
                </Button>
              )}
              
              {selectedPersona.loraTraining.status === 'training' && (
                <div className="text-xs text-green-300">
                  ⏳ Training in progress... Check back in 15-20 minutes
                </div>
              )}
              
              {selectedPersona.loraTraining.status === 'completed' && (
                <div className="text-xs text-green-400">
                  ✅ Custom LoRA ready! Use trigger word &quot;{selectedPersona.loraTraining.triggerWord}&quot; in prompts
                </div>
              )}
              
              {selectedPersona.loraTraining.status === 'failed' && (
                <div className="text-xs text-red-400">
                  ❌ Training failed: {selectedPersona.loraTraining.errorMessage}
                </div>
              )}
            </div>
          ) : (
            <div className="text-xs text-slate-400">
              No training data available. Create a new persona with 10+ images to enable LoRA training.
            </div>
          )}
        </div>
      )}

      {/* Prompt Input */}
      <div className="space-y-3">
        <label className="text-sm font-medium text-white">Thumbnail Description</label>
        <textarea
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          placeholder="Describe the thumbnail you want to create..."
          className="w-full h-24 px-3 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
        />
      </div>

      {/* Model Selection */}
      <div className="space-y-3">
        <div>
          <label className="block text-sm font-medium text-white mb-2">
            Generation Model
          </label>
          <select
            value={selectedModel}
            onChange={(e) => setSelectedModel(e.target.value)}
            className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            {LORA_MODELS.map((model) => (
              <option key={model.id} value={model.id}>
                {model.name}
              </option>
            ))}
          </select>
          <p className="text-xs text-slate-400 mt-1">
            {LORA_MODELS.find(m => m.id === selectedModel)?.description}
          </p>
        </div>
      </div>

      {/* Face Strength Control */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <label className="text-sm font-medium text-white">Face Integration Strength</label>
          <span className="text-sm text-purple-400 font-medium">{faceStrength}%</span>
        </div>
        <input
          type="range"
          min="10"
          max="100"
          value={faceStrength}
          onChange={(e) => setFaceStrength(Number(e.target.value))}
          className="w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer slider"
          style={{
            background: `linear-gradient(to right, #8b5cf6 0%, #8b5cf6 ${faceStrength}%, #475569 ${faceStrength}%, #475569 100%)`
          }}
        />
        <div className="flex justify-between text-xs text-slate-400">
          <span>Subtle</span>
          <span>Strong</span>
        </div>
      </div>

      {/* Advanced Settings Toggle */}
      <Button
        onClick={() => setShowAdvanced(!showAdvanced)}
        variant="outline"
        className="w-full flex items-center justify-center space-x-2"
      >
        <Settings className="w-4 h-4" />
        <span>{showAdvanced ? 'Hide' : 'Show'} Advanced Settings</span>
      </Button>

      {/* Advanced Settings */}
      {showAdvanced && (
        <div className="space-y-4 p-4 bg-slate-800/50 rounded-lg border border-slate-700">
          {/* Expression Override */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-white">Expression Override</label>
            <select
              value={expressionOverride}
              onChange={(e) => setExpressionOverride(e.target.value)}
              className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              {EXPRESSION_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Style Prompt */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-white">Additional Style Prompt</label>
            <input
              type="text"
              value={stylePrompt}
              onChange={(e) => setStylePrompt(e.target.value)}
              placeholder="e.g., cinematic lighting, dramatic shadows..."
              className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
          </div>
        </div>
      )}

      {/* Generate Button */}
      <Button
        onClick={handleGenerate}
        disabled={isGenerating || !prompt.trim() || !selectedPersona}
        className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-medium py-3 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isGenerating ? (
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            <span>Generating with LoRA...</span>
          </div>
        ) : (
          <div className="flex items-center space-x-2">
            <ImageIcon className="w-4 h-4" />
            <span>Generate Thumbnail</span>
          </div>
        )}
      </Button>

      {/* Tips */}
      <div className="p-4 bg-slate-800/30 rounded-lg border border-slate-700">
        <h4 className="text-sm font-medium text-white mb-2">💡 LoRA Training Benefits:</h4>
        <ul className="text-xs text-slate-400 space-y-1">
          <li>• <strong>Perfect Face Consistency:</strong> Custom trained model knows your exact face</li>
          <li>• <strong>Native Quality:</strong> No post-processing, pure FLUX generation</li>
          <li>• <strong>Trigger Words:</strong> Use &quot;{selectedPersona?.loraTraining?.triggerWord || 'trigger words'}&quot; for best results</li>
          <li>• <strong>One-time Cost:</strong> ~$2 training cost, then $0.10-0.20 per generation</li>
          <li>• <strong>15-20 min training time:</strong> Worth the wait for professional results</li>
        </ul>
      </div>
    </div>
  )
} 