import { NextRequest, NextResponse } from 'next/server'

/**
 * Test endpoint to verify ZIP cleanup functionality
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action') || 'info'
    
    if (action === 'info') {
      return NextResponse.json({
        message: 'ZIP Cleanup Test Endpoint',
        availableActions: [
          'info - Show this information',
          'test-webhook - Test webhook cleanup flow',
          'test-direct - Test direct cleanup endpoint'
        ],
        endpoints: [
          '/api/webhooks/training-complete - Webhook handler with cleanup',
          '/api/cleanup-training-zips - Direct cleanup endpoint'
        ],
        implementation: {
          personas: 'Cleanup handled in webhook handler',
          styles: 'Cleanup handled via cleanup endpoint call',
          delay: '1-3 minutes after training completion',
          verification: 'Replicate status progression ensures safe cleanup'
        }
      })
    }
    
    if (action === 'test-webhook') {
      // Test webhook flow
      const testWebhookPayload = {
        id: 'test-training-123',
        status: 'succeeded',
        type: 'training',
        destination: 'test-user/test-model',
        output: {
          weights: 'https://example.com/model-weights'
        }
      }
      
      return NextResponse.json({
        message: 'Webhook test payload generated',
        testPayload: testWebhookPayload,
        instructions: [
          '1. POST this payload to /api/webhooks/training-complete',
          '2. Include proper signature header for production',
          '3. Check logs for cleanup scheduling',
          '4. Verify ZIP cleanup after delay'
        ],
        note: 'This is a test payload - will not find actual training records'
      })
    }
    
    if (action === 'test-direct') {
      // Test direct cleanup endpoint
      return NextResponse.json({
        message: 'Direct cleanup test instructions',
        steps: [
          '1. Find a real training ID from your database',
          '2. POST to /api/cleanup-training-zips with { "trainingId": "real-id" }',
          '3. Check response for cleanup success/failure',
          '4. Verify ZIP file was actually deleted from Vercel Blob'
        ],
        example: {
          method: 'POST',
          url: '/api/cleanup-training-zips',
          body: { trainingId: 'your-real-training-id' }
        }
      })
    }
    
    return NextResponse.json({
      error: 'Unknown action',
      availableActions: ['info', 'test-webhook', 'test-direct']
    }, { status: 400 })
    
  } catch (error) {
    console.error('❌ Test endpoint error:', error)
    return NextResponse.json({
      error: 'Test endpoint error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

/**
 * POST endpoint to run actual cleanup tests
 */
export async function POST(request: NextRequest) {
  try {
    const { testType, trainingId } = await request.json()
    
    if (testType === 'simulate-webhook') {
      // Simulate webhook call for testing
      const webhookPayload = {
        id: trainingId || 'test-training-456',
        status: 'succeeded',
        type: 'training',
        destination: 'test-user/test-model'
      }
      
      try {
        const webhookResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/webhooks/training-complete`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'replicate-signature': 'sha256=test-signature' // Note: Will fail signature verification
          },
          body: JSON.stringify(webhookPayload)
        })
        
        const webhookResult = await webhookResponse.json()
        
        return NextResponse.json({
          success: true,
          message: 'Webhook simulation completed',
          webhookPayload,
          webhookResponse: {
            status: webhookResponse.status,
            result: webhookResult
          }
        })
        
      } catch (webhookError) {
        return NextResponse.json({
          error: 'Webhook simulation failed',
          details: webhookError instanceof Error ? webhookError.message : 'Unknown error'
        }, { status: 500 })
      }
    }
    
    if (testType === 'direct-cleanup') {
      if (!trainingId) {
        return NextResponse.json({
          error: 'Training ID required for direct cleanup test'
        }, { status: 400 })
      }
      
      try {
        const cleanupResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/cleanup-training-zips`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ trainingId, forceCleanup: true })
        })
        
        const cleanupResult = await cleanupResponse.json()
        
        return NextResponse.json({
          success: true,
          message: 'Direct cleanup test completed',
          trainingId,
          cleanupResponse: {
            status: cleanupResponse.status,
            result: cleanupResult
          }
        })
        
      } catch (cleanupError) {
        return NextResponse.json({
          error: 'Direct cleanup test failed',
          details: cleanupError instanceof Error ? cleanupError.message : 'Unknown error'
        }, { status: 500 })
      }
    }
    
    return NextResponse.json({
      error: 'Unknown test type',
      availableTypes: ['simulate-webhook', 'direct-cleanup']
    }, { status: 400 })
    
  } catch (error) {
    console.error('❌ Test POST error:', error)
    return NextResponse.json({
      error: 'Test POST error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
} 