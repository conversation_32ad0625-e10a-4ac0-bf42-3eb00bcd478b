# Dot Pattern Background Guide

## Overview

Thumbnex now features a beautiful black background with white dots pattern that creates a modern, professional look while maintaining excellent readability and visual appeal.

## Default Pattern

The main page now uses:
- **Background**: Pure black (#000000)  
- **Dots**: Semi-transparent white dots (15% opacity)
- **Spacing**: 24px grid with 1px dots
- **Effect**: Evenly distributed pattern across the entire page

## Available Pattern Variants

You can use these CSS utility classes to apply different dot patterns:

### 1. **Standard (.bg-dots)**
```css
background-size: 24px 24px
dot-size: 1px
opacity: 15%
```
- Balanced spacing and visibility
- Default pattern used on the main page

### 2. **Subtle (.bg-dots-subtle)**
```css
background-size: 20px 20px
dot-size: 1px  
opacity: 8%
```
- More subdued dots for less distraction
- Good for content-heavy areas

### 3. **Large (.bg-dots-large)**
```css
background-size: 32px 32px
dot-size: 1.5px
opacity: 12%
```
- Bigger dots with wider spacing
- More prominent visual effect

### 4. **Dense (.bg-dots-dense)**
```css
background-size: 12px 12px
dot-size: 0.5px
opacity: 10%
```
- Tightly packed small dots
- Creates fine texture effect

### 5. **Sparse (.bg-dots-sparse)**
```css
background-size: 48px 48px
dot-size: 1px
opacity: 20%
```
- Wide spacing with more visible dots
- Minimalist approach

### 6. **Grid (.bg-dots-grid)**
```css
dual-layer: primary (24x24) + secondary (12x12)
dot-sizes: 1px + 1px
opacities: 10% + 5%
```
- Two-layer pattern with offset positioning
- Creates complex grid effect

### 7. **Animated (.bg-dots-animated)**
```css
background-size: 24px 24px
animation: 30s linear infinite floating
```
- Slowly moving dots that create subtle motion
- Adds dynamic element to the background

## Usage Examples

### Apply to any element:
```tsx
<div className="bg-dots">
  Content with standard dot pattern
</div>

<div className="bg-dots-subtle">
  Content with subtle dots
</div>

<div className="bg-dots-animated">
  Content with animated floating dots
</div>
```

### Switching body patterns:
```tsx
// Add class to body element
document.body.className = 'dots-subtle'  // Subtle pattern
document.body.className = 'dots-large'   // Large pattern  
document.body.className = 'dots-grid'    // Grid pattern
```

## Technical Details

### Performance
- Uses CSS `radial-gradient()` for optimal performance
- No images required - pure CSS implementation
- GPU-accelerated rendering for smooth animations
- Minimal impact on page load times

### Accessibility
- Low contrast ensures text readability
- Supports `prefers-reduced-motion` for animation control
- High contrast mode compatible

### Browser Support
- Modern browsers (Chrome, Firefox, Safari, Edge)
- Graceful fallback to solid black background
- Mobile optimized for touch devices

## Customization

### Create Custom Patterns
```css
.bg-dots-custom {
  background-color: #000000;
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.12) 1px, transparent 1px);
  background-size: 28px 28px;
}
```

### Animation Variations
```css
@keyframes dotsCustom {
  0% { background-position: 0 0; }
  100% { background-position: 28px 28px; }
}

.bg-dots-custom-animated {
  animation: dotsCustom 45s linear infinite;
}
```

## Integration with Glass Morphism

The dot patterns work perfectly with the existing glass morphism effects:
- **Glass elements** maintain transparency and blur effects
- **Neon glows** stand out beautifully against the dotted background  
- **Content readability** remains excellent
- **Depth perception** enhanced by the subtle pattern

## Best Practices

1. **Use sparingly**: Apply different patterns only when needed
2. **Content first**: Ensure patterns don't interfere with readability
3. **Performance**: Prefer static patterns over animated ones for most use cases
4. **Consistency**: Stick to one pattern per page for cohesive design
5. **Testing**: Check appearance across different devices and screen sizes

The dotted background adds a sophisticated touch to Thumbnex while maintaining the clean, modern aesthetic that users expect from a professional AI tool.