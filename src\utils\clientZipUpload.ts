import JS<PERSON>ip from 'jszip'

export interface TrainingImage {
  file: File
  base64?: string
}

/**
 * Create ZIP file from training images on client side
 */
export async function createTrainingZipClient(images: TrainingImage[], triggerWord: string): Promise<Blob> {
  try {
    console.log(`📦 Creating ZIP from ${images.length} images...`)
    
    if (images.length === 0) {
      throw new Error('No images provided for ZIP creation')
    }
    
    // Filter to only include valid image files (check both MIME type and magic bytes)
    const validImageTypes = ['image/jpeg', 'image/png', 'image/webp']
    const validImages: TrainingImage[] = []
    
    console.log('🔍 Validating image files...')
    for (const image of images) {
      // Normalize MIME type (some systems incorrectly report image/jpg instead of image/jpeg)
      const normalizedMimeType = image.file.type === 'image/jpg' ? 'image/jpeg' : image.file.type
      const hasValidMimeType = validImageTypes.includes(normalizedMimeType)
      
      if (!hasValidMimeType) {
        console.warn(`⚠️ Skipping file with invalid MIME type: ${image.file.name} (${image.file.type})`)
        continue
      }
      
      // Double-check with magic bytes for security
      const hasValidMagicBytes = await isValidImageFile(image.file)
      
      if (!hasValidMagicBytes) {
        console.warn(`⚠️ Skipping file with invalid image format: ${image.file.name} (failed magic byte check)`)
        continue
      }
      
      validImages.push(image)
    }
    
    if (validImages.length === 0) {
      throw new Error('No valid image files found')
    }
    
    if (validImages.length !== images.length) {
      console.log(`📋 Filtered ${images.length} files down to ${validImages.length} valid images`)
    }
    
    const zip = new JSZip()
    
    // Add only valid images to ZIP
    for (let i = 0; i < validImages.length; i++) {
      const image = validImages[i]
      const fileExtension = image.file.name.split('.').pop()?.toLowerCase() || 'jpg'
      
      // Ensure extension matches common image formats
      const normalizedExtension = fileExtension === 'jpeg' ? 'jpg' : fileExtension
      const fileName = `${triggerWord}_${String(i + 1).padStart(3, '0')}.${normalizedExtension}`
      
      console.log(`📸 Adding image ${i + 1}/${validImages.length}: ${fileName} (${image.file.type})`)
      
      // Add file to ZIP
      zip.file(fileName, image.file)
    }
    
    // Generate ZIP blob
    console.log('🔄 Generating ZIP blob...')
    const zipBlob = await zip.generateAsync({
      type: 'blob',
      compression: 'DEFLATE',
      compressionOptions: {
        level: 6 // Good balance of size vs speed
      }
    })
    
    console.log(`✅ ZIP created successfully: ${(zipBlob.size / 1024 / 1024).toFixed(2)}MB`)
    return zipBlob
    
  } catch (error) {
    console.error('❌ Failed to create ZIP:', error)
    throw new Error(`Failed to create training ZIP: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Upload ZIP to Vercel Blob and get public URL
 */
export async function uploadZipToVercelBlob(zipBlob: Blob, personaName: string): Promise<string> {
  try {
    console.log('☁️ Uploading ZIP to Vercel Blob...')
    
    const sanitizedName = personaName.toLowerCase().replace(/[^a-z0-9]/g, '-')
    const timestamp = Date.now()
    const filename = `training-${sanitizedName}-${timestamp}.zip`
    
    // Create FormData for upload (ZIP as single file, not extracted)
    const formData = new FormData()
    formData.append('file', zipBlob, filename)
    
    console.log(`📤 Uploading ${filename} (${(zipBlob.size / 1024 / 1024).toFixed(2)}MB) as single ZIP file...`)
    console.log(`📋 ZIP file will be stored as single file, not extracted`)
    
    // Upload to our API endpoint that handles Vercel Blob
    const response = await fetch('/api/upload-training-zip', {
      method: 'POST',
      body: formData
    })
    
    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Upload failed: ${response.status} - ${errorText}`)
    }
    
    const result = await response.json()
    
    if (!result.success || !result.url) {
      throw new Error(`Upload failed: ${result.error || 'No URL returned'}`)
    }
    
    // Verify the returned URL is a direct ZIP file
    if (!result.url.includes('.zip')) {
      console.warn('⚠️ Warning: Returned URL does not appear to be a direct ZIP file:', result.url)
    }
    
    console.log(`✅ ZIP uploaded successfully as single file: ${result.url}`)
    console.log(`🔗 Direct ZIP URL (contains image files):`, result.url)
    return result.url
    
  } catch (error) {
    console.error('❌ Failed to upload ZIP:', error)
    throw new Error(`Failed to upload ZIP to Vercel Blob: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Complete client-side workflow: Images → ZIP → Vercel Blob → URL
 */
export async function uploadTrainingImages(
  images: TrainingImage[], 
  triggerWord: string, 
  personaName: string, 
  trainingType: 'persona' | 'style' = 'persona'
): Promise<{
  success: boolean
  zipUrl?: string
  zipSize?: number
  error?: string
}> {
  try {
    console.log(`🚀 Starting client-side ${trainingType} training images upload...`)
    
    // Validate inputs
    if (!images || images.length === 0) {
      throw new Error('No images provided')
    }
    
    // Different validation based on training type
    if (trainingType === 'persona') {
      // No hard validation for persona training, just need at least 1 image
      if (images.length < 1) {
        throw new Error('At least 1 image required for persona training')
      }
    } else {
      if (images.length < 3) {
        throw new Error('Minimum 3 images required for style training')
      }
      if (images.length > 6) {
        throw new Error('Maximum 6 images allowed for style training')
      }
    }
    
    if (!triggerWord || triggerWord.trim().length === 0) {
      throw new Error('Trigger word is required')
    }
    
    if (!personaName || personaName.trim().length === 0) {
      throw new Error(`${trainingType === 'persona' ? 'Persona' : 'Style'} name is required`)
    }
    
    console.log(`📊 Processing ${images.length} images for "${personaName}" with trigger word "${triggerWord}"`)
    
    // Step 1: Create ZIP from images
    const zipBlob = await createTrainingZipClient(images, triggerWord)
    
    // Step 2: Upload ZIP to Vercel Blob
    const zipUrl = await uploadZipToVercelBlob(zipBlob, personaName)
    
    console.log(`✅ Client-side ${trainingType} upload completed successfully!`)
    
    return {
      success: true,
      zipUrl,
      zipSize: zipBlob.size
    }
    
  } catch (error) {
    console.error(`❌ Client-side ${trainingType} upload failed:`, error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Helper function to convert File to base64 (if needed)
 */
export async function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = reject
    reader.readAsDataURL(file)
  })
}

/**
 * Validate if file is actually an image by checking magic bytes
 */
export async function isValidImageFile(file: File): Promise<boolean> {
  return new Promise((resolve) => {
    const reader = new FileReader()
    
    reader.onload = () => {
      const arr = new Uint8Array(reader.result as ArrayBuffer)
      
      // Check magic bytes for common image formats
      const isJPEG = arr.length >= 2 && arr[0] === 0xFF && arr[1] === 0xD8
      const isPNG = arr.length >= 8 && 
        arr[0] === 0x89 && arr[1] === 0x50 && arr[2] === 0x4E && arr[3] === 0x47 &&
        arr[4] === 0x0D && arr[5] === 0x0A && arr[6] === 0x1A && arr[7] === 0x0A
      const isWebP = arr.length >= 12 &&
        arr[0] === 0x52 && arr[1] === 0x49 && arr[2] === 0x46 && arr[3] === 0x46 &&
        arr[8] === 0x57 && arr[9] === 0x45 && arr[10] === 0x42 && arr[11] === 0x50
      
      resolve(isJPEG || isPNG || isWebP)
    }
    
    reader.onerror = () => resolve(false)
    
    // Read first 12 bytes to check magic bytes
    reader.readAsArrayBuffer(file.slice(0, 12))
  })
}

/**
 * Helper function to validate image files (sync validation)
 */
export function validateTrainingImages(files: File[], trainingType: 'persona' | 'style' = 'persona'): { valid: boolean; errors: string[]; imageFiles: File[] } {
  const errors: string[] = []
  const validTypes = ['image/jpeg', 'image/png', 'image/webp']
  const maxFileSize = 10 * 1024 * 1024 // 10MB per file
  const minFileSize = 1024 // 1KB
  
  // Filter to only image files (with MIME type normalization)
  const imageFiles = files.filter(file => {
    const normalizedMimeType = file.type === 'image/jpg' ? 'image/jpeg' : file.type
    return validTypes.includes(normalizedMimeType)
  })
  const nonImageFiles = files.filter(file => {
    const normalizedMimeType = file.type === 'image/jpg' ? 'image/jpeg' : file.type
    return !validTypes.includes(normalizedMimeType)
  })
  
  // Report non-image files
  if (nonImageFiles.length > 0) {
    errors.push(`${nonImageFiles.length} non-image files will be ignored: ${nonImageFiles.map(f => f.name).join(', ')}`)
  }
  
  // Different validation based on training type
  if (trainingType === 'persona') {
    // For persona training, just show recommendations, no hard validation
    if (imageFiles.length < 10) {
      errors.push(`📋 Recommendation: 10-20 images work best for persona training (you have ${imageFiles.length})`)
    }
    
    if (imageFiles.length > 20) {
      errors.push(`📋 Recommendation: 10-20 images work best for persona training (you have ${imageFiles.length})`)
    }
    
    if (imageFiles.length > 50) {
      errors.push(`⚠️ Too many images may slow down training (you have ${imageFiles.length})`)
    }
  } else {
    if (imageFiles.length < 3) {
      errors.push(`Minimum 3 images required for style training, you have ${imageFiles.length} valid images`)
    }
    
    if (imageFiles.length > 6) {
      errors.push(`Maximum 6 images allowed for style training, you have ${imageFiles.length} images`)
    }
  }
  
  imageFiles.forEach((file, index) => {
    if (file.size > maxFileSize) {
      errors.push(`Image ${index + 1}: File too large (${(file.size / 1024 / 1024).toFixed(1)}MB). Max 10MB per file.`)
    }
    
    if (file.size < minFileSize) {
      errors.push(`Image ${index + 1}: File too small (${file.size} bytes). Minimum 1KB.`)
    }
  })
  
  // For persona training, only fail validation for serious issues (file size, etc.)
  // For style training, enforce the 3-6 image requirement
  const hasBlockingErrors = errors.some(error => 
    error.includes('File too large') || 
    error.includes('File too small') ||
    (trainingType === 'style' && (error.includes('Minimum 3') || error.includes('Maximum 6')))
  )

  return {
    valid: !hasBlockingErrors,
    errors,
    imageFiles
  }
} 