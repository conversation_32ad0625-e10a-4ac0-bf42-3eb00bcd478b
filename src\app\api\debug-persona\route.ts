import { NextRequest, NextResponse } from 'next/server';
import { supabase, isSupabaseConfigured } from '../../../lib/supabase';
import { loadPersonas, savePersonas } from '../../../utils/personaStorage';

export async function GET() {
  try {
    const diagnostics = {
      timestamp: new Date().toISOString(),
      environment: {
        supabaseConfigured: isSupabaseConfigured(),
        hasSupabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
        hasSupabaseKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
        supabaseUrlPrefix: process.env.NEXT_PUBLIC_SUPABASE_URL?.substring(0, 20) + '...' || 'MISSING'
      },
      databaseTest: null as any,
      personaLoadTest: null as any
    };

    // Test database connection
    if (supabase) {
      try {
        console.log('🔍 Testing Supabase connection...');
        const { data, error } = await supabase
          .from('personas')
          .select('count', { count: 'exact', head: true });
        
        if (error) {
          diagnostics.databaseTest = {
            success: false,
            error: error.message,
            details: error
          };
        } else {
          diagnostics.databaseTest = {
            success: true,
            count: data || 0
          };
        }
      } catch (dbError) {
        diagnostics.databaseTest = {
          success: false,
          error: dbError instanceof Error ? dbError.message : 'Unknown database error'
        };
      }
    } else {
      diagnostics.databaseTest = {
        success: false,
        error: 'Supabase client not initialized'
      };
    }

    // Test persona loading
    try {
      console.log('🔍 Testing persona loading...');
      const personas = await loadPersonas();
      diagnostics.personaLoadTest = {
        success: true,
        count: personas.length,
        personaIds: personas.map(p => p.id).slice(0, 5) // First 5 IDs only
      };
    } catch (loadError) {
      diagnostics.personaLoadTest = {
        success: false,
        error: loadError instanceof Error ? loadError.message : 'Unknown load error'
      };
    }

    return NextResponse.json({
      success: true,
      diagnostics
    });
    
  } catch (error) {
    return NextResponse.json(
      { 
        error: 'Debug failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();
    
    const results = {
      timestamp: new Date().toISOString(),
      action,
      result: null as any
    };

    switch (action) {
      case 'test-save':
        try {
          console.log('🧪 Testing persona save operation...');
          
          // Create a minimal test persona
          const testPersona = {
            id: `test_${Date.now()}`,
            name: 'Debug Test Persona',
            description: 'Test persona for debugging',
            imageUrl: 'data:image/png;base64,test',
            imageBase64: 'data:image/png;base64,test',
            createdAt: new Date(),
            updatedAt: new Date(),
            usageCount: 0,
            category: 'professional' as const,
            isDefault: false,
            lastUsed: new Date()
          };
          
          // Load current personas
          const personas = await loadPersonas();
          console.log(`🔍 Loaded ${personas.length} existing personas`);
          
          // Add test persona
          personas.push(testPersona);
          
          // Try to save
          await savePersonas(personas);
          
          results.result = {
            success: true,
            message: 'Test persona saved successfully',
            totalPersonas: personas.length,
            testPersonaId: testPersona.id
          };
          
        } catch (saveError) {
          results.result = {
            success: false,
            error: saveError instanceof Error ? saveError.message : 'Unknown save error',
            errorType: saveError?.constructor?.name
          };
        }
        break;
        
      case 'clear-cache':
        try {
          console.log('🧹 Clearing persona cache...');
          // This would clear the in-memory cache if we exposed it
          results.result = {
            success: true,
            message: 'Cache cleared (note: only affects in-memory storage)'
          };
        } catch (clearError) {
          results.result = {
            success: false,
            error: clearError instanceof Error ? clearError.message : 'Unknown clear error'
          };
        }
        break;
        
      default:
        results.result = {
          success: false,
          error: `Unknown action: ${action}`
        };
    }

    return NextResponse.json({
      success: true,
      results
    });
    
  } catch (error) {
    return NextResponse.json(
      { 
        error: 'Debug POST failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
} 