'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import { X, <PERSON><PERSON><PERSON>, Edit3 } from 'lucide-react'
import Image from 'next/image'

interface EditAreasModalProps {
  isOpen: boolean
  onClose: () => void
  thumbnailUrl: string
  onSave: (editedThumbnail: string) => void
}

interface SelectionArea {
  id: string
  x: number
  y: number
  width: number
  height: number
}

export function EditAreasModal({ isOpen, onClose, thumbnailUrl, onSave }: EditAreasModalProps) {
  const [selectedAreas, setSelectedAreas] = useState<SelectionArea[]>([])
  const [editPrompt, setEditPrompt] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [isDrawing, setIsDrawing] = useState(false)
  const [startPos, setStartPos] = useState({ x: 0, y: 0 })

  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    setIsDrawing(true)
    setStartPos({ x, y })
  }

  const handleMouseUp = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isDrawing) return

    const rect = e.currentTarget.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    const width = Math.abs(x - startPos.x)
    const height = Math.abs(y - startPos.y)

    if (width > 30 && height > 30) {
      const newArea: SelectionArea = {
        id: Date.now().toString(),
        x: Math.min(startPos.x, x),
        y: Math.min(startPos.y, y),
        width,
        height,
      }
      
      setSelectedAreas([newArea]) // Only allow one selection at a time
    }

    setIsDrawing(false)
  }

  const handleApplyEdit = async () => {
    if (!editPrompt.trim() || selectedAreas.length === 0) return
    
    setIsProcessing(true)
    setTimeout(() => {
      setIsProcessing(false)
      onSave(thumbnailUrl)
      onClose()
    }, 2000)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm">
      <div className="relative mx-4 w-full max-w-4xl rounded-lg bg-bg-secondary border border-border-primary">
        {/* Header */}
        <div className="flex items-center justify-between border-b border-border-primary p-4">
          <div className="flex items-center space-x-3">
            <Edit3 className="h-5 w-5 text-white" />
            <h2 className="text-lg font-semibold text-text-primary">Edit Area</h2>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Image */}
            <div className="lg:col-span-2">
              <div className="mb-4">
                <p className="text-sm text-text-secondary">
                  Click and drag to select the area you want to edit
                </p>
              </div>
              
              <div 
                className="relative overflow-hidden rounded-lg border border-border-primary cursor-crosshair bg-bg-tertiary"
                onMouseDown={handleMouseDown}
                onMouseUp={handleMouseUp}
              >
                <Image
                  src={thumbnailUrl}
                  alt="Thumbnail to edit"
                  width={600}
                  height={338}
                  className="w-full select-none"
                  draggable={false}
                />
                
                {/* Selection Overlay */}
                {selectedAreas.map((area) => (
                  <div
                    key={area.id}
                    className="absolute border-2 border-accent-primary bg-accent-primary/20"
                    style={{
                      left: area.x,
                      top: area.y,
                      width: area.width,
                      height: area.height,
                    }}
                  />
                ))}
              </div>
            </div>

            {/* Controls */}
            <div className="space-y-4">
              <div>
                <label className="mb-2 block text-sm font-medium text-text-primary">
                  What would you like to change?
                </label>
                <textarea
                  value={editPrompt}
                  onChange={(e) => setEditPrompt(e.target.value)}
                  placeholder="e.g., Remove the background, Add fire effects, Change to blue..."
                  className="h-32 w-full resize-none rounded-lg border border-border-primary bg-bg-primary p-3 text-sm text-text-primary placeholder-text-tertiary focus:border-accent-primary focus:outline-none"
                />
              </div>

              {/* Quick suggestions */}
              <div className="space-y-2">
                <p className="text-xs text-text-secondary">Quick ideas:</p>
                <div className="flex flex-wrap gap-2">
                  {['Remove background', 'Add glow effect', 'Change color', 'Add text'].map((suggestion) => (
                    <button
                      key={suggestion}
                      onClick={() => setEditPrompt(suggestion)}
                      className="rounded-full bg-bg-tertiary px-3 py-1 text-xs text-text-secondary hover:bg-accent-primary/20 hover:text-accent-primary transition-colors"
                    >
                      {suggestion}
                    </button>
                  ))}
                </div>
              </div>

              {/* Apply button */}
              <Button
                onClick={handleApplyEdit}
                disabled={!editPrompt.trim() || selectedAreas.length === 0 || isProcessing}
                className="w-full"
                size="lg"
              >
                {isProcessing ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white/20 border-t-white" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Sparkles className="mr-2 h-4 w-4" />
                    Apply Edit (1 credit)
                  </>
                )}
              </Button>

              {selectedAreas.length === 0 && (
                <p className="text-xs text-text-tertiary text-center">
                  Select an area on the image first
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 