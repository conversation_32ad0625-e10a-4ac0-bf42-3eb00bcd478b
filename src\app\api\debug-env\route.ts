import { NextRequest, NextResponse } from 'next/server';

export async function GET() {
  try {
    // Check critical environment variables
    const checks = {
      REPLICATE_API_TOKEN: !!process.env.REPLICATE_API_TOKEN,
      REPLICATE_USERNAME: !!process.env.REPLICATE_USERNAME,
      REPLICATE_WEBHOOK_SECRET: !!process.env.REPLICATE_WEBHOOK_SECRET,
      BLOB_READ_WRITE_TOKEN: !!process.env.BLOB_READ_WRITE_TOKEN,
      NODE_ENV: process.env.NODE_ENV,
      VERCEL: !!process.env.VERCEL,
      VERCEL_ENV: process.env.VERCEL_ENV
    }
    
    // Additional info
    const info = {
      timestamp: new Date().toISOString(),
      hasReplicateToken: checks.REPLICATE_API_TOKEN,
      hasReplicateUsername: checks.REPLICATE_USERNAME,
      hasWebhookSecret: checks.REPLICATE_WEBHOOK_SECRET,
      hasBlobToken: checks.BLOB_READ_WRITE_TOKEN,
      environment: checks.NODE_ENV,
      isVercel: checks.VERCEL,
      vercelEnv: checks.VERCEL_ENV
    }
    
    // Check if all required tokens are present
    const allTokensPresent = checks.REPLICATE_API_TOKEN && checks.REPLICATE_USERNAME && checks.BLOB_READ_WRITE_TOKEN && checks.REPLICATE_WEBHOOK_SECRET
    
    return NextResponse.json({
      success: allTokensPresent,
      message: allTokensPresent ? 'All environment variables configured' : 'Missing required environment variables',
      checks,
      info,
      missing: Object.entries(checks)
        .filter(([key, value]) => (key.includes('TOKEN') || key.includes('USERNAME') || key.includes('WEBHOOK_SECRET')) && !value)
        .map(([key]) => key)
    })
    
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Failed to check environment',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
} 