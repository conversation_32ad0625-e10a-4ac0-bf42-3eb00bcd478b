# 🚀 Database Setup Instructions

## Quick Setup (3 steps)

### Step 1: Create Supabase Project
1. Go to [supabase.com](https://supabase.com)
2. Sign up/login and create a new project
3. Wait for project to initialize (2-3 minutes)

### Step 2: Get Your Credentials
1. In your Supabase dashboard, go to **Settings** → **API**
2. Copy these values:
   - **Project URL**: `https://xxxxx.supabase.co`
   - **Anon Public Key**: `eyJhbG...` (long string)

### Step 3: Create Environment File
Create `.env.local` file in your project root:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your.anon.key.here

# Replicate Configuration (Required)
# Get your token from: https://replicate.com/account/api-tokens
REPLICATE_API_TOKEN=r8_********************************
REPLICATE_USERNAME=your-replicate-username
REPLICATE_WEBHOOK_SECRET=your-webhook-secret-here

# Webhook Configuration
WEBHOOK_BASE_URL=https://your-app.vercel.app

# Optional: fal.ai API Key (for additional models)
FAL_API_KEY=your-fal-api-key-here
```

**Important Replicate Setup Notes:**
- Your `REPLICATE_API_TOKEN` should start with `r8_` followed by 32 characters
- Get your token from [Replicate API Tokens page](https://replicate.com/account/api-tokens)
- Set `REPLICATE_USERNAME` to your Replicate username for LoRA training
- Generate a secure webhook secret for `REPLICATE_WEBHOOK_SECRET`

## Then choose one method:

### Method A: Automatic Setup (Recommended)
```bash
node setup-database.js
```

### Method B: Manual Setup
1. Go to your Supabase dashboard
2. Click **SQL Editor**
3. Copy and paste the entire content from `database-schema.sql`
4. Click **Run** to execute
5. Test with: `node test-database.js`

## ✅ Verification
After setup, you should see:
- ✅ Supabase connection successful!
- ✅ Personas table exists and is accessible
- 🎉 Database is ready to use!

## 🎯 What This Fixes
- **Before**: LoRA training data lost between API calls (in-memory storage)
- **After**: LoRA training data persisted in database ✅
- **Result**: LoRA training will now work correctly! 🚀 