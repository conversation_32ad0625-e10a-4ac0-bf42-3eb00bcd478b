import { NextRequest, NextResponse } from 'next/server';
import { Style } from '../../../types/style';
import { loadStyles, saveStyles } from '../../../utils/styleStorage';
import { supabase } from '../../../lib/supabase'

// Simplified image validation - just check basic file properties
function validateImageFile(file: File): { valid: boolean; error?: string } {
  // Check file size (max 10MB)
  if (file.size > 10 * 1024 * 1024) {
    return {
      valid: false,
      error: 'File size too large. Maximum 10MB allowed.'
    }
  }

  // Check minimum size (1KB)
  if (file.size < 1024) {
    return {
      valid: false,
      error: 'File too small. Minimum 1KB required.'
    }
  }

  // Check MIME type (normalize image/jpg to image/jpeg)
  const normalizedMimeType = file.type === 'image/jpg' ? 'image/jpeg' : file.type
  const validMimeTypes = ['image/jpeg', 'image/png', 'image/webp']
  
  if (validMimeTypes.indexOf(normalizedMimeType) === -1) {
    return {
      valid: false,
      error: `Invalid file type: ${file.type}. Only JPEG, PNG, and WebP are supported.`
    }
  }

  // Check file extension
  const extension = file.name.split('.').pop()?.toLowerCase()
  const validExtensions = ['jpg', 'jpeg', 'png', 'webp']
  
  if (!extension || validExtensions.indexOf(extension) === -1) {
    return {
      valid: false,
      error: `Invalid file extension: .${extension}. Only .jpg, .jpeg, .png, and .webp are supported.`
    }
  }

  return { valid: true }
}

// Convert file to base64 for preview (server-side)
async function fileToBase64(file: File): Promise<string> {
  try {
    const bytes = await file.arrayBuffer()
    const buffer = new Uint8Array(bytes)
    
    // Convert to base64 using built-in btoa
    let binary = ''
    for (let i = 0; i < buffer.length; i++) {
      binary += String.fromCharCode(buffer[i])
    }
    const base64 = btoa(binary)
    
    return `data:${file.type};base64,${base64}`
  } catch (error) {
    console.error('Error converting file to base64:', error)
    throw new Error('Failed to process image file')
  }
}

// Note: Upload functionality removed - Replicate handles all image processing directly

// GET - Fetch all styles from simplified database
export async function GET() {
  try {
    console.log('🎨 GET /api/styles - Loading styles from database...')
    
    if (!supabase) {
      console.error('❌ Supabase not configured')
      return NextResponse.json({
        success: false,
        error: 'Database not configured'
      }, { status: 500 })
    }

    // Load all styles from Supabase
    const { data: styles, error } = await supabase
      .from('styles')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      console.error('❌ Error loading styles:', error)
      return NextResponse.json({
        success: false,
        error: 'Failed to load styles from database',
        details: error.message
      }, { status: 500 })
    }

    console.log(`✅ Loaded ${styles?.length || 0} styles from database`)

    // Transform the data to match StyleSelector expectations
    const transformedStyles = (styles || []).map(style => ({
      id: style.id,
      name: style.name,
      category: style.category || 'other',
      description: style.description || '',
      imageUrl: style.image_url || '/placeholder-thumbnail.svg',
      image_base64: style.image_base64 || '',
      tags: style.tags || [],
      createdAt: style.created_at,
      updatedAt: style.updated_at,
      usageCount: style.usage_count || 0,
      isDefault: style.is_default || false,
      // Include model_url and trigger_word at top level for StyleSelector
      model_url: style.model_url,
      trigger_word: style.trigger_word,
      // Include LoRA training data
      loraTraining: style.lora_training || null
    }))

    return NextResponse.json({
      success: true,
      styles: transformedStyles,
      count: transformedStyles.length
    })

  } catch (error) {
    console.error('❌ Error in styles API:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// POST - Create new style with multiple training images
export async function POST(request: NextRequest) {
  try {
    console.log('🎨 POST /api/styles called - Creating style with multiple training images...')
    
    const formData = await request.formData()
    const name = formData.get('name') as string
    const description = formData.get('description') as string
    const category = formData.get('category') as string
    const triggerWord = formData.get('triggerWord') as string
    const imageCount = parseInt(formData.get('imageCount') as string || '0')
    const tags = formData.get('tags') as string

    console.log(`📋 Processing ${imageCount} images for style: ${name}`)

    // Validate required fields
    if (!name || !triggerWord || imageCount < 3 || imageCount > 6) {
      return NextResponse.json(
        { error: 'Name, trigger word, and 3-6 images are required' },
        { status: 400 }
      )
    }

    // Collect all image files
    const imageFiles: File[] = []
    for (let i = 0; i < imageCount; i++) {
      const imageFile = formData.get(`imageFile_${i}`) as File
      if (imageFile && imageFile.type.startsWith('image/')) {
        imageFiles.push(imageFile)
      }
    }

    if (imageFiles.length < 3 || imageFiles.length > 6) {
      return NextResponse.json(
        { error: `Invalid image count. Got ${imageFiles.length}, need 3-6 images.` },
        { status: 400 }
      )
    }

    console.log(`✅ Collected ${imageFiles.length} valid image files`)

    // Validate images (but don't process them - Replicate will handle that)
    const validImages: File[] = []
    const invalidImages: { file: File; reason: string }[] = []

    for (let i = 0; i < imageFiles.length; i++) {
      const imageFile = imageFiles[i]
      console.log(`🔄 Validating style image ${i + 1}/${imageFiles.length}: ${imageFile.name}`)

      // Simple validation only
      const validation = validateImageFile(imageFile)
      if (!validation.valid) {
        console.warn(`⚠️ Skipping invalid style image ${i + 1}: ${validation.error}`)
        invalidImages.push({ file: imageFile, reason: validation.error! })
        continue
      }

      validImages.push(imageFile)
      console.log(`✅ Style image ${i + 1} is valid`)
    }

    if (validImages.length < 3 || validImages.length > 6) {
      return NextResponse.json(
        { 
          error: `Invalid image count. Got ${validImages.length} valid images, need 3-6.`,
          invalidImages: invalidImages.map(img => ({ name: img.file.name, reason: img.reason }))
        },
        { status: 400 }
      )
    }

    // Get first image for preview (no processing)
    const firstImageBase64 = await fileToBase64(validImages[0])

    // Generate unique style ID
    const styleId = `style-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

    // For now, just return success (actual training implementation would be added later)
    console.log(`✅ Successfully created style: ${name} with ${validImages.length} images`)

    return NextResponse.json({
      success: true,
      message: `Style "${name}" created successfully with ${validImages.length} images`,
      styleId: styleId,
      note: 'No image processing performed - Replicate will handle all image processing during training.',
      trainingInfo: {
        imageCount: validImages.length,
        triggerWord: triggerWord.toUpperCase(),
        estimatedTrainingCost: '$2-4 USD',
        estimatedTrainingTime: '15-20 minutes',
        processingNote: 'Replicate automatically handles image resizing, cropping, and optimization'
      }
    })

  } catch (error) {
    console.error('❌ Error creating style:', error)
    return NextResponse.json(
      { 
        error: 'Failed to create style',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}