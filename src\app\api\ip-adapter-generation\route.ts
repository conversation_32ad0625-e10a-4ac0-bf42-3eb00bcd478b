import { NextRequest, NextResponse } from 'next/server';
import Replicate from 'replicate';
import { storeGeneratedImage } from '@/utils/imageStorage';

// Initialize Replicate client with proper error handling
const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN!,
});

// fal.ai client setup
const FAL_API_KEY = process.env.FAL_API_KEY;

interface RequestBody {
  prompt: string;
  model: string;
  faceStrength?: number;
  styleStrength?: number;
  expressionOverride?: string;
  faceImageData?: string;
  styleImageData?: string;
  persona?: any; // Include persona for LoRA training data
  styleTemplate?: any; // Include style template for LoRA training data
  speedTier?: 'fast' | 'balanced' | 'quality'; // Add speed tier option
  aspectRatio?: '16:9' | '4:3' | '1:1' | '9:16'; // Add aspect ratio option
}

// Convert aspect ratio to width/height for FLUX models
function getAspectRatioDimensions(aspectRatio: string): { width: number; height: number } {
  switch (aspectRatio) {
    case '16:9':
      return { width: 1280, height: 720 }; // YouTube standard
    case '4:3':
      return { width: 1024, height: 768 }; // Classic format
    case '1:1':
      return { width: 1024, height: 1024 }; // Square format
    case '9:16':
      return { width: 720, height: 1280 }; // Vertical/mobile format
    default:
      return { width: 1280, height: 720 }; // Default to 16:9
  }
}

// Enhanced prompt for thumbnail generation with LoRA
function enhancePromptForLoRA(userPrompt: string, personaTriggerWord?: string, styleTriggerWord?: string): string {
  const qualityPrefix = "high quality, professional, sharp focus, detailed";
  
  const thumbnailEnhancements = [
    "YouTube thumbnail style",
    "cinematic lighting",
    "vibrant colors",
    "eye-catching composition",
    "professional photography",
    "4K resolution",
    "engaging expression",
    "dynamic pose",
    "dramatic lighting",
    "bold composition"
  ];

  // Add trigger words if available
  let enhancedPrompt = userPrompt;
  const triggerWords = [];
  
  if (personaTriggerWord) {
    triggerWords.push(personaTriggerWord);
  }
  if (styleTriggerWord) {
    triggerWords.push(styleTriggerWord);
  }
  
  if (triggerWords.length > 0) {
    enhancedPrompt = `${triggerWords.join(', ')}, ${userPrompt}`;
  }

  const allEnhancements = [...thumbnailEnhancements];
  return `${qualityPrefix}, ${enhancedPrompt}, ${allEnhancements.join(", ")}`;
}

export async function POST(request: NextRequest) {
  let finalThumbnailUrl = ''; // Declare at function scope for error handling
  console.log('🎯 === FLUX LORA GENERATION REQUEST RECEIVED ===');
  
  try {
    const body: RequestBody = await request.json();
    console.log('📋 Request data:', {
      prompt: body.prompt,
      model: body.model,
      speedTier: body.speedTier || 'balanced',
      hasFaceImage: !!body.faceImageData,
      hasPersona: !!body.persona,
      personaHasLoRA: !!body.persona?.loraTraining?.modelUrl,
      hasStyleTemplate: !!body.styleTemplate,
      styleHasLoRA: !!body.styleTemplate?.loraTraining?.modelUrl
    });

    // 🔍 ENHANCED PERSONA DEBUG - Server Side
    if (body.persona) {
      console.log('🎯 SERVER RECEIVED PERSONA:', {
        personaId: body.persona.id,
        personaName: body.persona.name,
        hasLoraTraining: !!body.persona.loraTraining,
        loraStatus: body.persona.loraTraining?.status,
        loraModelUrl: body.persona.loraTraining?.modelUrl,
        loraTriggerWord: body.persona.loraTraining?.triggerWord,
        fullLoraData: body.persona.loraTraining
      });
    } else {
      console.log('❌ NO PERSONA IN SERVER REQUEST');
    }

    // 🔍 ENHANCED STYLE DEBUG - Server Side
    if (body.styleTemplate) {
      console.log('🎨 SERVER RECEIVED STYLE TEMPLATE:', {
        styleId: body.styleTemplate.id,
        styleName: body.styleTemplate.name,
        hasLoraTraining: !!body.styleTemplate.loraTraining,
        loraStatus: body.styleTemplate.loraTraining?.status,
        loraModelUrl: body.styleTemplate.loraTraining?.modelUrl,
        loraTriggerWord: body.styleTemplate.loraTraining?.triggerWord,
        fullLoraData: body.styleTemplate.loraTraining
      });
    } else {
      console.log('❌ NO STYLE TEMPLATE IN SERVER REQUEST');
    }

    const { 
      prompt, 
      model, 
      faceStrength = 0.8, 
      styleStrength = 0.6,
      expressionOverride = 'auto',
      faceImageData,
      styleImageData,
      persona,
      styleTemplate,
      speedTier = 'balanced', // Default to balanced
      aspectRatio = '16:9' // Default to 16:9 for YouTube thumbnails
    } = body;

    // Check if we have LoRA models available (persona and/or style)
    const hasPersonaLoRA = !!(persona?.loraTraining?.status === 'completed' && persona?.loraTraining?.modelUrl);
    const hasStyleLoRA = !!(styleTemplate?.loraTraining?.status === 'completed' && styleTemplate?.loraTraining?.modelUrl);
    const hasAnyLoRA = hasPersonaLoRA || hasStyleLoRA;

    console.log('🎯 Generation path determination:', {
      hasPersonaLoRA: hasPersonaLoRA,
      hasStyleLoRA: hasStyleLoRA,
      hasAnyLoRA: hasAnyLoRA,
      personaExists: !!persona,
      personaStatus: persona?.loraTraining?.status,
      personaModelUrl: persona?.loraTraining?.modelUrl,
      personaTriggerWord: persona?.loraTraining?.triggerWord,
      styleExists: !!styleTemplate,
      styleStatus: styleTemplate?.loraTraining?.status,
      styleModelUrl: styleTemplate?.loraTraining?.modelUrl,
      styleTriggerWord: styleTemplate?.loraTraining?.triggerWord
    });

    if (!prompt) {
      return NextResponse.json(
        { error: 'Prompt is required' },
        { status: 400 }
      );
    }

    if (!process.env.REPLICATE_API_TOKEN) {
      return NextResponse.json(
        { error: 'Replicate API token not configured' },
        { status: 500 }
      );
    }

    // Check if we have any trained LoRA models (persona and/or style)
    if (hasAnyLoRA) {
      console.log('🎭🎨 ✅ LORA PATH SELECTED - Using trained LoRA model(s) for generation');
      
      // Determine which LoRA model to use as primary
      // Priority: If both exist, use persona as primary model and style as extra_lora
      let primaryModelUrl: string = '';
      let personaTriggerWord: string | undefined;
      let styleTriggerWord: string | undefined;
      
      if (hasPersonaLoRA) {
        primaryModelUrl = persona.loraTraining.modelUrl;
        personaTriggerWord = persona.loraTraining.triggerWord;
        console.log('🎭 Using persona LoRA as primary model');
      } else if (hasStyleLoRA) {
        primaryModelUrl = styleTemplate.loraTraining.modelUrl;
        styleTriggerWord = styleTemplate.loraTraining.triggerWord;
        console.log('🎨 Using style LoRA as primary model');
      }
      
      // If both exist, use style as extra_lora
      if (hasPersonaLoRA && hasStyleLoRA) {
        styleTriggerWord = styleTemplate.loraTraining.triggerWord;
        console.log('🎭🎨 Using both persona (primary) and style (extra_lora) LoRA models');
      }
      
      const enhancedPrompt = enhancePromptForLoRA(prompt, personaTriggerWord, styleTriggerWord);
      
      // Safety check for primary model URL
      if (!primaryModelUrl) {
        throw new Error('No valid LoRA model URL found');
      }
      
      console.log('🔧 LoRA generation config:', {
        personaName: persona?.name,
        personaId: persona?.id,
        styleName: styleTemplate?.name,
        styleId: styleTemplate?.id,
        personaTriggerWord: personaTriggerWord,
        styleTriggerWord: styleTriggerWord,
        primaryModel: primaryModelUrl,
        enhancedPrompt: enhancedPrompt,
        originalPrompt: prompt,
        speedTier: speedTier,
        hasBothLoRAs: hasPersonaLoRA && hasStyleLoRA,
        note: 'LoRA models only work with FLUX-dev base model'
      });

      try {
        // Use trained LoRA model - MUST use FLUX-dev compatible parameters
        console.log('🔧 Using trained LoRA model directly');
        console.log('🎯 Primary model: ' + primaryModelUrl);
        console.log('📝 Enhanced prompt: ' + enhancedPrompt);
        console.log('⚠️ Note: LoRA models are trained on FLUX-dev and only work with FLUX-dev parameters');
        
        // Use LoRA-specific configuration (always FLUX-dev compatible)
        const loraConfig = getSpeedConfig('balanced', true); // Always use balanced config for LoRA
        
        const loraOutput = await replicate.run(
          primaryModelUrl as `${string}/${string}` | `${string}/${string}:${string}`,
          {
            input: {
              prompt: enhancedPrompt,
              model: "dev", // Always use dev model for LoRA
              go_fast: false, // LoRA models don't support go_fast
              lora_scale: 1,
              megapixels: loraConfig.megapixels,
              num_outputs: 1,
              aspect_ratio: aspectRatio, // User-selected aspect ratio
              output_format: "png",
              guidance_scale: loraConfig.guidance_scale,
              output_quality: loraConfig.output_quality,
              prompt_strength: 0.8,
              extra_lora_scale: 1,
              num_inference_steps: loraConfig.num_inference_steps,
              disable_safety_checker: true // Disable content filtering
            }
          }
        );

        // Handle LoRA output - FIXED to match Replicate documentation
        console.log('🔍 LoRA output received:', {
          type: typeof loraOutput,
          isArray: Array.isArray(loraOutput),
          length: Array.isArray(loraOutput) ? loraOutput.length : 'N/A'
        });

        // 🔧 FIXED: Based on Replicate docs, output is typically an array of URL strings
        console.log('🔍 Raw LoRA output type:', typeof loraOutput);
        console.log('🔍 Is array:', Array.isArray(loraOutput));
        if (Array.isArray(loraOutput)) {
          console.log('🔍 Array length:', loraOutput.length);
          if (loraOutput.length > 0) {
            console.log('🔍 First item type:', typeof loraOutput[0]);
          }
        }

        // Handle standard Replicate output format
        if (Array.isArray(loraOutput) && loraOutput.length > 0) {
          const firstItem = loraOutput[0];
          
          // Most common case: direct URL string in array
          if (typeof firstItem === 'string') {
            finalThumbnailUrl = firstItem;
            console.log('✅ Got URL directly from array (standard Replicate format) - truncated for log');
          }
          // Handle ReadableStream (some models return this)
          else if (firstItem && typeof firstItem === 'object' && 'locked' in firstItem) {
            console.log('🔄 Detected ReadableStream, converting to base64...');
            
            try {
              const reader = firstItem.getReader();
              const chunks = [];
              
              while (true) {
                const { done, value } = await reader.read();
                if (done) break;
                chunks.push(value);
              }
              
              const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
              const combined = new Uint8Array(totalLength);
              let offset = 0;
              
              for (const chunk of chunks) {
                combined.set(chunk, offset);
                offset += chunk.length;
              }
              
              const base64 = Buffer.from(combined).toString('base64');
              finalThumbnailUrl = `data:image/png;base64,${base64}`;
              
              console.log('✅ Converted ReadableStream to data URL');
            } catch (streamError) {
              console.error('❌ Failed to process ReadableStream:', streamError);
              throw new Error('Failed to process image stream from LoRA model');
            }
          }
          // Handle object with URL property (less common)
          else if (firstItem && typeof firstItem === 'object' && 'url' in firstItem) {
            finalThumbnailUrl = typeof firstItem.url === 'function' ? firstItem.url() : firstItem.url;
            console.log('✅ Got URL from object property:', finalThumbnailUrl);
          }
          // Unexpected format
          else {
            console.log('❌ Unexpected first item format:', {
              type: typeof firstItem,
              keys: firstItem && typeof firstItem === 'object' ? Object.keys(firstItem) : 'N/A',
              sample: typeof firstItem === 'object' ? 'Object (truncated)' : firstItem
            });
            // Try to convert to string as last resort
            finalThumbnailUrl = String(firstItem);
          }
        } 
        // Handle direct string output (less common)
        else if (typeof loraOutput === 'string') {
          finalThumbnailUrl = loraOutput;
          console.log('✅ Got URL directly from string output (truncated)');
        } 
        // Error case
        else {
          console.log('❌ LoRA output is not a valid format');
          console.log('🔍 Raw output type:', typeof loraOutput);
          console.log('🔍 Raw output sample (truncated)');
          throw new Error('Invalid LoRA output format - expected array of URLs or direct URL string');
        }

        if (!finalThumbnailUrl || finalThumbnailUrl.trim() === '') {
          console.error('❌ Failed to extract image URL from LoRA output');
          console.error('🔍 Final URL value:', finalThumbnailUrl);
          console.error('🔍 Final URL type:', typeof finalThumbnailUrl);
          console.error('🔍 Final URL length:', finalThumbnailUrl ? finalThumbnailUrl.length : 'N/A');
          console.error('🔍 LoRA output for debugging:', JSON.stringify(loraOutput, null, 2));
          throw new Error('Failed to extract image URL from LoRA output');
        }
        
        console.log('✅ LoRA URL extraction successful:', finalThumbnailUrl);

        console.log('✅ LoRA generation successful');

      } catch (loraError) {
        console.error('❌ LoRA generation failed, falling back to standard FLUX:', loraError);
        console.error('🔍 LoRA error details:', {
          errorMessage: loraError instanceof Error ? loraError.message : 'Unknown error',
          errorStack: loraError instanceof Error ? loraError.stack : 'No stack trace',
          finalThumbnailUrl: finalThumbnailUrl,
          hasPersonaLoRA: hasPersonaLoRA,
          hasStyleLoRA: hasStyleLoRA,
          hasAnyLoRA: hasAnyLoRA,
          personaStatus: persona?.loraTraining?.status,
          modelUrl: persona?.loraTraining?.modelUrl
        });
        
        // Fallback to standard FLUX with speed optimization
        const enhancedPrompt = enhancePromptForLoRA(prompt);
        const speedConfig = getSpeedConfig(speedTier, false); // No LoRA for fallback
        const fluxModel = getFluxModel(speedTier, false); // No LoRA for fallback
        
        console.log('🔧 FLUX fallback generation config:', {
          model: fluxModel,
          speedTier,
          enhancedPrompt: 'Enhanced prompt (truncated)'
        });

        try {
          console.log('🚀 Starting FLUX generation...');
          
          const dimensions = getAspectRatioDimensions(aspectRatio);
          
          const fluxOutput = await replicate.run(
            fluxModel,
            {
              input: {
                prompt: enhancedPrompt,
                width: dimensions.width,
                height: dimensions.height,
                num_inference_steps: speedConfig.num_inference_steps,
                guidance_scale: speedConfig.guidance_scale,
                output_format: "png",
                output_quality: speedConfig.output_quality,
                go_fast: speedTier === 'fast', // Enable optimizations for fast tier
                disable_safety_checker: true // Disable content filtering
              }
            }
          );

          console.log('🔄 FLUX generation completed, processing output...');

          console.log('🔍 FLUX fallback output received:', {
            type: typeof fluxOutput,
            isArray: Array.isArray(fluxOutput),
            length: Array.isArray(fluxOutput) ? fluxOutput.length : 'N/A',
            firstItemType: Array.isArray(fluxOutput) && fluxOutput.length > 0 ? typeof fluxOutput[0] : 'N/A'
          });

          // Handle ReadableStream response from Replicate (fallback)
          if (Array.isArray(fluxOutput) && fluxOutput.length > 0) {
            const firstItem = fluxOutput[0];
            
            // Check if it's a ReadableStream
            if (firstItem && typeof firstItem === 'object' && 'locked' in firstItem) {
              console.log('🔄 Detected ReadableStream in fallback, converting to URL...');
              
              try {
                // Convert ReadableStream to URL
                const reader = firstItem.getReader();
                const chunks = [];
                
                while (true) {
                  const { done, value } = await reader.read();
                  if (done) break;
                  chunks.push(value);
                }
                
                // Combine chunks into a single Uint8Array
                const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
                const combined = new Uint8Array(totalLength);
                let offset = 0;
                
                for (const chunk of chunks) {
                  combined.set(chunk, offset);
                  offset += chunk.length;
                }
                
                // Convert to base64 data URL
                const base64 = Buffer.from(combined).toString('base64');
                finalThumbnailUrl = `data:image/png;base64,${base64}`;
                
                console.log('✅ Converted fallback ReadableStream to data URL');
              } catch (streamError) {
                console.error('❌ Failed to process fallback ReadableStream:', streamError);
                throw new Error('Failed to process fallback image stream from FLUX');
              }
            } else if (typeof firstItem === 'string') {
              finalThumbnailUrl = firstItem;
              console.log('✅ Using fallback direct URL from array:', finalThumbnailUrl);
            } else {
              console.error('❌ Unexpected fallback first item type:', typeof firstItem);
              throw new Error(`Invalid first item in FLUX fallback output: ${typeof firstItem}`);
            }
          } else if (typeof fluxOutput === 'string') {
            finalThumbnailUrl = fluxOutput;
            console.log('✅ Using fallback direct string output:', finalThumbnailUrl);
          } else {
            console.error('❌ Unexpected FLUX fallback output format:', typeof fluxOutput);
            throw new Error(`Invalid output from FLUX fallback: ${typeof fluxOutput}`);
          }
        } catch (fallbackError) {
          console.error('❌ FLUX fallback also failed:', fallbackError);
          throw fallbackError;
        }
      }

    } else if (persona?.loraTraining?.status === 'training') {
      // LoRA is still training
      return NextResponse.json(
        { 
          error: 'LoRA model is still training', 
          details: `Your persona "${persona.name}" is still being trained. Please wait a few more minutes and try again.`,
          trainingStatus: persona.loraTraining.status,
          estimatedCompletion: persona.loraTraining.estimatedCompletion
        },
        { status: 202 } // 202 Accepted - request received but not yet processed
      );

    } else {
      // No LoRA available, use standard FLUX generation with speed optimization
      console.log('🎨 ❌ STANDARD FLUX PATH - No LoRA available, using standard FLUX generation');
      console.log('🔍 Why no LoRA?', {
        hasPersonaLoRA: hasPersonaLoRA,
        hasStyleLoRA: hasStyleLoRA,
        hasAnyLoRA: hasAnyLoRA,
        personaExists: !!persona,
        styleExists: !!styleTemplate,
        loraStatus: persona?.loraTraining?.status,
        modelUrl: persona?.loraTraining?.modelUrl,
        hasModelUrl: !!(persona?.loraTraining?.modelUrl)
      });
      
      const enhancedPrompt = enhancePromptForLoRA(prompt);
      const speedConfig = getSpeedConfig(speedTier, false); // No LoRA
      const fluxModel = getFluxModel(speedTier, false); // No LoRA
      
      console.log('🔧 Standard FLUX generation config:', {
        model: fluxModel,
        speedTier,
        enhancedPrompt: enhancedPrompt,
        width: 1280,
        height: 720
      });

      try {
        const dimensions = getAspectRatioDimensions(aspectRatio);
        
        const fluxOutput = await replicate.run(
          fluxModel,
          {
            input: {
              prompt: enhancedPrompt,
              width: dimensions.width,
              height: dimensions.height,
              num_inference_steps: speedConfig.num_inference_steps,
              guidance_scale: speedConfig.guidance_scale,
              output_format: "png",
              output_quality: speedConfig.output_quality,
              go_fast: speedTier === 'fast',
              disable_safety_checker: true // Disable content filtering
            }
          }
        );

        console.log('🔍 FLUX output received:', {
          type: typeof fluxOutput,
          isArray: Array.isArray(fluxOutput),
          length: Array.isArray(fluxOutput) ? fluxOutput.length : 'N/A',
          firstItemType: Array.isArray(fluxOutput) && fluxOutput.length > 0 ? typeof fluxOutput[0] : 'N/A'
        });

        // Handle ReadableStream response from Replicate
        if (Array.isArray(fluxOutput) && fluxOutput.length > 0) {
          const firstItem = fluxOutput[0];
          
          // Check if it's a ReadableStream
          if (firstItem && typeof firstItem === 'object' && 'locked' in firstItem) {
            console.log('🔄 Detected ReadableStream, converting to URL...');
            
            try {
              // Convert ReadableStream to URL
              // The stream should contain the image data
              const reader = firstItem.getReader();
              const chunks = [];
              
              while (true) {
                const { done, value } = await reader.read();
                if (done) break;
                chunks.push(value);
              }
              
              // Combine chunks into a single Uint8Array
              const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
              const combined = new Uint8Array(totalLength);
              let offset = 0;
              
              for (const chunk of chunks) {
                combined.set(chunk, offset);
                offset += chunk.length;
              }
              
              // Convert to base64 data URL
              const base64 = Buffer.from(combined).toString('base64');
              finalThumbnailUrl = `data:image/png;base64,${base64}`;
              
              console.log('✅ Converted ReadableStream to data URL');
            } catch (streamError) {
              console.error('❌ Failed to process ReadableStream:', streamError);
              throw new Error('Failed to process image stream from FLUX');
            }
          } else if (typeof firstItem === 'string') {
            finalThumbnailUrl = firstItem;
            console.log('✅ Using direct URL from array:', finalThumbnailUrl);
          } else {
            console.error('❌ Unexpected first item type:', typeof firstItem);
            throw new Error(`Invalid first item in FLUX output: ${typeof firstItem}`);
          }
        } else if (typeof fluxOutput === 'string') {
          finalThumbnailUrl = fluxOutput;
          console.log('✅ Using direct string output:', finalThumbnailUrl);
        } else {
          console.error('❌ Unexpected FLUX output format:', typeof fluxOutput);
          throw new Error(`Invalid output from FLUX: ${typeof fluxOutput}`);
        }
      } catch (error) {
        console.error('❌ FLUX generation failed:', error);
        throw error;
      }
    }

    // Apply style transfer if style image is provided
    if (styleImageData && finalThumbnailUrl) {
      console.log('🎨 Applying style transfer...');
      
      try {
        const styleTransferResponse = await fetch('https://fal.run/fal-ai/flux/dev/image-to-image', {
          method: 'POST',
          headers: {
            'Authorization': `Key ${process.env.FAL_API_KEY}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            image_url: finalThumbnailUrl,
            prompt: `Apply the artistic style from the reference image while maintaining the subject and composition. Match the colors, lighting, and visual aesthetic.`,
            strength: Math.min(0.7, styleStrength + 0.2),
            num_inference_steps: 30,
            guidance_scale: 7.5,
          }),
        });

        if (styleTransferResponse.ok) {
          const styleResult = await styleTransferResponse.json();
          if (styleResult.images && styleResult.images.length > 0) {
            finalThumbnailUrl = styleResult.images[0].url;
            console.log('✅ Style transfer successful');
          }
        } else {
          console.log('⚠️ Style transfer failed, using original image');
        }
      } catch (styleError) {
        console.log('⚠️ Style transfer error, using original image:', styleError);
      }
    }

    // Validate final URL
    if (!finalThumbnailUrl || typeof finalThumbnailUrl !== 'string') {
      console.error('❌ Final URL validation failed:');
      console.error('🔍 finalThumbnailUrl value:', finalThumbnailUrl);
      console.error('🔍 finalThumbnailUrl type:', typeof finalThumbnailUrl);
      console.error('🔍 hasPersonaLoRA:', hasPersonaLoRA);
      console.error('🔍 hasStyleLoRA:', hasStyleLoRA);
      console.error('🔍 hasAnyLoRA:', hasAnyLoRA);
      console.error('🔍 persona status:', persona?.loraTraining?.status);
      throw new Error('No valid image URL generated');
    }
    
    console.log('✅ Final URL validation passed:', finalThumbnailUrl);

    // 🚀 NEW: Store image permanently in Supabase (with improved error handling)
    console.log('💾 Attempting to store image permanently in Supabase...');
    
    try {
      const generationParams = {
        baseModel: 'black-forest-labs/flux-dev',
        loraModel: persona?.loraTraining?.status === 'completed' ? persona.loraTraining.modelUrl : null,
        loraScale: 1.0,
        faceStrength,
        styleStrength,
        expressionOverride,
        hasStyle: !!styleImageData,
        hasLoRA: persona?.loraTraining?.status === 'completed',
        triggerWord: persona?.loraTraining?.triggerWord,
        method: persona?.loraTraining?.status === 'completed' ? 'flux-dev-with-lora' : 'flux-dev-only',
        speedTier
      };

      console.log('🔄 Calling storeGeneratedImage...');
      const storageResult = await storeGeneratedImage(
        finalThumbnailUrl,
        enhancePromptForLoRA(prompt, persona?.loraTraining?.triggerWord),
        persona?.id,
        generationParams
      );

      console.log('✅ Storage successful:', storageResult);

      // Return the final result with permanent URL
      const result = {
        imageUrl: storageResult.permanentUrl, // ✨ Now using permanent Supabase URL
        temporaryUrl: finalThumbnailUrl, // Keep original for debugging
        imageId: storageResult.record.id, // Database record ID
        model: 'flux-dev-with-lora',
        success: true, // Explicit success flag
        steps: {
          loraGeneration: persona?.loraTraining?.status === 'completed' ? storageResult.permanentUrl : null,
          baseGeneration: persona?.loraTraining?.status !== 'completed' ? storageResult.permanentUrl : null,
          styleTransfer: styleImageData ? storageResult.permanentUrl : null
        },
        metadata: {
          prompt: enhancePromptForLoRA(prompt, persona?.loraTraining?.triggerWord),
          baseModel: 'black-forest-labs/flux-dev',
          loraModel: persona?.loraTraining?.status === 'completed' ? persona.loraTraining.modelUrl : null,
          loraScale: 1.0,
          faceStrength,
          styleStrength,
          expressionOverride,
          hasStyle: !!styleImageData,
          hasLoRA: persona?.loraTraining?.status === 'completed',
          triggerWord: persona?.loraTraining?.triggerWord,
          method: persona?.loraTraining?.status === 'completed' ? 'flux-dev-with-lora' : 'flux-dev-only',
          speedTier,
          stored: true, // Indicates image is permanently stored
          fileName: storageResult.record.file_name
        }
      };

      // Validate response before returning
      if (!result.imageUrl) {
        console.error('❌ CRITICAL: Result missing imageUrl!', result);
        throw new Error('Response validation failed: missing imageUrl');
      }

      console.log('🎉 FLUX LoRA generation completed successfully with permanent storage');
      console.log('📤 Returning result with imageUrl:', result.imageUrl);
      return NextResponse.json(result);
      
    } catch (storageError) {
      console.error('❌ Storage failed due to Supabase throttling/limitations:', storageError);
      console.error('🔍 Storage error details:', {
        errorMessage: storageError instanceof Error ? storageError.message : 'Unknown error',
        errorName: storageError instanceof Error ? storageError.name : 'Unknown',
        finalThumbnailUrl: finalThumbnailUrl ? 'Valid URL available' : 'No URL'
      });
      
      // 🚀 GRACEFUL FALLBACK: Return temporary URL without throwing error
      console.log('⚠️ Using temporary URL fallback due to storage limitations');
      
      const fallbackResult = {
        imageUrl: finalThumbnailUrl, // Use working temporary URL
        temporaryUrl: finalThumbnailUrl,
        imageId: null,
        model: 'flux-dev-with-lora',
        success: true, // Explicit success flag
        warning: 'Image stored temporarily (24-48h) due to service limitations',
        steps: {
          loraGeneration: persona?.loraTraining?.status === 'completed' ? finalThumbnailUrl : null,
          baseGeneration: persona?.loraTraining?.status !== 'completed' ? finalThumbnailUrl : null,
          styleTransfer: styleImageData ? finalThumbnailUrl : null
        },
        metadata: {
          prompt: enhancePromptForLoRA(prompt, persona?.loraTraining?.triggerWord),
          baseModel: 'black-forest-labs/flux-dev',
          loraModel: persona?.loraTraining?.status === 'completed' ? persona.loraTraining.modelUrl : null,
          loraScale: 1.0,
          faceStrength,
          styleStrength,
          expressionOverride,
          hasStyle: !!styleImageData,
          hasLoRA: persona?.loraTraining?.status === 'completed',
          triggerWord: persona?.loraTraining?.triggerWord,
          method: persona?.loraTraining?.status === 'completed' ? 'flux-dev-with-lora' : 'flux-dev-only',
          speedTier,
          stored: false, // Indicates storage failed but generation succeeded
          storageError: 'Service limitations - using temporary URL'
        }
      };

      // Validate fallback response before returning
      if (!fallbackResult.imageUrl) {
        console.error('❌ CRITICAL: Fallback result missing imageUrl!', fallbackResult);
        throw new Error('Fallback validation failed: missing imageUrl');
      }

      console.log('✅ Returning successful generation with temporary URL');
      console.log('📤 Fallback result imageUrl:', fallbackResult.imageUrl);
      return NextResponse.json(fallbackResult);
    }

  } catch (error) {
    console.error('❌ Generation failed:', error);
    console.error('🔍 Error details:', {
      errorMessage: error instanceof Error ? error.message : 'Unknown error',
      errorName: error instanceof Error ? error.name : 'Unknown',
      errorStack: error instanceof Error ? error.stack : 'No stack trace',
      finalThumbnailUrl: typeof finalThumbnailUrl !== 'undefined' ? finalThumbnailUrl : 'undefined'
    });
    
    const errorResponse = { 
      error: 'Generation failed', 
      details: error instanceof Error ? error.message : 'Unknown error',
      success: false,
      finalUrl: typeof finalThumbnailUrl !== 'undefined' ? finalThumbnailUrl : null
    };
    
    console.log('📤 Returning error response:', errorResponse);
    return NextResponse.json(errorResponse, { status: 500 });
  }
}

// Helper functions for speed optimization
function getSpeedConfig(speedTier: string, hasLoRA: boolean = false) {
  // LoRA models have their own optimal parameters regardless of speed tier
  if (hasLoRA) {
    return {
      num_inference_steps: 28, // Optimal for LoRA models
      guidance_scale: 3.5,
      output_quality: 90,
      megapixels: "1"
    };
  }

  // For non-LoRA generations, use speed-specific configs
  switch (speedTier) {
    case 'fast':
      return {
        num_inference_steps: 4, // Schnell optimized
        guidance_scale: 2.5,
        output_quality: 80,
        megapixels: "0.5" // Lower resolution for speed
      };
    case 'quality':
      return {
        num_inference_steps: 50, // Higher quality
        guidance_scale: 4.0,
        output_quality: 95,
        megapixels: "1"
      };
    default: // balanced
      return {
        num_inference_steps: 28,
        guidance_scale: 3.5,
        output_quality: 90,
        megapixels: "1"
      };
  }
}

function getFluxModel(speedTier: string, hasLoRA: boolean = false) {
  // CRITICAL: LoRA models are trained on FLUX-dev and ONLY work with FLUX-dev
  // Cannot use FLUX-Juiced or FLUX-Pro with LoRA models
  if (hasLoRA) {
    console.log('🎯 LoRA detected: forcing FLUX-dev for compatibility');
    return "black-forest-labs/flux-dev"; // Always use FLUX-dev for LoRA
  }

  // For non-LoRA generations, use speed-optimized models
  switch (speedTier) {
    case 'fast':
      return "prunaai/flux.1-juiced"; // 2.6x faster optimized version
    case 'quality':
      return "black-forest-labs/flux-pro"; // Highest quality
    default: // balanced
      return "black-forest-labs/flux-dev"; // Good balance
  }
} 