export interface Persona {
  id: string
  name: string
  description?: string
  imageUrl: string // Main display image (first uploaded image)
  imageBase64: string // Main display image base64
  createdAt: Date
  updatedAt: Date
  usageCount: number
  category?: 'business' | 'gaming' | 'casual' | 'professional' | 'other'
  isDefault?: boolean
  lastUsed?: Date
  // LoRA Training fields - Updated for Fast FLUX Trainer
  loraTraining?: {
    status: 'pending' | 'training' | 'completed' | 'failed'
    trainingId?: string
    modelUrl?: string
    triggerWord: string // Required unique trigger word (e.g., "TOK", "CYBRPNK")
    trainingProgress?: number
    errorMessage?: string
    trainingImages: TrainingImage[] // Multiple training images (10-20 recommended)
    trainingZipUrl?: string // ZIP file URL for Replicate training
    createdAt?: Date
    completedAt?: Date
    // Fast FLUX Trainer specific parameters
    steps: number // Default 1000 steps
    learningRate?: number // Default from Replicate
    batchSize?: number // Default from Replicate
    resolution: number // Target resolution (1024 recommended)
    loraType: 'subject' | 'style' // For personas, always 'subject'
  }
}

export interface TrainingImage {
  id: string
  originalName: string
  processedImageBase64: string // Processed to optimal resolution
  uploadedAt: Date
  expression?: string // Optional expression description
  setting?: string // Optional setting description
  // Storage optimization fields
  storageUrl?: string // URL to image in Supabase Storage
  storagePath?: string // Path in storage for cleanup
}

export interface CreatePersonaRequest {
  name: string
  description?: string
  imageFiles: File[] // Multiple images (minimum 10)
  category?: Persona['category']
  isDefault?: boolean
  triggerWord?: string // Optional custom trigger word
}

export interface PersonaContextType {
  personas: Persona[]
  selectedPersona: Persona | null
  isLoading: boolean
  error: string | null
  
  // Actions
  createPersona: (request: CreatePersonaRequest) => Promise<Persona>
  updatePersona: (id: string, updates: Partial<Persona>) => Promise<Persona>
  deletePersona: (id: string) => Promise<void>
  selectPersona: (persona: Persona | null) => void
  setDefaultPersona: (id: string) => Promise<void>
  refreshPersonas: () => Promise<void>
}

export interface StyleTemplate {
  id: string
  name: string
  category: 'gaming' | 'tech' | 'vlog' | 'educational' | 'fitness' | 'cooking' | 'music' | 'business' | 'other'
  description: string
  imageUrl: string
  tags: string[]
  createdAt: string
  usageCount: number
}

export interface ThumbnailGeneration {
  prompt: string
  persona?: Persona
  styleTemplate?: StyleTemplate
  model: string
  faceStrength?: number
  styleStrength?: number
  expressionOverride?: string
} 