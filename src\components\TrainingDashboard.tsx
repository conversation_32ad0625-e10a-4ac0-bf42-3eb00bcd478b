'use client'

import { Plus } from 'lucide-react'
import { Button } from './ui/Button'

export function TrainingDashboard() {

  return (
    <div className="space-y-6">
      {/* Simple Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-white mb-2">LoRA Training</h1>
        <p className="text-white/70">Create custom personas with AI training</p>
      </div>

      {/* Main Training Interface */}
      <div className="max-w-2xl mx-auto">
        <div className="bg-bg-secondary/50 backdrop-blur-sm border border-white/10 rounded-lg p-8">
          <div className="text-center mb-6">
            <Button
              onClick={() => {
                // Use the same navigation event as persona buttons
                window.dispatchEvent(new CustomEvent('navigateToTraining', { 
                  detail: { openNewTraining: true } 
                }))
              }}
              variant="primary"
              size="lg"
              className="flex items-center space-x-3 mx-auto px-8 py-4 text-lg"
            >
              <Plus className="h-6 w-6" />
              <span>Start New Training</span>
            </Button>
          </div>
          
          <div className="text-center text-white/60 space-y-2">
            <p className="text-sm">Upload 10-50 high-quality images of yourself</p>
            <p className="text-sm">Training takes ~15-20 minutes with A100 GPU</p>
            <p className="text-sm">Cost: $2-4 USD per training session</p>
          </div>
        </div>
      </div>

    </div>
  )
} 