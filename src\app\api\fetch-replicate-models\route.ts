import { NextRequest, NextResponse } from 'next/server'

interface ReplicateModel {
  id: string
  name: string
  description?: string
  url: string
  visibility: string
  created_at: string
  updated_at: string
  latest_version?: {
    id: string
    created_at: string
  }
  owner: string
}

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Fetching Replicate models for zeddora...')
    
    const { searchParams } = new URL(request.url)
    const username = searchParams.get('username') || 'zeddora'
    const includePrivate = searchParams.get('includePrivate') === 'true'
    
    // Replicate API endpoint
    const apiUrl = `https://api.replicate.com/v1/models?owner=${username}`
    
    console.log('📡 Fetching from:', apiUrl)
    console.log('🔐 Include private models:', includePrivate)
    
    // Prepare headers
    const headers: Record<string, string> = {
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    }
    
    // Add authentication if we want private models and have the token
    if (includePrivate && process.env.REPLICATE_API_TOKEN) {
      headers['Authorization'] = `Token ${process.env.REPLICATE_API_TOKEN}`
      console.log('🔑 Using authenticated request')
    }
    
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers
    })
    
    console.log('📊 Replicate API Status:', response.status)
    
    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ Replicate API Error:', errorText)
      return NextResponse.json({
        error: 'Failed to fetch models from Replicate',
        details: errorText,
        status: response.status
      }, { status: response.status })
    }
    
    const data = await response.json()
    console.log('📋 Replicate Response:', data)
    
    if (data.results && Array.isArray(data.results)) {
      const models = data.results
      console.log(`✅ Found ${models.length} models for user: ${username}`)
      
      // Process and categorize models
      const processedModels = models.map((model: ReplicateModel) => {
        const isLora = model.description?.toLowerCase().includes('lora') || 
                      model.description?.toLowerCase().includes('flux') ||
                      model.name.toLowerCase().includes('lora')
        
        return {
          id: model.id,
          name: model.name,
          description: model.description,
          url: model.url,
          visibility: model.visibility,
          created_at: model.created_at,
          updated_at: model.updated_at,
          latest_version: model.latest_version ? {
            id: model.latest_version.id,
            created_at: model.latest_version.created_at
          } : null,
          isLora,
          owner: model.owner
        }
      })
      
      // Create summary
      const loraModels = processedModels.filter((m: any) => m.isLora)
      const summary = {
        totalModels: models.length,
        loraModels: loraModels.length,
        otherModels: models.length - loraModels.length,
        username: username,
        includePrivate: includePrivate
      }
      
      return NextResponse.json({
        success: true,
        models: processedModels,
        loraModels: loraModels,
        summary: summary
      })
      
    } else {
      console.log('❌ Unexpected response format from Replicate')
      return NextResponse.json({
        error: 'Unexpected response format from Replicate API',
        data: data
      }, { status: 500 })
    }
    
  } catch (error) {
    console.error('❌ Error fetching Replicate models:', error)
    return NextResponse.json({
      error: 'Failed to fetch models',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  return NextResponse.json({
    message: 'Use GET method to fetch Replicate models',
    endpoints: {
      'GET /api/fetch-replicate-models': 'Fetch public models for zeddora',
      'GET /api/fetch-replicate-models?username=zeddora': 'Fetch models for specific user',
      'GET /api/fetch-replicate-models?includePrivate=true': 'Include private models (requires REPLICATE_API_TOKEN)'
    }
  })
} 