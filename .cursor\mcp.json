{"mcpServers": {"supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--project-ref=xujzgjeoawhxkauzcvgj", "--access-token", "********************************************"], "env": {"SUPABASE_ACCESS_TOKEN": "********************************************", "SUPABASE_URL": "https://xujzgjeoawhxkauzcvgj.supabase.co", "SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh1anpnamVvYXdoeGthdXpjdmdqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxNjE1MDEsImV4cCI6MjA2NTczNzUwMX0.cLh7dA4SbE0W0PNXWEq2uGcZ4bokd8d6EhhKRiPUvyg"}}}}