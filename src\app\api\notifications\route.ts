import { NextRequest, NextResponse } from 'next/server'
import { getAllNotifications, getPersonaNotifications, getTrainingProgress } from '../../../utils/webhookNotifications'

/**
 * GET /api/notifications - Fetch webhook notifications
 * Query params:
 * - personaId: Filter by specific persona
 * - type: Filter by notification type
 * - limit: Limit number of results
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const personaId = searchParams.get('personaId')
    const type = searchParams.get('type')
    const limit = parseInt(searchParams.get('limit') || '20')
    
    let notifications
    
    if (personaId) {
      if (type === 'progress') {
        // Get training progress for specific persona
        notifications = getTrainingProgress(personaId)
      } else {
        // Get all notifications for specific persona
        notifications = getPersonaNotifications(personaId)
      }
    } else {
      // Get all notifications
      notifications = getAllNotifications()
    }
    
    // Apply type filter if specified
    if (type && type !== 'progress') {
      notifications = notifications.filter(n => n.type === type)
    }
    
    // Apply limit
    notifications = notifications.slice(0, limit)
    
    return NextResponse.json({
      success: true,
      notifications,
      count: notifications.length,
      filters: {
        personaId: personaId || 'all',
        type: type || 'all',
        limit
      }
    })
    
  } catch (error) {
    console.error('❌ Error fetching notifications:', error)
    return NextResponse.json(
      { 
        error: 'Failed to fetch notifications',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
} 