import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '../../../lib/supabase'

/**
 * Server-Side Background Training Monitor
 * This endpoint automatically monitors active trainings and updates the database
 * when they complete. It should be called periodically by a cron job or scheduler.
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Starting background training monitor...')
    
    if (!supabase) {
      return NextResponse.json({
        success: false,
        error: 'Database not configured'
      }, { status: 500 })
    }

    // Step 1: Get all active training jobs (incomplete trainings)
    const { data: activeTrainings, error: fetchError } = await supabase
      .from('training_jobs')
      .select('*')
      .in('status', ['training', 'starting', 'processing'])
      .order('created_at', { ascending: false })

    if (fetchError) {
      console.error('❌ Failed to fetch active trainings:', fetchError)
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch active trainings'
      }, { status: 500 })
    }

    if (!activeTrainings || activeTrainings.length === 0) {
      console.log('✅ No active trainings to monitor')
      return NextResponse.json({
        success: true,
        message: 'No active trainings found',
        processed: 0
      })
    }

    console.log(`📊 Found ${activeTrainings.length} active training(s) to monitor`)

    // Step 2: Check each active training with Replicate
    const replicateToken = process.env.REPLICATE_API_TOKEN
    if (!replicateToken) {
      return NextResponse.json({
        success: false,
        error: 'Replicate API token not configured'
      }, { status: 500 })
    }

    let processedCount = 0
    let completedCount = 0
    const results = []

    for (const training of activeTrainings) {
      try {
        console.log(`🔍 Checking training: ${training.replicate_training_id}`)
        
        // Fetch current status from Replicate
        const replicateResponse = await fetch(
          `https://api.replicate.com/v1/trainings/${training.replicate_training_id}`,
          {
            headers: {
              'Authorization': `Token ${replicateToken}`,
              'Content-Type': 'application/json'
            }
          }
        )

        if (!replicateResponse.ok) {
          console.error(`❌ Failed to fetch training ${training.replicate_training_id}: ${replicateResponse.status}`)
          results.push({
            trainingId: training.replicate_training_id,
            status: 'error',
            error: `HTTP ${replicateResponse.status}`
          })
          continue
        }

        const trainingData = await replicateResponse.json()
        processedCount++

        // Step 3: Update database if status changed
        const currentStatus = trainingData.status
        const newStatus = currentStatus === 'succeeded' ? 'completed' : 
                         currentStatus === 'failed' ? 'failed' :
                         currentStatus === 'canceled' ? 'failed' : 'training'

        const updateData: any = {
          status: newStatus,
          updated_at: new Date().toISOString()
        }

        // Add progress if available
        if (trainingData.progress) {
          updateData.progress = Math.round(trainingData.progress * 100)
        }

        // Handle completion
        if (currentStatus === 'succeeded') {
          // Extract model URL
          let modelUrl = null
          if (trainingData.output?.version) {
            modelUrl = trainingData.output.version
          } else if (trainingData.destination) {
            modelUrl = trainingData.destination
          } else if (trainingData.output?.weights) {
            modelUrl = trainingData.output.weights
          }

          if (modelUrl) {
            updateData.model_url = modelUrl
            updateData.completed_at = trainingData.completed_at || new Date().toISOString()
            completedCount++
            
            console.log(`✅ Training completed! Model URL: ${modelUrl}`)

            // Step 4: Update associated persona
            if (training.persona_id) {
              await supabase
                .from('personas')
                .update({
                  model_url: modelUrl,
                  updated_at: new Date().toISOString()
                })
                .eq('id', training.persona_id)
              
              console.log(`🎭 Updated persona ${training.persona_id} with model URL`)
            }
          }
        }

        // Update training job
        await supabase
          .from('training_jobs')
          .update(updateData)
          .eq('id', training.id)

        results.push({
          trainingId: training.replicate_training_id,
          status: currentStatus,
          updated: true,
          modelUrl: updateData.model_url || null
        })

        console.log(`📊 Updated training ${training.replicate_training_id}: ${currentStatus}`)

      } catch (error) {
        console.error(`❌ Error processing training ${training.replicate_training_id}:`, error)
        results.push({
          trainingId: training.replicate_training_id,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    console.log(`✅ Background monitor completed: ${processedCount} processed, ${completedCount} completed`)

    return NextResponse.json({
      success: true,
      processed: processedCount,
      completed: completedCount,
      results
    })

  } catch (error) {
    console.error('❌ Background training monitor error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// Health check endpoint
export async function GET(request: NextRequest) {
  return NextResponse.json({
    status: 'ready',
    service: 'background-training-monitor',
    timestamp: new Date().toISOString()
  })
} 