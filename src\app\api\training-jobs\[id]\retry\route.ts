import { NextRequest, NextResponse } from 'next/server'

/**
 * DEPRECATED: Legacy Training Job Retry Endpoint
 * 
 * This endpoint is part of the old persona-based training system.
 * The new direct training system handles retries through the Training Dashboard.
 */

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { id } = params
  console.log(`⚠️ DEPRECATED: Training job retry endpoint called for job ${id}`)
  
  return NextResponse.json({
    error: 'Training job retry endpoint has been deprecated',
    message: 'The legacy training job system has been replaced with the new direct training approach.',
    solution: 'Use the Training Dashboard to create new personas with LoRA training.',
    migration: {
      oldSystem: 'Database-tracked training jobs with retry functionality',
      newSystem: 'Direct training API with immediate feedback and auto-persona creation',
      benefits: [
        'No database complexity for training jobs',
        'A100 GPU for faster, more reliable training',
        'Immediate error feedback without retry needed',
        'Automatic persona creation on success'
      ]
    },
    jobId: id,
    deprecated: true,
    timestamp: new Date().toISOString()
  }, { status: 410 }) // 410 Gone - resource permanently removed
} 