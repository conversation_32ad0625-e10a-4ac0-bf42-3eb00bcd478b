'use client'

import { useState } from 'react'
import { PreviewPanel } from '@/components/sections/PreviewPanel'

export function ThumbnailCreator() {
  const [prompt, setPrompt] = useState('')
  const [selectedPersona, setSelectedPersona] = useState<string | null>(null)
  const [selectedInspiration, setSelectedInspiration] = useState<string | null>(null)
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedImage, setGeneratedImage] = useState<string | null>(null)

  const handleGenerate = async () => {
    setIsGenerating(true)
    // TODO: Implement actual generation logic
    setTimeout(() => {
      setGeneratedImage('/demo-earth-thumbnail.svg')
      setIsGenerating(false)
    }, 3000)
  }

  return (
    <div className="min-h-screen relative overflow-hidden">

      <div className="relative z-10 container mx-auto p-6">
        <div className="mx-auto max-w-5xl">
          <PreviewPanel 
            isGenerating={isGenerating}
            generatedImage={generatedImage}
            prompt={prompt}
            onPromptChange={setPrompt}
            selectedPersona={selectedPersona}
            onPersonaChange={setSelectedPersona}
            selectedInspiration={selectedInspiration}
            onInspirationChange={setSelectedInspiration}
            onGenerate={handleGenerate}
          />
        </div>
      </div>
    </div>
  )
} 