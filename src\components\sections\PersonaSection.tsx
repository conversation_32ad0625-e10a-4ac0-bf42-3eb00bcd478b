'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { User, Plus, Edit, Trash2, X } from 'lucide-react'
import Image from 'next/image'

interface PersonaSectionProps {
  selectedPersona: string | null
  onPersonaChange: (persona: string | null) => void
}

// Mock persona data
const personas = [
  {
    id: 'persona-1',
    name: 'Main Creator',
    image: '/personas/creator-main.jpg',
    usage: 47
  },
  {
    id: 'persona-2', 
    name: 'Gaming Avatar',
    image: '/personas/gaming-avatar.jpg',
    usage: 23
  }
]

export function PersonaSection({ selectedPersona, onPersonaChange }: PersonaSectionProps) {
  const [faceBlendStrength, setFaceBlendStrength] = useState(80)
  const [expression, setExpression] = useState('auto')

  return (
    <section className="rounded-lg border border-border-primary bg-bg-secondary p-6">
      {/* Section Header */}
      <div className="mb-4 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <User className="h-5 w-5 text-white" />
          <h3 className="text-lg font-semibold text-text-primary">
            Choose Your Persona
          </h3>
        </div>
        <Button 
          size="sm" 
          variant="outline" 
          className="flex items-center space-x-1 text-xs"
          onClick={() => {
            // Navigate to Training Dashboard and open New Training modal
            window.dispatchEvent(new CustomEvent('navigateToTraining', { 
              detail: { openNewTraining: true } 
            }))
          }}
        >
          <Plus className="h-3 w-3" />
          <span>Add New</span>
        </Button>
      </div>

      {/* Persona Grid */}
      <div className="mb-4 grid grid-cols-3 gap-3">
        {/* No Persona Option */}
        <button
          onClick={() => onPersonaChange(null)}
          className={`flex flex-col items-center space-y-2 rounded-lg border-2 p-3 transition-colors ${
            selectedPersona === null
              ? 'border-accent-primary bg-accent-primary/10'
              : 'border-border-primary bg-bg-tertiary hover:border-border-secondary'
          }`}
        >
          <div className="flex h-16 w-16 items-center justify-center rounded-lg bg-bg-primary">
            <X className="h-6 w-6 text-text-tertiary" />
          </div>
          <span className="text-xs font-medium text-text-primary">No Persona</span>
        </button>

        {/* User Personas */}
        {personas.map((persona) => (
          <button
            key={persona.id}
            onClick={() => onPersonaChange(persona.id)}
            className={`group relative flex flex-col items-center space-y-2 rounded-lg border-2 p-3 transition-colors ${
              selectedPersona === persona.id
                ? 'border-accent-primary bg-accent-primary/10'
                : 'border-border-primary bg-bg-tertiary hover:border-border-secondary'
            }`}
          >
            <div className="relative h-16 w-16 overflow-hidden rounded-lg">
              <Image
                src={persona.image}
                alt={persona.name}
                fill
                className="object-cover"
                onError={(e) => {
                  // Fallback for missing images
                  const target = e.target as HTMLImageElement
                  target.style.display = 'none'
                  const parent = target.parentElement
                  if (parent) {
                    parent.innerHTML = `<div class="flex h-full w-full items-center justify-center bg-bg-primary"><User class="h-6 w-6 text-text-tertiary" /></div>`
                  }
                }}
              />
              
              {/* Hover Overlay */}
              <div className="absolute inset-0 flex items-center justify-center space-x-1 bg-black/50 opacity-0 transition-opacity group-hover:opacity-100">
                <div className="rounded bg-white/20 p-1 cursor-pointer hover:bg-white/30 transition-colors">
                  <Edit className="h-3 w-3 text-white" />
                </div>
                <div className="rounded bg-white/20 p-1 cursor-pointer hover:bg-white/30 transition-colors">
                  <Trash2 className="h-3 w-3 text-white" />
                </div>
              </div>
            </div>
            <div className="text-center">
              <span className="text-xs font-medium text-text-primary">{persona.name}</span>
              <p className="text-xs text-text-tertiary">Used {persona.usage} times</p>
            </div>
          </button>
        ))}

        {/* Add Persona Card */}
        <button 
          className="flex flex-col items-center justify-center space-y-2 rounded-lg border-2 border-dashed border-border-primary bg-bg-tertiary p-3 transition-colors hover:border-border-secondary hover:bg-bg-tertiary/80"
          onClick={() => {
            // Navigate to Training Dashboard and open New Training modal
            window.dispatchEvent(new CustomEvent('navigateToTraining', { 
              detail: { openNewTraining: true } 
            }))
          }}
        >
          <div className="flex h-16 w-16 items-center justify-center rounded-lg bg-bg-primary">
            <Plus className="h-6 w-6 text-text-tertiary" />
          </div>
          <span className="text-xs font-medium text-text-primary">Add Persona</span>
        </button>
      </div>

      {/* Advanced Persona Settings */}
      {selectedPersona && (
        <div className="space-y-4 rounded-lg bg-bg-tertiary p-4">
          <h4 className="text-sm font-medium text-text-primary">Persona Settings</h4>
          
          {/* Face Blend Strength */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm text-text-secondary">Face Blend Strength</label>
              <span className="text-xs font-medium text-text-primary">{faceBlendStrength}%</span>
            </div>
            <input
              type="range"
              min="0"
              max="100"
              value={faceBlendStrength}
              onChange={(e) => setFaceBlendStrength(Number(e.target.value))}
              className="w-full accent-accent-primary"
            />
          </div>

          {/* Expression Override */}
          <div className="space-y-2">
            <label className="text-sm text-text-secondary">Expression Override</label>
            <select
              value={expression}
              onChange={(e) => setExpression(e.target.value)}
              className="w-full rounded-lg border border-border-primary bg-bg-primary p-2 text-sm text-text-primary focus:border-border-focus focus:outline-none"
            >
              <option value="auto">Auto (from prompt)</option>
              <option value="neutral">Neutral</option>
              <option value="happy">Happy/Excited</option>
              <option value="surprised">Surprised/Shocked</option>
              <option value="focused">Focused/Serious</option>
            </select>
          </div>
        </div>
      )}
    </section>
  )
} 