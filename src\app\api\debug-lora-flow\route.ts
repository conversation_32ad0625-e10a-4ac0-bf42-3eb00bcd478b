import { NextRequest, NextResponse } from 'next/server';
import { loadPersonas } from '../../../utils/personaStorage';

export async function GET(request: NextRequest) {
  try {
    const personas = await loadPersonas();
    
    // Analyze LoRA workflow status
    const loraAnalysis = {
      totalPersonas: personas.length,
      
      // Training status breakdown
      trainingStatus: {
        pending: personas.filter(p => p.loraTraining?.status === 'pending').length,
        training: personas.filter(p => p.loraTraining?.status === 'training').length,
        completed: personas.filter(p => p.loraTraining?.status === 'completed').length,
        failed: personas.filter(p => p.loraTraining?.status === 'failed').length,
        noTraining: personas.filter(p => !p.loraTraining).length
      },
      
      // Detailed analysis of completed personas
      completedPersonas: personas
        .filter(p => p.loraTraining?.status === 'completed')
        .map(p => ({
          id: p.id,
          name: p.name,
          hasModelUrl: !!p.loraTraining?.modelUrl,
          modelUrl: p.loraTraining?.modelUrl,
          triggerWord: p.loraTraining?.triggerWord,
          trainingId: p.loraTraining?.trainingId,
          completedAt: p.loraTraining?.completedAt,
          
          // Model URL analysis
          modelUrlAnalysis: p.loraTraining?.modelUrl ? {
            format: p.loraTraining.modelUrl.includes('http') ? 'URL' : 'model_name',
            isDestination: !p.loraTraining.modelUrl.includes('http') && p.loraTraining.modelUrl.includes('/'),
            isWeightsUrl: p.loraTraining.modelUrl.includes('replicate.delivery'),
            isSafetensors: p.loraTraining.modelUrl.includes('.safetensors'),
            isVersionId: p.loraTraining.modelUrl.includes(':') && !p.loraTraining.modelUrl.includes('http'),
            length: p.loraTraining.modelUrl.length,
            preview: p.loraTraining.modelUrl.substring(0, 60) + (p.loraTraining.modelUrl.length > 60 ? '...' : '')
          } : null,
          
          // Generation readiness check
          readyForGeneration: !!(
            p.loraTraining?.status === 'completed' &&
            p.loraTraining?.modelUrl &&
            p.loraTraining?.triggerWord
          )
        })),
      
      // Training in progress
      trainingPersonas: personas
        .filter(p => p.loraTraining?.status === 'training')
        .map(p => ({
          id: p.id,
          name: p.name,
          trainingId: p.loraTraining?.trainingId,
          progress: p.loraTraining?.trainingProgress || 0,
          triggerWord: p.loraTraining?.triggerWord
        })),
      
      // Failed training
      failedPersonas: personas
        .filter(p => p.loraTraining?.status === 'failed')
        .map(p => ({
          id: p.id,
          name: p.name,
          error: p.loraTraining?.errorMessage,
          trainingId: p.loraTraining?.trainingId
        })),
      
      // Environment check
      environment: {
        hasReplicateToken: !!process.env.REPLICATE_API_TOKEN,
        hasReplicateUsername: !!process.env.REPLICATE_USERNAME,
        hasWebhookSecret: !!process.env.REPLICATE_WEBHOOK_SECRET,
        hasWebhookUrl: !!process.env.WEBHOOK_BASE_URL,
        webhookUrl: process.env.WEBHOOK_BASE_URL ? 
          `${process.env.WEBHOOK_BASE_URL}/api/webhooks/training-complete` : 'Not configured'
      },
      
      // Common issues diagnostic
      diagnostics: {
        noCompletedPersonas: personas.filter(p => p.loraTraining?.status === 'completed').length === 0,
        completedButNoModelUrl: personas.filter(p => 
          p.loraTraining?.status === 'completed' && !p.loraTraining?.modelUrl
        ).length,
        completedButNoTriggerWord: personas.filter(p => 
          p.loraTraining?.status === 'completed' && !p.loraTraining?.triggerWord
        ).length,
        webhookNotConfigured: !process.env.WEBHOOK_BASE_URL || !process.env.REPLICATE_WEBHOOK_SECRET
      }
    };
    
    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      analysis: loraAnalysis
    });

  } catch (error) {
    return NextResponse.json(
      { 
        error: 'LoRA flow debug failed', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

// Also add a POST endpoint to manually check a specific training ID
export async function POST(request: NextRequest) {
  try {
    const { trainingId } = await request.json();
    
    if (!trainingId) {
      return NextResponse.json({ error: 'Training ID required' }, { status: 400 });
    }
    
    if (!process.env.REPLICATE_API_TOKEN) {
      return NextResponse.json({ error: 'Replicate API token not configured' }, { status: 500 });
    }
    
    // Check training status directly with Replicate
    const response = await fetch(
      `https://api.replicate.com/v1/trainings/${trainingId}`,
      {
        headers: {
          'Authorization': `Bearer ${process.env.REPLICATE_API_TOKEN}`
        }
      }
    );
    
    if (!response.ok) {
      return NextResponse.json(
        { error: `Failed to check training: ${response.status}` },
        { status: response.status }
      );
    }
    
    const trainingData = await response.json();
    
    // Also check if we have a persona with this training ID
    const personas = await loadPersonas();
    const persona = personas.find(p => p.loraTraining?.trainingId === trainingId);
    
    return NextResponse.json({
      success: true,
      trainingId,
      replicateData: {
        status: trainingData.status,
        progress: trainingData.progress,
        destination: trainingData.destination,
        output: trainingData.output,
        error: trainingData.error,
        created_at: trainingData.created_at,
        completed_at: trainingData.completed_at
      },
      personaData: persona ? {
        id: persona.id,
        name: persona.name,
        status: persona.loraTraining?.status,
        modelUrl: persona.loraTraining?.modelUrl,
        progress: persona.loraTraining?.trainingProgress
      } : null,
      syncStatus: {
        personaExists: !!persona,
        statusMatch: persona?.loraTraining?.status === trainingData.status,
        hasModelUrl: !!persona?.loraTraining?.modelUrl,
        modelUrlSource: persona?.loraTraining?.modelUrl === trainingData.destination ? 'destination' : 
                       persona?.loraTraining?.modelUrl === trainingData.output?.weights ? 'weights' : 
                       'unknown'
      }
    });

  } catch (error) {
    return NextResponse.json(
      { 
        error: 'Training check failed', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
} 