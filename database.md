# Thumbnex Database Analysis & Requirements

## 📋 **Project Overview: Thumbnex - AI Thumbnail Generator**

**Thumbnex** is a comprehensive AI-powered YouTube thumbnail generation platform built with Next.js, TypeScript, and Supabase. This is designed to be a market-dominating product in the creator economy space.

### **🎯 Core Features Identified:**

1. **AI Thumbnail Generation**
   - Text prompt → Professional 1080p+ thumbnail in 6-8 seconds
   - Uses Stable Diffusion XL + IP-Adapter for face consistency
   - Integrates with Replicate API for AI model processing

2. **Persona System** (LoRA Training)
   - Multi-image persona creation (minimum 10 training images)
   - LoRA (Low-Rank Adaptation) training for face consistency
   - Custom trigger words for AI model personalization
   - Fast FLUX trainer integration with Replicate

3. **Recreate Feature** (Signature Feature)
   - YouTube URL input → extract and recreate viral thumbnails
   - Inspiration weight system (low/medium/high similarity)
   - ControlNet + Stable Diffusion integration

4. **Personal Library System**
   - Persona Library: Multiple saved faces with categories
   - Style Templates: Pre-defined thumbnail styles
   - Usage tracking and analytics

5. **Face Detection & Processing**
   - MediaPipe integration for face detection
   - Image preprocessing and optimization
   - Automatic cropping and resizing

### **🛠️ Technology Stack:**

**Frontend:**
- Next.js 14 with TypeScript
- React with modern hooks
- Tailwind CSS for styling
- Radix UI components
- Lucide React icons

**Backend & APIs:**
- Next.js API routes
- Supabase for database and storage
- Replicate API for AI model inference
- Sharp for image processing
- JSZip for file compression

**AI/ML:**
- Stable Diffusion XL
- IP-Adapter for face consistency
- LoRA training with Fast FLUX trainer
- MediaPipe for face detection
- ControlNet for image recreation

### **📊 Current Database Schema:**

**Main Table: `personas`**
```sql
CREATE TABLE IF NOT EXISTS personas (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  image_url TEXT NOT NULL,
  image_base64 TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  usage_count INTEGER DEFAULT 0,
  category TEXT DEFAULT 'other',
  is_default BOOLEAN DEFAULT FALSE,
  last_used TIMESTAMPTZ DEFAULT NOW(),
  lora_training JSONB -- Stores LoRA training configuration and status
);
```

**Key Features:**
- Personal face storage with metadata
- LoRA training status and configuration
- Usage analytics and categorization
- Training image management (10-20 images per persona)

### **🔄 Workflow Understanding:**

1. **Persona Creation:** Upload 10+ images → Process & validate → Store in Supabase → Ready for LoRA training
2. **LoRA Training:** Create ZIP → Send to Replicate → Track progress → Store model URL
3. **Thumbnail Generation:** Prompt + Persona + Style → AI processing → Return generated image
4. **Recreate Feature:** YouTube URL → Extract thumbnail → Apply persona → Generate variation

### **💰 Business Model:**
- Subscription tiers ($12-49/month)
- Credit-based system for usage
- Pay-per-generation model
- Target: 85-95% gross margins

---

## **🚀 Required Database Tables for Full Functionality:**

### **1. Users & Authentication**
```sql
-- User management
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  subscription_tier TEXT DEFAULT 'free', -- free, basic, pro, agency
  credits_remaining INTEGER DEFAULT 10,
  credits_total_used INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  last_login TIMESTAMPTZ DEFAULT NOW()
);
```

### **2. Subscription Management**
```sql
-- Subscription tracking
CREATE TABLE subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  tier TEXT NOT NULL, -- basic, pro, agency
  status TEXT DEFAULT 'active', -- active, cancelled, expired
  current_period_start TIMESTAMPTZ NOT NULL,
  current_period_end TIMESTAMPTZ NOT NULL,
  credits_included INTEGER NOT NULL,
  price_paid DECIMAL(10,2),
  stripe_subscription_id TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### **3. Generation History**
```sql
-- Track all thumbnail generations
CREATE TABLE generations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  persona_id TEXT REFERENCES personas(id) ON DELETE SET NULL,
  style_template_id UUID REFERENCES style_templates(id) ON DELETE SET NULL,
  prompt TEXT NOT NULL,
  generated_image_url TEXT,
  youtube_url TEXT, -- for recreate feature
  inspiration_weight TEXT, -- low, medium, high
  model_used TEXT DEFAULT 'stable-diffusion-xl',
  generation_time INTERVAL,
  cost_credits INTEGER DEFAULT 1,
  status TEXT DEFAULT 'completed', -- pending, processing, completed, failed
  error_message TEXT,
  metadata JSONB, -- Additional generation parameters
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### **4. Style Templates**
```sql
-- Predefined style templates
CREATE TABLE style_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  category TEXT NOT NULL, -- gaming, tech, vlog, educational, fitness, cooking, music, business, other
  description TEXT,
  image_url TEXT NOT NULL,
  tags TEXT[] DEFAULT '{}',
  prompt_template TEXT, -- Base prompt for this style
  model_parameters JSONB, -- Style-specific AI parameters
  usage_count INTEGER DEFAULT 0,
  is_premium BOOLEAN DEFAULT FALSE,
  created_by UUID REFERENCES users(id) ON DELETE SET NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### **5. Training Jobs**
```sql
-- Track LoRA training jobs
CREATE TABLE training_jobs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  persona_id TEXT REFERENCES personas(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  replicate_training_id TEXT UNIQUE,
  status TEXT DEFAULT 'pending', -- pending, training, completed, failed
  progress INTEGER DEFAULT 0,
  training_images_count INTEGER NOT NULL,
  trigger_word TEXT NOT NULL,
  model_url TEXT,
  training_cost DECIMAL(10,2),
  estimated_completion TIMESTAMPTZ,
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  error_message TEXT,
  webhook_data JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### **6. Credit Transactions**
```sql
-- Track credit usage and purchases
CREATE TABLE credit_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  generation_id UUID REFERENCES generations(id) ON DELETE SET NULL,
  transaction_type TEXT NOT NULL, -- purchase, usage, refund, bonus
  credits_change INTEGER NOT NULL, -- positive for additions, negative for usage
  credits_balance_after INTEGER NOT NULL,
  description TEXT,
  reference_id TEXT, -- Stripe payment ID, generation ID, etc.
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### **7. User Personas Link**
```sql
-- Link personas to users
ALTER TABLE personas ADD COLUMN user_id UUID REFERENCES users(id) ON DELETE CASCADE;

-- Add user-specific policies for personas
CREATE POLICY "Users can only see their own personas" ON personas
  FOR ALL USING (auth.uid() = user_id);
```

### **8. Webhooks Log**
```sql
-- Track webhook events from external services
CREATE TABLE webhook_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_type TEXT NOT NULL, -- training_complete, payment_success, etc.
  source TEXT NOT NULL, -- replicate, stripe, etc.
  payload JSONB NOT NULL,
  processed BOOLEAN DEFAULT FALSE,
  processing_error TEXT,
  related_id TEXT, -- persona_id, user_id, training_job_id, etc.
  created_at TIMESTAMPTZ DEFAULT NOW(),
  processed_at TIMESTAMPTZ
);
```

## **🔒 Row Level Security (RLS) Policies:**

```sql
-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE generations ENABLE ROW LEVEL SECURITY;
ALTER TABLE credit_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE training_jobs ENABLE ROW LEVEL SECURITY;

-- User policies
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

-- Subscription policies
CREATE POLICY "Users can view own subscriptions" ON subscriptions
  FOR ALL USING (auth.uid() = user_id);

-- Generation policies
CREATE POLICY "Users can view own generations" ON generations
  FOR ALL USING (auth.uid() = user_id);

-- Credit transaction policies
CREATE POLICY "Users can view own transactions" ON credit_transactions
  FOR ALL USING (auth.uid() = user_id);

-- Training job policies
CREATE POLICY "Users can view own training jobs" ON training_jobs
  FOR ALL USING (auth.uid() = user_id);
```

## **📈 Indexes for Performance:**

```sql
-- Performance indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX idx_subscriptions_status ON subscriptions(status);
CREATE INDEX idx_generations_user_id ON generations(user_id);
CREATE INDEX idx_generations_created_at ON generations(created_at);
CREATE INDEX idx_generations_status ON generations(status);
CREATE INDEX idx_personas_user_id ON personas(user_id);
CREATE INDEX idx_personas_category ON personas(category);
CREATE INDEX idx_credit_transactions_user_id ON credit_transactions(user_id);
CREATE INDEX idx_training_jobs_persona_id ON training_jobs(persona_id);
CREATE INDEX idx_training_jobs_status ON training_jobs(status);
CREATE INDEX idx_webhook_events_processed ON webhook_events(processed);
CREATE INDEX idx_webhook_events_event_type ON webhook_events(event_type);
```

## **🔄 Functions and Triggers:**

```sql
-- Function to update credits after generation
CREATE OR REPLACE FUNCTION update_user_credits()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
    UPDATE users 
    SET 
      credits_remaining = credits_remaining - NEW.cost_credits,
      credits_total_used = credits_total_used + NEW.cost_credits,
      updated_at = NOW()
    WHERE id = NEW.user_id;
    
    -- Log credit transaction
    INSERT INTO credit_transactions (
      user_id, generation_id, transaction_type, 
      credits_change, credits_balance_after, description
    ) VALUES (
      NEW.user_id, NEW.id, 'usage', 
      -NEW.cost_credits, 
      (SELECT credits_remaining FROM users WHERE id = NEW.user_id),
      'Thumbnail generation: ' || LEFT(NEW.prompt, 50)
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for credit updates
CREATE TRIGGER update_credits_on_generation 
  AFTER UPDATE ON generations 
  FOR EACH ROW 
  EXECUTE FUNCTION update_user_credits();

-- Function to update persona usage
CREATE OR REPLACE FUNCTION update_persona_usage()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.status = 'completed' AND NEW.persona_id IS NOT NULL THEN
    UPDATE personas 
    SET 
      usage_count = usage_count + 1,
      last_used = NOW(),
      updated_at = NOW()
    WHERE id = NEW.persona_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for persona usage tracking
CREATE TRIGGER track_persona_usage 
  AFTER UPDATE ON generations 
  FOR EACH ROW 
  EXECUTE FUNCTION update_persona_usage();
```

## **🌟 Next Steps for Database Setup:**

1. **Create all tables** in the correct order (respecting foreign keys)
2. **Set up RLS policies** for security
3. **Create performance indexes**
4. **Add functions and triggers** for automation
5. **Insert default data** (style templates, default personas)
6. **Test with sample data** to ensure everything works
7. **Set up Supabase Storage** for image files
8. **Configure webhooks** for external service integration

This comprehensive database schema will support all the features identified in the Thumbnex project and provide a solid foundation for scaling the platform. 