import { NextRequest, NextResponse } from 'next/server';
import Replicate from 'replicate';
import { storeGeneratedImage } from '@/utils/imageStorage';

// Initialize Replicate client with proper error handling
const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN!,
});

// Intelligent prompt enhancement based on content
function enhancePromptForThumbnail(userPrompt: string): string {
  // Quality prefix - always added at the beginning
  const qualityPrefix = "photo realistic, pixel details, sharp, cinematic";
  
  const baseEnhancements = [
    "YouTube thumbnail style",
    "professional thumbnail design", 
    "eye-catching",
    "dramatic lighting",
    "vibrant saturated colors",
    "high contrast",
    "bold composition",
    "cinematic quality",
    "4K resolution",
    "sharp focus",
    "attention-grabbing",
    "professional photography",
    "studio lighting",
    "clear subject focus",
    "social media optimized",
    "masterpiece",
    "best quality",
    "ultra detailed"
  ];

  // Content-specific enhancements
  const contentKeywords = userPrompt.toLowerCase();
  const specificEnhancements = [];

  if (contentKeywords.includes('person') || contentKeywords.includes('face') || contentKeywords.includes('people')) {
    specificEnhancements.push("expressive face", "emotional expression", "dynamic pose", "engaging eyes");
  }

  if (contentKeywords.includes('action') || contentKeywords.includes('explosion') || contentKeywords.includes('fight')) {
    specificEnhancements.push("action shot", "motion blur", "dynamic movement", "intense energy");
  }

  if (contentKeywords.includes('space') || contentKeywords.includes('sci-fi') || contentKeywords.includes('futuristic')) {
    specificEnhancements.push("epic space scene", "cosmic lighting", "stellar background", "sci-fi atmosphere");
  }

  if (contentKeywords.includes('gaming') || contentKeywords.includes('game')) {
    specificEnhancements.push("gaming thumbnail", "neon colors", "digital art style", "gaming aesthetic");
  }

  // Combine all enhancements
  const allEnhancements = [...baseEnhancements, ...specificEnhancements];
  
  // Structure: Quality Prefix + User Prompt + Enhancements
  return `${qualityPrefix}, ${userPrompt}, ${allEnhancements.join(", ")}`;
}

export async function POST(request: NextRequest) {
  try {
    console.log('🔥 === API REQUEST RECEIVED ===');
    const requestBody = await request.json();
    console.log('📦 Raw request body:', JSON.stringify(requestBody, null, 2));
    
    const { prompt } = requestBody;

    if (!prompt) {
      console.log('❌ No prompt provided in request');
      return NextResponse.json(
        { error: 'Prompt is required' },
        { status: 400 }
      );
    }

    if (!process.env.REPLICATE_API_TOKEN) {
      console.log('❌ Replicate API token not configured');
      return NextResponse.json(
        { error: 'Replicate API token not configured' },
        { status: 500 }
      );
    }

    console.log('🎨 Generating thumbnail with Flux Dev...');
    console.log('📝 Original Prompt Received:', prompt);
    console.log('📏 Original Prompt Length:', prompt.length, 'characters');
    console.log('📐 Resolution: 1280x720 (High Quality YouTube Thumbnail)');

    const enhancedPrompt = enhancePromptForThumbnail(prompt);
    console.log('🚀 Enhanced Prompt Being Sent to Replicate:', enhancedPrompt);
    console.log('📏 Enhanced Prompt Length:', enhancedPrompt.length, 'characters');

    // Generate thumbnail using Flux Dev
    const replicateParams = {
      input: {
        prompt: enhancedPrompt,
        width: 1280,
        height: 720, // YouTube thumbnail high resolution (16:9)
        num_inference_steps: 28, // Flux Dev optimal steps
        guidance_scale: 3.5, // Guidance scale for Flux Dev
        output_format: "webp",
        output_quality: 90,
        disable_safety_checker: true, // Disable content filtering
      }
    };
    
    console.log('🔧 === REPLICATE API CALL ===');
    console.log('🎯 Model: black-forest-labs/flux-dev');
    console.log('⚙️ Parameters being sent to Replicate:', JSON.stringify(replicateParams, null, 2));
    console.log('⏰ Starting generation at:', new Date().toISOString());
    
    const output = await replicate.run(
      "black-forest-labs/flux-dev",
      replicateParams
    );
    
    console.log('⏰ Generation completed at:', new Date().toISOString());
    console.log('✅ Thumbnail generated successfully');
    console.log('🔍 Raw output type:', typeof output);
    console.log('🔍 Raw output:', output);

    // Handle Replicate's response format - following documentation best practices
    let imageUrl = '';
    
    // Replicate typically returns an array of URLs - use destructuring as per docs
    if (Array.isArray(output) && output.length > 0) {
      // Use destructuring assignment as recommended in Replicate docs
      const [firstResult] = output;
      imageUrl = firstResult;
    } else if (typeof output === 'string') {
      // Handle direct string return (less common)
      imageUrl = output;
    } else {
      console.error('❌ Unexpected output format from Replicate:', output);
      throw new Error('Unexpected output format received from Replicate');
    }

    console.log('📸 Extracted image URL:', imageUrl);

    // Validate URL format
    if (!imageUrl || !imageUrl.startsWith('http')) {
      console.error('❌ Invalid or missing image URL:', imageUrl);
      throw new Error('Invalid image URL format received from Replicate');
    }

    // 🚀 NEW: Store image permanently in Supabase
    console.log('💾 Storing image permanently in Supabase...');
    
    try {
      const generationParams = {
        model: 'black-forest-labs/flux-dev',
        width: 1024, // Reduced from 1280 for better memory usage
        height: 576, // Reduced from 720 for better memory usage
        num_inference_steps: 28,
        guidance_scale: 3.5,
        output_format: 'webp',
        output_quality: 85, // Slightly reduced quality for smaller file size
        enhancedPrompt: enhancedPrompt
      };

      const { permanentUrl, record } = await storeGeneratedImage(
        imageUrl,
        prompt, // Use original prompt for database
        undefined, // No persona for basic generation
        generationParams
      );

      const successResponse = {
        success: true,
        imageUrl: permanentUrl, // ✨ Now using permanent Supabase URL
        temporaryUrl: imageUrl, // Keep original for debugging
        imageId: record.id, // Database record ID
        prompt: prompt,
        metadata: {
          stored: true,
          fileName: record.file_name,
          enhancedPrompt: enhancedPrompt
        }
      };
      
      console.log('🎉 Thumbnail generation completed successfully with permanent storage');
      console.log('🔍 Sending success response:', successResponse);
      
      return NextResponse.json(successResponse);
      
    } catch (storageError) {
      console.error('❌ Failed to store image permanently:', storageError);
      
      // Fallback: return temporary URL if storage fails
      const successResponse = {
        success: true,
        imageUrl: imageUrl, // Fallback to temporary URL
        temporaryUrl: imageUrl,
        imageId: null,
        prompt: prompt,
        error: 'Failed to store image permanently',
        metadata: {
          stored: false,
          storageError: storageError instanceof Error ? storageError.message : 'Unknown storage error',
          enhancedPrompt: enhancedPrompt
        }
      };
      
      console.log('⚠️ Returning temporary URL due to storage failure');
      console.log('🔍 Sending fallback response:', successResponse);
      
      return NextResponse.json(successResponse);
    }

  } catch (error) {
    console.error('❌ Error generating thumbnail:', error);
    
    // Ensure we always return valid JSON
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorResponse = {
      success: false,
      error: 'Failed to generate thumbnail',
      details: errorMessage
    };
    
    console.log('🔍 Sending error response:', errorResponse);
    
    return NextResponse.json(errorResponse, { status: 500 });
  }
} 