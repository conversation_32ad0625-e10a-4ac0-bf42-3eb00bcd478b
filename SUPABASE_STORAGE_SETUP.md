# Supabase Storage Setup for Thumbnex

## 🔑 Required Environment Variable

To enable permanent image storage, you need to add the **Supabase Service Role Key** to your `.env.local` file.

### Step 1: Get Your Service Role Key

1. Go to your **Supabase Dashboard**: https://supabase.com/dashboard/project/xujzgjeoawhxkauzcvgj
2. Navigate to **Settings** → **API**
3. Find the section **Project API keys**
4. Copy the **service_role** key (NOT the anon key)

### Step 2: Add to Environment File

Add this line to your `.env.local` file:

```bash
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

**Important**: The service role key has admin privileges and should never be exposed to the client-side code.

## 🗂️ Storage Bucket Status

✅ **Bucket Created**: `generated-thumbnails`
✅ **Policies Set**: Public read access, public upload allowed
✅ **Database Table**: `generated_images` table created

## 🚀 Implementation Status

✅ **Image Storage Utility**: `src/utils/imageStorage.ts`
✅ **Main Generation Endpoint**: `src/app/api/ip-adapter-generation/route.ts` - Updated with permanent storage
✅ **Basic Generation Endpoint**: `src/app/api/generate-thumbnail/route.ts` - Updated with permanent storage

## 🔄 Current Workflow

1. **Generate image** with Replicate → Gets temporary URL
2. **Download image** from temporary URL
3. **Upload to Supabase Storage** → Gets permanent URL
4. **Save record** in `generated_images` table
5. **Return permanent URL** to frontend

## ⚠️ Fallback Behavior

If image storage fails, the API will:
- Return the temporary Replicate URL as fallback
- Include error information in the response
- Log the storage error for debugging

## 🧪 Testing

Once you add the service role key, you can test the image storage by:

1. Making a generation request to either endpoint
2. Checking the response for `stored: true` in metadata
3. Verifying the `imageUrl` points to your Supabase storage
4. Checking the `generated_images` table for the saved record

## 📊 Expected Response Format

```json
{
  "imageUrl": "https://xujzgjeoawhxkauzcvgj.supabase.co/storage/v1/object/public/generated-thumbnails/thumbnail_1234567890_abc123.png",
  "temporaryUrl": "https://replicate.delivery/...",
  "imageId": "uuid-of-database-record",
  "metadata": {
    "stored": true,
    "fileName": "thumbnail_1234567890_abc123.png"
  }
}
``` 