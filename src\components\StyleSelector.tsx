import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Style } from '../types/style';
import { StyleTemplate } from '../types/persona';
import { useStyles } from '../hooks/useStyles';
import { Palette, Plus, X, Sparkles, ChevronDown, Crown, Trash2, Setting<PERSON>, Star } from 'lucide-react';

interface StyleSelectorProps {
  selectedStyle: StyleTemplate | null;
  onStyleSelect: (style: StyleTemplate | null) => void;
  className?: string;
}

const CATEGORY_COLORS = {
  gaming: 'bg-purple-500',
  tech: 'bg-blue-500', 
  vlog: 'bg-green-500',
  educational: 'bg-indigo-500',
  fitness: 'bg-red-500',
  business: 'bg-gray-500',
  creative: 'bg-pink-500',
  minimal: 'bg-slate-500',
  retro: 'bg-orange-500',
  cinematic: 'bg-green-400',
  other: 'bg-gray-500'
};

export function StyleSelector({ selectedStyle, onStyleSelect, className = '' }: StyleSelectorProps) {
  const { styles, loading, error, refreshStyles, resetError } = useStyles();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  // Type cast styles to work with simplified schema
  const typedStyles = styles as any[];

  // Filter styles: show all styles (simplified approach)
  const filteredStyles = typedStyles

  // Check if style is ready (has model_url)
  const isStyleReady = (style: any) => {
    return style.model_url && style.model_url.length > 0
  }

  // Handle style selection
  const handleStyleSelect = useCallback((style: any) => {
    console.log('🎨 StyleSelector: Selecting style:', style.name, style);
    onStyleSelect(style);
    setIsDropdownOpen(false);
  }, [onStyleSelect]);

  const handleTrainStyle = async (style: any) => {
    // Navigate to Training Dashboard and open style training (same as sidebar button)
    window.dispatchEvent(new CustomEvent('navigateToTraining', { 
      detail: { openNewTraining: true, trainingType: 'style', styleId: style.id } 
    }));
    setIsDropdownOpen(false);
  };

  // No auto-refresh needed with simplified approach

  // Listen for style creation events from training
  useEffect(() => {
    const handleStyleCreated = (event: CustomEvent) => {
      console.log('🎯 Style created event received:', event.detail);
      // Refresh styles to show the new one
      refreshStyles();
    };

    window.addEventListener('styleCreated', handleStyleCreated as EventListener);
    
    return () => {
      window.removeEventListener('styleCreated', handleStyleCreated as EventListener);
    };
  }, [refreshStyles]);

  // Close dropdown when clicking outside
  useEffect(() => {
    if (!isDropdownOpen) return;
    function handleClickOutside(event: MouseEvent) {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isDropdownOpen]);

  return (
    <div className={`relative ${className}`}>
      {/* Enhanced Error Display */}
      {error && (
        <div 
          className="mb-3 p-3 backdrop-blur-xl bg-red-500/10 border-2 border-red-400/40 rounded-xl text-red-300 text-sm shadow-[0_0_30px_rgba(239,68,68,0.3)]"
          style={{
            background: 'linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.05) 50%, rgba(239, 68, 68, 0.1) 100%)',
            boxShadow: '0 0 30px rgba(239, 68, 68, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1), inset 0 -1px 0 rgba(239, 68, 68, 0.2)'
          }}
        >
          <div className="flex items-center justify-between gap-2">
            <span className="flex-1 min-w-0 break-words">{error}</span>
            <button 
              onClick={resetError} 
              className="flex-shrink-0 text-red-400 hover:text-red-300 p-1 min-w-[44px] min-h-[44px] lg:min-w-[auto] lg:min-h-[auto] lg:p-0 flex items-center justify-center touch-manipulation glass-neon rounded hover:shadow-lg hover:shadow-red-500/20 transition-all duration-300"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* Enhanced Main Selector */}
      <div className="space-y-3">
        <button
          ref={buttonRef}
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
          className="min-h-[44px] flex items-center space-x-2 px-3 py-2 rounded-lg glass-neon hover:glass-neon-strong border border-green-400/30 hover:border-green-300/50 transition-all duration-300 w-full touch-manipulation group"
          disabled={loading}
        >
          <Palette className="w-4 h-4 text-green-300 flex-shrink-0 group-hover:animate-subtle-pulse" />
          <span className="text-white font-medium text-sm lg:text-xs flex-1 min-w-0 text-left truncate">
            {selectedStyle ? selectedStyle.name : 'Styles'}
          </span>
          <ChevronDown className={`w-4 h-4 text-green-300 transition-transform flex-shrink-0 ${isDropdownOpen ? 'rotate-180' : ''}`} />
        </button>

        {/* Enhanced Dropdown Menu */}
        {isDropdownOpen && (
          <div
            ref={menuRef}
            className="fixed bottom-[100px] left-2 right-2 md:absolute md:bottom-full md:left-0 md:right-0 z-50 mb-2 p-3 backdrop-blur-3xl bg-slate-900/20 border-2 border-green-300/40 rounded-2xl max-h-[70vh] lg:max-h-80 overflow-y-auto"
            style={{
              background: 'linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 191, 0, 0.05) 50%, rgba(255, 215, 0, 0.1) 100%)',
              boxShadow: 'inset 0 1px 0 rgba(255, 255, 255, 0.2), inset 0 -1px 0 rgba(255, 215, 0, 0.2)'
            }}
          >
            {/* Enhanced Create New Button */}
            <button
              onClick={() => {
                // Navigate to Training Dashboard and open style training (same as sidebar button)
                window.dispatchEvent(new CustomEvent('navigateToTraining', { 
                  detail: { openNewTraining: true, trainingType: 'style' } 
                }))
                setIsDropdownOpen(false)
              }}
                              className="w-full flex items-center space-x-3 p-3 backdrop-blur-xl bg-white/5 hover:bg-white/10 border border-white/10 hover:border-green-300/30 rounded-lg mb-1 transition-all duration-300"
            >
              <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center flex-shrink-0 shadow-lg shadow-purple-500/30">
                <Plus className="w-5 h-5 text-white group-hover:rotate-90 transition-transform duration-300" />
              </div>
              <div className="flex-1 text-left min-w-0">
                <span className="text-white font-medium text-sm block truncate">Create New Style</span>
                <p className="text-xs text-green-200/70 truncate">Train your own custom style</p>
              </div>
            </button>

            {/* Enhanced No Style Option */}
            <button
              onClick={() => {
                console.log('🎨 StyleSelector: Selecting "No Style" (null)')
                onStyleSelect(null);
                setIsDropdownOpen(false);
              }}
              className={`w-full flex items-center space-x-3 p-3 backdrop-blur-xl bg-white/5 hover:bg-white/10 border border-white/10 hover:border-green-300/30 rounded-lg mb-1 transition-all duration-300 ${
                !selectedStyle ? 'bg-green-500/10 border-green-300/40' : ''
              }`}
            >
              <div className="w-10 h-10 bg-slate-700/50 rounded-lg flex items-center justify-center flex-shrink-0 shadow-lg">
                <span className="text-xs text-slate-400 font-medium group-hover:text-white transition-colors">None</span>
              </div>
              <div className="flex-1 text-left min-w-0">
                <span className="text-white font-medium text-sm block truncate">No Style</span>
                <p className="text-xs text-green-200/70 truncate">Use base model without style</p>
              </div>
            </button>

            {/* Enhanced Empty State */}
            {filteredStyles.length === 0 && !loading && (
              <div 
                className="p-6 text-center backdrop-blur-xl bg-white/5 border-2 border-green-300/20 rounded-xl"
                style={{
                  background: 'linear-gradient(135deg, rgba(255, 215, 0, 0.05) 0%, rgba(255, 191, 0, 0.02) 50%, rgba(255, 215, 0, 0.05) 100%)',
                  boxShadow: 'inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                }}
              >
                <Palette className="w-8 h-8 text-green-300 mx-auto mb-2 animate-float" />
                <p className="text-green-200 text-sm">No custom styles available</p>
                <p className="text-xs text-green-300/70 mt-1">Train your first style to get started</p>
              </div>
            )}

            {/* Simplified Style Items */}
            {filteredStyles.map((style: any) => {
              const isReady = isStyleReady(style)
              const isSelected = selectedStyle?.id === style.id
              
              return (
                <button
                  key={style.id}
                  onClick={() => handleStyleSelect(style)}
                  className={`w-full flex items-center space-x-3 p-3 backdrop-blur-xl bg-white/5 hover:bg-white/10 border border-white/10 hover:border-green-300/30 rounded-lg mb-1 transition-all duration-300 touch-manipulation ${isSelected ? 'bg-green-500/10 border-green-300/40' : ''}`}
                >
                  <div className="relative flex-shrink-0">
                    {style.image_base64 ? (
                      <img
                        src={style.image_base64}
                        alt={style.name}
                                                  className="w-10 h-10 rounded-lg object-cover border border-green-500/20 shadow-lg"
                      />
                    ) : (
                                              <div className="w-10 h-10 bg-gray-600 rounded-lg flex items-center justify-center border border-green-500/20 shadow-lg">
                        <Palette className="w-5 h-5 text-white" />
                      </div>
                    )}
                    {/* Simple Status indicator */}
                    {isReady && (
                      <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full shadow-lg shadow-green-500/30"></div>
                    )}
                  </div>
                  
                  <div className="flex-1 text-left min-w-0">
                                         <div className="flex items-center justify-between min-w-0">
                       <span className="text-white font-medium text-sm truncate flex-1">{style.name}</span>
                       <span className={`hidden md:inline-block text-xs px-2 py-0.5 rounded-full flex-shrink-0 ml-2 ${
                         isReady ? 'bg-green-900/30 text-green-300' : 'bg-gray-900/30 text-gray-300'
                       }`}>
                         {isReady ? 'Ready' : 'Not Ready'}
                       </span>
                     </div>
                    {style.trigger_word && (
                      <p className="text-xs text-green-200/70 truncate mt-0.5">
                        {style.trigger_word}
                      </p>
                    )}
                  </div>
                </button>
              )
            })}
          </div>
        )}
      </div>

      {/* Enhanced Overlay */}
      {isDropdownOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black/50 lg:bg-black/30 backdrop-blur-md"
          onClick={() => {
            setIsDropdownOpen(false)
          }}
        />
      )}
    </div>
  )
} 