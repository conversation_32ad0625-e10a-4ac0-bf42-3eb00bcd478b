'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { <PERSON><PERSON><PERSON>, <PERSON>, Setting<PERSON>, Shuffle, ChevronDown, ChevronUp } from 'lucide-react'

interface GenerationControlsProps {
  onGenerate: () => void
  isGenerating: boolean
  hasPrompt: boolean
  onSpeedTierChange?: (speedTier: 'fast' | 'balanced' | 'quality') => void
  speedTier?: 'fast' | 'balanced' | 'quality'
  selectedPersona?: any // Add persona to check LoRA status
}

export function GenerationControls({ 
  onGenerate, 
  isGenerating, 
  hasPrompt, 
  onSpeedTierChange,
  speedTier = 'balanced',
  selectedPersona
}: GenerationControlsProps) {
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [generationSteps, setGenerationSteps] = useState(50)
  const [creativity, setCreativity] = useState('medium')
  const [negativePrompt, setNegativePrompt] = useState('')

  // Check if selected persona has a trained LoRA
  const hasLoRA = !!(selectedPersona?.loraTraining?.status === 'completed' && selectedPersona?.loraTraining?.modelUrl)

  const speedTierOptions = [
    {
      value: 'fast',
      label: 'Fast',
      description: '2-4 seconds • Lower cost',
      icon: '⚡',
      cost: '1 credit',
      disabled: hasLoRA // Disable fast mode if LoRA is present
    },
    {
      value: 'balanced',
      label: 'Balanced',
      description: '6-12 seconds • Good quality',
      icon: '⚖️',
      cost: '2 credits',
      disabled: false
    },
    {
      value: 'quality',
      label: 'Quality',
      description: '15-25 seconds • Best results',
      icon: '💎',
      cost: '3 credits',
      disabled: hasLoRA // Disable quality mode if LoRA is present
    }
  ]

  const currentTier = speedTierOptions.find(tier => tier.value === speedTier) || speedTierOptions[1]

  return (
    <div className="space-y-4">
      {/* LoRA Compatibility Warning */}
      {hasLoRA && (
        <div className="p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
          <div className="flex items-center space-x-2">
            <span className="text-lg">🎯</span>
            <div className="flex-1">
              <div className="text-sm font-medium text-blue-400">
                LoRA Model Active
              </div>
              <div className="text-xs text-text-secondary">
                Using FLUX-dev for compatibility. Speed tiers don&apos;t apply to LoRA models.
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Speed Tier Selection */}
      <div className="space-y-3">
        <label className="text-sm font-medium text-text-primary">
          Generation Speed {hasLoRA && <span className="text-xs text-text-tertiary">(Disabled for LoRA)</span>}
        </label>
        <div className="grid grid-cols-3 gap-2">
          {speedTierOptions.map((tier) => (
            <button
              key={tier.value}
              onClick={() => !tier.disabled && onSpeedTierChange?.(tier.value as 'fast' | 'balanced' | 'quality')}
              disabled={tier.disabled}
              className={`p-3 rounded-lg border transition-all ${
                speedTier === tier.value && !tier.disabled
                  ? 'border-accent-primary bg-accent-primary/10 text-accent-primary'
                  : tier.disabled
                  ? 'border-border-primary bg-bg-tertiary text-text-tertiary opacity-50 cursor-not-allowed'
                  : 'border-border-primary bg-bg-secondary hover:border-border-focus text-text-secondary hover:text-text-primary'
              }`}
            >
              <div className="text-lg mb-1">{tier.icon}</div>
              <div className="text-sm font-medium">{tier.label}</div>
              <div className="text-xs opacity-75">{tier.description}</div>
              <div className={`text-xs font-medium mt-1 ${tier.disabled ? 'text-text-tertiary' : 'text-accent-primary'}`}>
                {hasLoRA ? 'N/A' : tier.cost}
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Primary Controls */}
      <div className="flex flex-col space-y-3 lg:flex-row lg:space-x-3 lg:space-y-0">
        <Button
          onClick={onGenerate}
          disabled={!hasPrompt || isGenerating}
          className="flex-1 bg-accent-primary text-white hover:bg-accent-primary/90 disabled:opacity-50"
          size="lg"
        >
          <Sparkles className="mr-2 h-4 w-4" />
          <span className="font-medium">
            {isGenerating ? 'Generating...' : 'Generate Thumbnail'}
          </span>
          <span className="ml-2 rounded bg-white/20 px-2 py-0.5 text-xs">
            {hasLoRA ? '2 credits' : currentTier.cost}
          </span>
        </Button>

        <Button variant="secondary" size="lg" className="lg:w-auto">
          <Save className="mr-2 h-4 w-4" />
          Save Draft
        </Button>
      </div>

      {/* Speed Tier Info */}
      <div className="p-3 bg-bg-tertiary rounded-lg border border-border-primary">
        <div className="flex items-center space-x-2">
          <span className="text-lg">{hasLoRA ? '🎯' : currentTier.icon}</span>
          <div className="flex-1">
            <div className="text-sm font-medium text-text-primary">
              {hasLoRA ? 'LoRA Model (FLUX-dev)' : `${currentTier.label} Mode Selected`}
            </div>
            <div className="text-xs text-text-secondary">
              {hasLoRA 
                ? 'LoRA models use FLUX-dev for optimal face consistency'
                : currentTier.description
              }
            </div>
          </div>
          <div className="text-xs font-medium text-accent-primary">
            {hasLoRA ? '2 credits' : currentTier.cost}
          </div>
        </div>
      </div>

      {/* Secondary Controls */}
      <div className="flex flex-col space-y-2 lg:flex-row lg:space-x-2 lg:space-y-0">
        <Button
          variant="outline"
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="flex items-center justify-center lg:flex-1"
        >
          <Settings className="mr-2 h-4 w-4" />
          Advanced Options
          {showAdvanced ? (
            <ChevronUp className="ml-2 h-4 w-4" />
          ) : (
            <ChevronDown className="ml-2 h-4 w-4" />
          )}
        </Button>

        <Button variant="outline" className="lg:w-auto">
          <Shuffle className="mr-2 h-4 w-4" />
          Random Style
        </Button>
      </div>

      {/* Advanced Settings Panel */}
      {showAdvanced && (
        <div className="rounded-lg border border-border-primary bg-bg-secondary p-4">
          <h4 className="mb-4 text-sm font-medium text-text-primary">Advanced Settings</h4>
          <div className="grid gap-4 lg:grid-cols-3">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-sm text-text-secondary">Generation Steps</label>
                <span className="text-xs font-medium text-text-primary">{generationSteps}</span>
              </div>
              <input
                type="range"
                min="20"
                max="100"
                value={generationSteps}
                onChange={(e) => setGenerationSteps(Number(e.target.value))}
                className="w-full accent-accent-primary"
                disabled={hasLoRA}
              />
              {hasLoRA && (
                <div className="text-xs text-text-tertiary">
                  LoRA models use optimal steps (28)
                </div>
              )}
            </div>
            <div className="space-y-2">
              <label className="text-sm text-text-secondary">Creativity Level</label>
              <select
                value={creativity}
                onChange={(e) => setCreativity(e.target.value)}
                disabled={hasLoRA}
                className="w-full rounded-lg border border-border-primary bg-bg-tertiary p-2 text-sm text-text-primary focus:border-border-focus focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <option value="low">Conservative</option>
                <option value="medium">Balanced</option>
                <option value="high">Creative</option>
                <option value="extreme">Experimental</option>
              </select>
              {hasLoRA && (
                <div className="text-xs text-text-tertiary">
                  LoRA models use balanced creativity
                </div>
              )}
            </div>
            <div className="space-y-2">
              <label className="text-sm text-text-secondary">Negative Prompt</label>
              <input
                type="text"
                value={negativePrompt}
                onChange={(e) => setNegativePrompt(e.target.value)}
                placeholder="What to avoid..."
                className="w-full rounded-lg border border-border-primary bg-bg-tertiary p-2 text-sm text-text-primary placeholder-text-tertiary focus:border-border-focus focus:outline-none"
              />
            </div>
          </div>
        </div>
      )}

      {/* Generation Status */}
      {isGenerating && (
        <div className="rounded-lg bg-bg-tertiary p-3">
          <div className="flex items-center space-x-3">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-border-primary border-t-accent-primary"></div>
            <div className="flex-1">
              <p className="text-sm font-medium text-text-primary">
                Generating your thumbnail with {hasLoRA ? 'LoRA model' : currentTier.label.toLowerCase() + ' mode'}...
              </p>
              <p className="text-xs text-text-secondary">
                {hasLoRA ? 'LoRA generation: 8-15 seconds' :
                 speedTier === 'fast' ? 'This usually takes 2-4 seconds' : 
                 speedTier === 'quality' ? 'This usually takes 15-25 seconds' : 
                 'This usually takes 6-12 seconds'}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 