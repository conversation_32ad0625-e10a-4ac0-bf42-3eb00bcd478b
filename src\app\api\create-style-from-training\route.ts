import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Initialize Supabase client
const supabaseUrl = 'https://xujzgjeoawhxkauzcvgj.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh1anpnamVvYXdoeGthdXpjdmdqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxNjE1MDEsImV4cCI6MjA2NTczNzUwMX0.cLh7dA4SbE0W0PNXWEq2uGcZ4bokd8d6EhhKRiPUvyg'

const supabase = createClient(supabaseUrl, supabaseKey)

export async function POST(request: NextRequest) {
  try {
    console.log('🎨 POST /api/create-style-from-training called - Creating style record from training...')
    
    const body = await request.json()
    const { styleName, category, description, triggerWord, trainingId, modelUrl, zipUrl } = body

    // Validate required fields
    if (!styleName || !triggerWord || !trainingId) {
      return NextResponse.json(
        { error: 'styleName, triggerWord, and trainingId are required' },
        { status: 400 }
      )
    }

    // Set defaults for optional fields
    const finalCategory = category || 'custom'
    const finalDescription = description || `Custom style trained with trigger word: ${triggerWord}`

    // Generate unique style ID
    const styleId = `style-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const currentTime = new Date().toISOString()

    console.log(`🎯 Creating style: ${styleName} with trigger word: ${triggerWord}`)

    // Step 1: Create style record in database
    const styleData = {
      id: styleId,
      name: styleName,
      category: finalCategory,
      description: finalDescription,
      image_url: '/placeholder-thumbnail.svg', // Default placeholder
      image_base64: '', // Empty for now
      tags: ['custom', 'trained', category || 'other'],
      usage_count: 0,
      is_default: false,
      created_at: currentTime,
      updated_at: currentTime,
      lora_training: {
        status: modelUrl ? 'completed' : 'training',
        trainingId: trainingId,
        modelUrl: modelUrl || null,
        triggerWord: triggerWord,
        trainingProgress: modelUrl ? 100 : 0,
        trainingImages: [], // Empty after ZIP upload
        trainingZipUrl: zipUrl,
        createdAt: currentTime,
        completedAt: modelUrl ? currentTime : null,
        steps: 1000,
        resolution: 1024,
        loraType: 'style'
      }
    }

    const { data: style, error: styleError } = await supabase
      .from('styles')
      .insert(styleData)
      .select()
      .single()

    if (styleError) {
      console.error('❌ Style creation error:', styleError)
      return NextResponse.json(
        { error: 'Failed to create style record', details: styleError.message },
        { status: 500 }
      )
    }

    console.log('✅ Style created:', style.id)

    // Step 2: Create training job record for webhook tracking (like personas)
    const trainingJobData = {
      style_id: style.id,
      replicate_training_id: trainingId,
      status: modelUrl ? 'completed' : 'training',
      progress: modelUrl ? 100 : 0,
      training_images_count: 3, // Default for style training
      trigger_word: triggerWord,
      model_url: modelUrl,
      training_cost: 2.50, // Estimated cost for style training
      estimated_completion: new Date(Date.now() + 15 * 60 * 1000).toISOString(), // 15 minutes
      started_at: currentTime,
      completed_at: modelUrl ? currentTime : null,
      webhook_data: {
        styleName: styleName,
        category: finalCategory,
        description: finalDescription,
        zipUrl: zipUrl
      }
    }

    const { data: trainingJob, error: trainingError } = await supabase
      .from('style_training_jobs')
      .insert(trainingJobData)
      .select()
      .single()
    
    if (trainingError) {
      console.error('❌ Training job creation error:', trainingError)
      // This is critical for webhook processing, but don't fail the whole operation
      console.error('⚠️ WARNING: Training job creation failed - webhook processing may not work!')
    } else {
      console.log('✅ Training job created:', trainingJob.id)
    }
    
    console.log('✅ Successfully created style with training job linkage:', styleId)
    
    return NextResponse.json({
      success: true,
      style: {
        id: style.id,
        name: style.name,
        triggerWord: style.lora_training.triggerWord,
        modelUrl: style.lora_training.modelUrl,
        ready: !!style.lora_training.modelUrl,
        status: style.lora_training.modelUrl ? 'completed' : 'training'
      },
      trainingJob: trainingJob ? {
        id: trainingJob.id,
        status: trainingJob.status,
        progress: trainingJob.progress,
        replicateTrainingId: trainingJob.replicate_training_id
      } : null,
      webhook: {
        configured: true,
        note: 'Webhook will automatically update this style when training completes'
      },
      message: 'Style created successfully from training data. Webhook will update when training completes.'
    })
    
  } catch (error) {
    console.error('❌ Error creating style from training:', error)
    return NextResponse.json({
      error: 'Failed to create style from training',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
} 