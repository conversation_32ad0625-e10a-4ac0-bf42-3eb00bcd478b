@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* Dark mode by default */
  :root {
    color-scheme: dark;
    --bg-primary: #000000;
    --bg-secondary: #0f0f10;
    --bg-tertiary: #1a1a1d;
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-tertiary: #666666;
    --border-primary: #333333;
    --border-secondary: #444444;
    --border-focus: #fb5607;
    --accent-primary: #fb5607;
  }

  * {
    @apply border-transparent;
  }

  body {
    font-feature-settings: "rlig" 1, "calt" 1;
    background-color: #000000;
    background-image: radial-gradient(circle, rgba(255, 255, 255, 0.15) 1px, transparent 1px);
    background-size: 24px 24px;
    background-position: 0 0, 12px 12px;
    color: var(--text-primary);
    min-height: 100vh;
  }

  /* Alternative dotted pattern - more subtle */
  body.dots-subtle {
    background-image: radial-gradient(circle, rgba(255, 255, 255, 0.08) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  /* Alternative dotted pattern - larger dots */
  body.dots-large {
    background-image: radial-gradient(circle, rgba(255, 255, 255, 0.12) 1.5px, transparent 1.5px);
    background-size: 32px 32px;
  }

  /* Alternative dotted pattern - grid style */
  body.dots-grid {
    background-image: 
      radial-gradient(circle, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
      radial-gradient(circle, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
    background-size: 24px 24px, 12px 12px;
    background-position: 0 0, 6px 6px;
  }

  /* Custom scrollbar for dark theme */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: var(--bg-secondary);
  }

  ::-webkit-scrollbar-thumb {
    background: var(--border-secondary);
    border-radius: 9999px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: var(--accent-primary);
  }
}

@layer components {
  /* Button base styles */
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 ease-in-out;
    @apply bg-bg-secondary text-white border border-white/20;
    @apply focus:outline-none focus:ring-2 focus:ring-accent-primary/50;
  }

  .btn:hover {
    @apply border-accent-primary bg-bg-tertiary;
    box-shadow: 0 0 10px rgba(251, 86, 7, 0.2);
  }

  .btn:active,
  .btn.active {
    @apply bg-accent-primary border-accent-primary text-white;
    box-shadow: 0 0 15px rgba(251, 86, 7, 0.4);
  }

  /* Button variants */
  .btn-primary {
    @apply btn;
  }

  .btn-secondary {
    @apply btn border-white/10;
  }

  .btn-outline {
    @apply btn bg-transparent border-white/30;
  }

  .btn-outline:hover {
    @apply bg-accent-primary/10 border-accent-primary;
  }

  .btn-outline:active,
  .btn-outline.active {
    @apply bg-accent-primary border-accent-primary text-white;
  }

  /* Size variants */
  .btn-sm {
    @apply px-3 py-1.5 text-sm;
  }

  .btn-lg {
    @apply px-6 py-3 text-lg;
  }

  /* Default button element styles */
  button:not([class*="btn"]) {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 ease-in-out;
    @apply bg-bg-secondary text-white border border-white/20;
    @apply focus:outline-none focus:ring-2 focus:ring-accent-primary/50;
  }

  button:not([class*="btn"]):hover {
    @apply border-accent-primary bg-bg-tertiary;
    box-shadow: 0 0 10px rgba(251, 86, 7, 0.2);
  }

  button:not([class*="btn"]):active {
    @apply bg-accent-primary border-accent-primary text-white;
    box-shadow: 0 0 15px rgba(251, 86, 7, 0.4);
  }

  /* Input button styles */
  input[type="button"],
  input[type="submit"],
  input[type="reset"] {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 ease-in-out;
    @apply bg-bg-secondary text-white border border-white/20;
    @apply focus:outline-none focus:ring-2 focus:ring-accent-primary/50;
  }

  input[type="button"]:hover,
  input[type="submit"]:hover,
  input[type="reset"]:hover {
    @apply border-accent-primary bg-bg-tertiary;
    box-shadow: 0 0 10px rgba(251, 86, 7, 0.2);
  }

  input[type="button"]:active,
  input[type="submit"]:active,
  input[type="reset"]:active {
    @apply bg-accent-primary border-accent-primary text-white;
    box-shadow: 0 0 15px rgba(251, 86, 7, 0.4);
  }
}

@layer utilities {
  /* Custom focus styles */
  .focus-visible {
    outline: none;
    ring: 2px solid #fb5607;
    ring-offset: 2px;
    ring-offset-color: #0a0a0b;
  }

  /* Background size animation utilities */
  .bg-size-200 {
    background-size: 200% 200%;
  }
  
  .bg-pos-0 {
    background-position: 0% 50%;
  }
  
  .bg-pos-100 {
    background-position: 100% 50%;
  }

  /* Gradient text utilities */
  .text-gradient {
    background: linear-gradient(135deg, #fb5607, #dc2626, #fbbf24);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Glass morphism effects */
  .glass {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .glass-dark {
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .glass-neon {
    background: transparent;
    border: 1px solid rgba(0, 204, 102, 0.25);
  }

  .glass-neon-strong {
    background: transparent;
    border: 1px solid rgba(0, 204, 102, 0.35);
  }

  /* Sidebar background with visible transparency */
  .sidebar-ultra-low-opacity {
    background: rgba(0, 0, 0, 0.25) !important;
    border-right: 1px solid rgba(0, 204, 102, 0.3) !important;
  }

  .sidebar-header-ultra-low-opacity {
    background: rgba(0, 0, 0, 0.22) !important;
    border-bottom: 1px solid rgba(0, 204, 102, 0.2) !important;
  }

  /* Animated background gradients */
  .gradient-shift {
    background: linear-gradient(
      -45deg,
      #0f172a,
      #1e293b,
      #334155,
      #475569
    );
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
  }

  /* Minimal animations */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  .animate-float-delayed {
    animation: float 6s ease-in-out infinite;
    animation-delay: 2s;
  }
  
  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }
  
  .animate-subtle-pulse {
    animation: subtlePulse 3s ease-in-out infinite;
  }

  /* Touch-friendly utilities */
  .touch-manipulation {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }
  
  /* Focus states for accessibility */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-accent-primary/50 focus:ring-offset-2 focus:ring-offset-transparent;
  }

  /* Glass card effects */
  .card-glass {
    background: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(16px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    box-shadow: 
      0 4px 32px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.05);
  }

  .card-glass-hover {
    transition: all 0.3s ease;
  }
  
  .card-glass-hover:hover {
    background: rgba(15, 23, 42, 0.9);
    border-color: rgba(255, 215, 0, 0.35);
  }
  
  /* Gold glass backgrounds */
  .bg-gold-glass {
    background: rgba(255, 215, 0, 0.08);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 215, 0, 0.25);
  }
  
  .bg-amber-glass {
    background: rgba(255, 191, 0, 0.08);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 191, 0, 0.25);
  }
  
  .bg-soft-gold-glass {
    background: rgba(245, 197, 24, 0.08);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(245, 197, 24, 0.25);
  }

  /* Darker Neon Green Text Effects */
  .text-neon {
    color: #00CC66;
  }

  .text-neon-glow {
    color: #00CC66;
    text-shadow: 0 0 10px rgba(0, 204, 102, 0.5);
  }

  .text-green-neon {
    color: #00CC66;
  }

  .text-green-bright {
    color: #00AA55;
  }

  .text-green-soft {
    color: #33DD77;
  }

  .text-green-glow {
    color: #00DD88;
  }

  .text-green-electric {
    color: #00BB44;
  }

  /* Darker Neon Green Backgrounds with Glow */
  .bg-neon-green {
    background: #00CC66;
    box-shadow: 0 0 20px rgba(0, 204, 102, 0.5);
  }

  .bg-neon-green-strong {
    background: #00CC66;
    box-shadow:
      0 0 20px rgba(0, 204, 102, 0.6),
      0 0 40px rgba(0, 204, 102, 0.4),
      0 0 80px rgba(0, 204, 102, 0.2);
  }

  .btn-neon-generate {
    background: linear-gradient(135deg, #00CC66, #00AA55);
    color: #000000;
    box-shadow:
      0 0 30px rgba(0, 204, 102, 0.8),
      0 0 60px rgba(0, 204, 102, 0.4),
      0 4px 20px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(0, 204, 102, 0.6);
    transition: all 0.3s ease;
  }

  .btn-neon-generate:hover {
    background: linear-gradient(135deg, #00AA55, #00BB44);
    color: #000000;
    box-shadow:
      0 0 40px rgba(0, 204, 102, 1),
      0 0 80px rgba(0, 204, 102, 0.6),
      0 0 120px rgba(0, 204, 102, 0.3),
      0 4px 25px rgba(0, 0, 0, 0.4);
    transform: translateY(-1px);
  }

  /* Dotted background utilities */
  .bg-dots {
    background-color: #000000;
    background-image: radial-gradient(circle, rgba(255, 255, 255, 0.15) 1px, transparent 1px);
    background-size: 24px 24px;
  }
  
  .bg-dots-subtle {
    background-color: #000000;
    background-image: radial-gradient(circle, rgba(255, 255, 255, 0.08) 1px, transparent 1px);
    background-size: 20px 20px;
  }
  
  .bg-dots-large {
    background-color: #000000;
    background-image: radial-gradient(circle, rgba(255, 255, 255, 0.12) 1.5px, transparent 1.5px);
    background-size: 32px 32px;
  }
  
  .bg-dots-grid {
    background-color: #000000;
    background-image: 
      radial-gradient(circle, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
      radial-gradient(circle, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
    background-size: 24px 24px, 12px 12px;
    background-position: 0 0, 6px 6px;
  }

  .bg-dots-dense {
    background-color: #000000;
    background-image: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0.5px, transparent 0.5px);
    background-size: 12px 12px;
  }

  .bg-dots-sparse {
    background-color: #000000;
    background-image: radial-gradient(circle, rgba(255, 255, 255, 0.2) 1px, transparent 1px);
    background-size: 48px 48px;
  }

  /* Animated dotted background */
  .bg-dots-animated {
    background-color: #000000;
    background-image: radial-gradient(circle, rgba(255, 255, 255, 0.15) 1px, transparent 1px);
    background-size: 24px 24px;
    animation: dotsFloat 30s linear infinite;
  }

  /* Grid pattern with white lines */
  .bg-grid-pattern {
    background-image: 
      linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 30px 30px;
  }

  /* Dense grid pattern */
  .bg-grid-dense {
    background-image: 
      linear-gradient(rgba(255, 255, 255, 0.15) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.15) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  /* Sparse grid pattern */
  .bg-grid-sparse {
    background-image: 
      linear-gradient(rgba(255, 255, 255, 0.08) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.08) 1px, transparent 1px);
    background-size: 50px 50px;
  }

  /* Responsive grid utilities */
  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }
  
  .grid-responsive-2 {
    @apply grid grid-cols-1 md:grid-cols-2;
  }
  
  .grid-responsive-auto {
    @apply grid grid-cols-[repeat(auto-fit,minmax(280px,1fr))];
  }

  /* Mobile-specific utilities */
  @media (max-width: 768px) {
    .mobile-stack {
      @apply flex-col space-y-3 space-x-0;
    }
    
    .mobile-full {
      @apply w-full;
    }
    
    .mobile-center {
      @apply text-center justify-center;
    }
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes glow {
  from {
    border-color: rgba(255, 215, 0, 0.25);
  }
  to {
    border-color: rgba(255, 215, 0, 0.45);
  }
}

@keyframes subtlePulse {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

@keyframes dotsFloat {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 24px 24px;
  }
}

/* React Image Crop overrides for glass theme */
.ReactCrop {
  @apply rounded-lg overflow-hidden;
}

.ReactCrop__crop-selection {
  border: 2px solid rgba(255, 215, 0, 0.8) !important;
  box-shadow: 0 0 0 9999em rgba(0, 0, 0, 0.5) !important;
}

.ReactCrop__drag-handle {
  background: rgba(255, 215, 0, 0.9) !important;
  border: 2px solid rgba(255, 255, 255, 0.8) !important;
  width: 12px !important;
  height: 12px !important;
}

.ReactCrop__drag-handle:focus {
  outline: 2px solid rgba(255, 215, 0, 0.8) !important;
}

/* Scrollbar styling */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 215, 0, 0.35) rgba(15, 23, 42, 0.5);
}

*::-webkit-scrollbar {
  width: 6px;
}

*::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.5);
  border-radius: 3px;
}

*::-webkit-scrollbar-thumb {
  background: rgba(255, 215, 0, 0.35);
  border-radius: 3px;
}

*::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 215, 0, 0.55);
}

/* Mobile touch improvements */
@media (max-width: 768px) {
  /* Larger touch targets */
  button, .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Better spacing on mobile */
  .mobile-padding {
    @apply px-4 py-3;
  }
  
  /* Improved readability */
  .mobile-text {
    font-size: 16px; /* Prevents zoom on iOS */
  }
  
  /* Better modal positioning */
  .mobile-modal {
    @apply rounded-t-2xl rounded-b-none;
    max-height: 90vh;
  }
}

/* Tablet-specific improvements */
@media (min-width: 768px) and (max-width: 1199px) {
  /* Better button alignment for tablets */
  .tablet-controls {
    @apply flex flex-row space-x-3 space-y-0;
  }
  
  /* Improved sidebar width for tablets */
  .tablet-sidebar {
    width: 300px;
  }
  
  /* Better touch targets for tablets */
  button {
    min-height: 48px;
  }
  
  /* Improved text sizing */
  input, textarea {
    font-size: 16px; /* Prevents zoom on iOS/iPadOS */
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .glass, .glass-dark, .glass-neon {
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid white;
  }
  
  .text-neon, .text-neon-glow {
    color: white;
    text-shadow: none;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animate-float,
  .animate-float-delayed,
  .animate-glow,
  .animate-subtle-pulse,
  .gradient-shift {
    animation: none;
  }
  
  .transition-all,
  .transition-colors,
  .transition-transform {
    transition: none;
  }
} 