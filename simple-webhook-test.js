const https = require('https');

console.log('🧪 SIMPLE WEBHOOK TEST');
console.log('======================');
console.log('Testing webhook endpoint connectivity...\n');

// Test 1: Check environment variables
console.log('📋 Checking environment variables...');
https.get('https://thumbnex-ejan.vercel.app/api/debug-env', (res) => {
  let data = '';
  res.on('data', (chunk) => data += chunk);
  res.on('end', () => {
    try {
      const result = JSON.parse(data);
      console.log('✅ REPLICATE_API_TOKEN:', result.checks?.REPLICATE_API_TOKEN ? 'Set' : 'Missing');
      console.log('✅ REPLICATE_USERNAME:', result.checks?.REPLICATE_USERNAME ? 'Set' : 'Missing');
      console.log('🔑 REPLICATE_WEBHOOK_SECRET:', result.checks?.REPLICATE_WEBHOOK_SECRET ? 'Set' : 'Missing');
      
      if (result.checks?.REPLICATE_WEBHOOK_SECRET) {
        console.log('\n✅ Environment looks good! Testing webhook endpoint...\n');
      } else {
        console.log('\n⚠️  REPLICATE_WEBHOOK_SECRET is missing - webhooks may not work properly\n');
      }
      
      // Test webhook endpoint regardless
      testWebhookEndpoint(result.checks?.REPLICATE_WEBHOOK_SECRET);
      
    } catch (e) {
      console.log('❌ Failed to parse environment response');
      testWebhookEndpoint(false);
    }
  });
}).on('error', (err) => {
  console.log('❌ Environment check failed:', err.message);
  testWebhookEndpoint(false);
});

function testWebhookEndpoint(hasSecret) {
  console.log('🌐 Testing webhook endpoint...');
  
  const testPayload = JSON.stringify({
    id: 'webhook-test-' + Date.now(),
    status: 'succeeded',
    type: 'training',
    created_at: new Date().toISOString(),
    completed_at: new Date().toISOString()
  });
  
  const options = {
    hostname: 'thumbnex-ejan.vercel.app',
    port: 443,
    path: '/api/webhooks/training-complete-v2',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(testPayload),
      'X-Replicate-Signature': 'sha256=test-signature'
    }
  };
  
  const startTime = Date.now();
  const req = https.request(options, (res) => {
    let responseData = '';
    res.on('data', (chunk) => responseData += chunk);
    res.on('end', () => {
      const responseTime = Date.now() - startTime;
      
      console.log('\n📊 WEBHOOK TEST RESULTS:');
      console.log('Status Code:', res.statusCode);
      console.log('Response Time:', responseTime + 'ms');
      console.log('Response Preview:', responseData.substring(0, 150) + '...');
      
      if (res.statusCode === 200) {
        console.log('\n🎉 WEBHOOK SYSTEM STATUS:');
        console.log('✅ Endpoint: Accessible and working');
        console.log('✅ Processing: Functional');
        console.log('✅ Response: Good (' + responseTime + 'ms)');
        console.log(hasSecret ? '✅ Secret: Configured' : '⚠️  Secret: Missing');
        
        if (hasSecret) {
          console.log('\n💡 RESULT: System is ready! Webhooks should work for new personas.');
          console.log('   Create a new persona to test the full workflow.');
        } else {
          console.log('\n⚠️  RESULT: Webhook endpoint works, but secret is missing.');
          console.log('   Check REPLICATE_WEBHOOK_SECRET in Vercel and redeploy.');
        }
      } else {
        console.log('\n❌ WEBHOOK ENDPOINT ISSUE:');
        console.log('   Status:', res.statusCode);
        console.log('   This means webhooks will not work properly.');
      }
      
      console.log('\n🔧 NEXT STEPS:');
      if (hasSecret && res.statusCode === 200) {
        console.log('   - System is ready! Try creating a new persona.');
      } else if (!hasSecret) {
        console.log('   - Add REPLICATE_WEBHOOK_SECRET to Vercel environment variables');
        console.log('   - Redeploy the application');
        console.log('   - Run this test again');
      } else {
        console.log('   - Check webhook endpoint implementation');
        console.log('   - Check server logs for errors');
      }
    });
  });
  
  req.on('error', (err) => {
    console.log('\n❌ WEBHOOK TEST FAILED:');
    console.log('   Error:', err.message);
    console.log('   This means the webhook endpoint is not accessible.');
  });
  
  req.setTimeout(10000, () => {
    console.log('\n❌ WEBHOOK TEST TIMEOUT:');
    console.log('   The webhook endpoint took too long to respond.');
    req.destroy();
  });
  
  req.write(testPayload);
  req.end();
} 