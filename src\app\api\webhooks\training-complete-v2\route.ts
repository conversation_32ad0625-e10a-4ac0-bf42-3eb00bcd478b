import { NextRequest, NextResponse } from 'next/server'
import crypto from 'crypto'
import { supabase } from '../../../../lib/supabase'

/**
 * Enhanced Webhook handler for Replicate training completion
 * This provides immediate updates when training completes for both personas and styles
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🎯 Enhanced webhook v2 received')
    
    const body = await request.json()
    console.log('📦 Webhook payload:', JSON.stringify(body, null, 2))

    // Log webhook event for debugging
    if (supabase) {
      try {
        await supabase.from('webhook_events').insert({
          event_type: 'training_complete_v2',
          payload: body,
          received_at: new Date().toISOString()
        })
        console.log('📝 Logged webhook event to database')
      } catch (logError) {
        console.warn('⚠️ Failed to log webhook event:', logError)
      }
    }

    // Extract training information
    const trainingId = body.id
    const status = body.status
    const output = body.output

    if (!trainingId) {
      console.error('❌ No training ID in webhook payload')
      return NextResponse.json({ 
        error: 'Missing training ID',
        received: true 
      }, { status: 400 })
    }

    console.log(`🔍 Processing training: ${trainingId}, status: ${status}`)

    if (!supabase) {
      console.error('❌ Supabase not configured')
      return NextResponse.json({ 
        error: 'Database not configured',
        received: true 
      }, { status: 500 })
    }

    // First, check if this is a persona training job
    const { data: personaTrainingJob, error: personaFetchError } = await supabase
      .from('training_jobs')
      .select('*')
      .eq('replicate_training_id', trainingId)
      .single()

    // Then, check if this is a style training job
    const { data: styleTrainingJob, error: styleFetchError } = await supabase
      .from('style_training_jobs')
      .select('*')
      .eq('replicate_training_id', trainingId)
      .single()

    console.log(`📋 Persona training job found: ${!!personaTrainingJob}`)
    console.log(`🎨 Style training job found: ${!!styleTrainingJob}`)

    // Determine which type of training this is
    let trainingJob = null
    let trainingType = null

    if (personaTrainingJob && !personaFetchError) {
      trainingJob = personaTrainingJob
      trainingType = 'persona'
      console.log(`🎭 Found persona training job: ${trainingJob.id} for persona: ${trainingJob.persona_id}`)
    } else if (styleTrainingJob && !styleFetchError) {
      trainingJob = styleTrainingJob
      trainingType = 'style'
      console.log(`🎨 Found style training job: ${trainingJob.id} for style: ${trainingJob.style_id}`)
    } else {
      console.error(`❌ Training job not found for ID: ${trainingId}`)
      console.error('Persona error:', personaFetchError)
      console.error('Style error:', styleFetchError)
      return NextResponse.json({ 
        error: 'Training job not found',
        received: true 
      }, { status: 404 })
    }

    // Prepare update data
    const updateData: any = {
      updated_at: new Date().toISOString()
    }

    // Handle different status values
    if (status === 'succeeded') {
      updateData.status = 'completed'
      updateData.progress = 100
      updateData.completed_at = body.completed_at || new Date().toISOString()

      // Extract model URL with multiple fallback options
      let modelUrl = null
      if (output?.version) {
        modelUrl = output.version
      } else if (output?.weights) {
        modelUrl = output.weights
      } else if (body.destination) {
        modelUrl = body.destination
      } else if (output?.model) {
        modelUrl = output.model
      }

      if (modelUrl) {
        updateData.model_url = modelUrl
        console.log(`✅ Extracted model URL: ${modelUrl}`)

        // Update the associated record (persona or style) with model URL
        if (trainingType === 'persona' && trainingJob.persona_id) {
          const { error: personaError } = await supabase
            .from('personas')
            .update({
              model_url: modelUrl,
              updated_at: new Date().toISOString()
            })
            .eq('id', trainingJob.persona_id)

          if (personaError) {
            console.error(`❌ Failed to update persona ${trainingJob.persona_id}:`, personaError)
          } else {
            console.log(`🎭 Successfully updated persona ${trainingJob.persona_id} with model URL`)
          }
        } else if (trainingType === 'style' && trainingJob.style_id) {
          const { error: styleError } = await supabase
            .from('styles')
            .update({
              model_url: modelUrl,
              updated_at: new Date().toISOString()
            })
            .eq('id', trainingJob.style_id)

          if (styleError) {
            console.error(`❌ Failed to update style ${trainingJob.style_id}:`, styleError)
          } else {
            console.log(`🎨 Successfully updated style ${trainingJob.style_id} with model URL`)
          }
        }
      } else {
        console.warn('⚠️ No model URL found in webhook payload')
      }
      
    } else if (status === 'failed' || status === 'canceled') {
      updateData.status = 'failed'
      if (body.error) {
        updateData.error_message = body.error.toString()
      }
      console.log(`❌ Training failed: ${status}`)
      
    } else if (status === 'processing' || status === 'starting') {
      updateData.status = 'training'
      if (body.progress) {
        updateData.progress = Math.round(body.progress * 100)
      }
      console.log(`📊 Training in progress: ${status} (${updateData.progress}%)`)
    }

    // Update the appropriate training job table
    const tableName = trainingType === 'persona' ? 'training_jobs' : 'style_training_jobs'
    const { error: updateError } = await supabase
      .from(tableName)
      .update(updateData)
      .eq('id', trainingJob.id)

    if (updateError) {
      console.error(`❌ Failed to update ${trainingType} training job:`, updateError)
      return NextResponse.json({ 
        error: `Failed to update ${trainingType} training job`,
        received: true 
      }, { status: 500 })
    }

    console.log(`✅ Successfully updated ${trainingType} training job ${trainingJob.id}`)

    // Send success response to Replicate
    return NextResponse.json({ 
      success: true,
      message: `${trainingType} training status updated successfully`,
      trainingId,
      trainingType,
      status: updateData.status,
      modelUrl: updateData.model_url || null
    })

  } catch (error) {
    console.error('❌ Webhook processing error:', error)
    
    // Still return success to prevent Replicate retries
    return NextResponse.json({ 
      error: 'Internal server error',
      received: true,
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// Health check
export async function GET() {
  return NextResponse.json({
    status: 'ready',
    webhook: 'training-complete-v2',
    timestamp: new Date().toISOString(),
    supports: ['persona_training', 'style_training']
  })
} 