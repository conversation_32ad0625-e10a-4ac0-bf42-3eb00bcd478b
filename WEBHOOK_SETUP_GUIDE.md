# 🔔 **Webhook Setup Guide - Auto-Persona Creation**

## 🎯 **Overview**
This guide sets up automatic persona updates when LoRA training completes, eliminating all manual intervention mentioned in your changelog.

## 📋 **Required Environment Variables**

Add these to your `.env.local` file:

```bash
# Webhook configuration
REPLICATE_WEBHOOK_SECRET=your_webhook_secret_here
WEBHOOK_BASE_URL=https://yourdomain.com

# Your existing variables
REPLICATE_API_TOKEN=your_replicate_token
```

## 🔧 **Setup Steps**

### **Step 1: Get Your Webhook Secret**
1. Go to [Replicate Dashboard](https://replicate.com/account/webhooks)
2. Create a new webhook endpoint
3. Copy the webhook secret

### **Step 2: Configure Your Environment**
```bash
# Replace with your actual values
REPLICATE_WEBHOOK_SECRET=whsec_your_secret_here
WEBHOOK_BASE_URL=https://yourdomain.com
```

### **Step 3: Deploy Your App**
Deploy to Vercel, <PERSON><PERSON>, or your hosting platform so Replicate can reach your webhook endpoint.

### **Step 4: Test the Webhook**
1. Create a new persona with 10+ images
2. Start LoRA training
3. Monitor the logs - you should see webhook notifications

## 🌐 **Webhook Endpoint**
Your webhook will be available at:
```
https://yourdomain.com/api/webhooks/training-complete
```

## ✅ **What This Automates**

### **Before (Manual)**
1. User starts training
2. **Manual polling** every 30 seconds
3. **Manual script** to update personas.json
4. **Manual model URL extraction**

### **After (Automated)**
1. User starts training
2. **Webhook automatically** updates persona
3. **Real-time status** updates
4. **Instant notifications**

## 🔍 **Testing Your Setup**

### **Check Webhook Endpoint**
```bash
curl -X POST https://yourdomain.com/api/webhooks/training-complete \
  -H "Content-Type: application/json" \
  -H "replicate-signature: sha256=test" \
  -d '{"test": "webhook"}'
```

### **View Notifications**
```bash
curl https://yourdomain.com/api/notifications
```

### **Check Logs**
Monitor your server logs for:
- `🔔 Webhook received: Training completion notification`
- `✅ Training completed successfully!`
- `💾 Persona "PersonaName" updated automatically`

## 🚀 **Features Enabled**

- **✅ Auto-persona updates** - No more manual scripts
- **✅ Real-time progress** - Live training status
- **✅ Instant notifications** - Know immediately when ready
- **✅ Error handling** - Graceful failure management
- **✅ Security** - Webhook signature verification

## 🔧 **Advanced Configuration**

### **Local Development**
For local testing, use ngrok:
```bash
ngrok http 3000
# Use the ngrok URL as your WEBHOOK_BASE_URL
```

### **Production Deployment**
1. Deploy to your hosting platform
2. Set environment variables in your hosting dashboard
3. Update `WEBHOOK_BASE_URL` to your production domain

## 📊 **Monitoring**

### **Check Webhook Activity**
```javascript
// Frontend code to check notifications
const response = await fetch('/api/notifications?personaId=persona_id')
const { notifications } = await response.json()
```

### **View Training Progress**
```javascript
// Get real-time progress updates
const response = await fetch('/api/notifications?personaId=persona_id&type=progress')
const { notifications } = await response.json()
```

## 🎉 **Result**

Once set up, your workflow becomes:
1. **User uploads photos** → Persona created
2. **User clicks "Train LoRA"** → Training starts
3. **Webhook receives completion** → Persona auto-updated
4. **User gets instant notification** → Ready to use!

**No more manual intervention required! 🚀** 