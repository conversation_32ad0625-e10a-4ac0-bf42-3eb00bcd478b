# Vercel Deployment Guide for Thumbnex

## 🚨 **CRITICAL: Fix "Replicate API token not configured" Error**

If you're getting a 500 error with "Replicate API token not configured", follow these steps:

### 🔧 **Step 1: Set Environment Variables in Vercel**

1. **Go to Vercel Dashboard**:
   - Open [vercel.com/dashboard](https://vercel.com/dashboard)
   - Select your Thumbnex project

2. **Add Environment Variables**:
   - Go to **Settings** → **Environment Variables**
   - Add these **REQUIRED** variables:

```env
REPLICATE_API_TOKEN=r8_your_actual_token_here
REPLICATE_USERNAME=your_replicate_username
REPLICATE_WEBHOOK_SECRET=your_webhook_secret_here
WEBHOOK_BASE_URL=https://your-app-name.vercel.app
```

3. **Apply to All Environments**:
   - Make sure to check **Production**, **Preview**, and **Development**
   - Click **Save** for each variable

### 🔄 **Step 2: Redeploy Your App**

After adding environment variables:
```bash
# Trigger a new deployment
git commit --allow-empty -m "Trigger redeploy with env vars"
git push origin main
```

Or in Vercel Dashboard:
- Go to **Deployments** tab
- Click **Redeploy** on your latest deployment

## 🚀 Required Environment Variables

Configure these environment variables in your Vercel dashboard under **Project Settings → Environment Variables**:

### 🔑 **Essential Variables**

1. **REPLICATE_API_TOKEN** (Required)
   - Your Replicate API token for AI model access
   - Get it from: https://replicate.com/account/api-tokens
   - Example: `r8_abc123...xyz789`

2. **REPLICATE_USERNAME** (Required)
   - Your Replicate username for model storage
   - Example: `your-username`

3. **REPLICATE_WEBHOOK_SECRET** (Required for automation)
   - Secret key for webhook signature verification
   - Generate a random 32+ character string
   - Example: `whsec_abc123xyz789randomsecret123`

4. **WEBHOOK_BASE_URL** (Required for webhooks)
   - Your deployed Vercel app URL
   - Format: `https://your-app-name.vercel.app`
   - Example: `https://thumbnex-abc123.vercel.app`

### 🎨 **Optional Variables**

5. **FAL_API_KEY** (Optional)
   - For FAL.ai image generation models
   - Get it from: https://fal.ai/dashboard
   - Example: `fal_abc123...xyz789`

6. **NEXT_PUBLIC_SUPABASE_URL** (Optional - for persistent storage)
   - Your Supabase project URL
   - Example: `https://your-project-ref.supabase.co`

7. **NEXT_PUBLIC_SUPABASE_ANON_KEY** (Optional - for persistent storage)
   - Your Supabase anonymous key
   - Example: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

8. **SUPABASE_SERVICE_ROLE_KEY** (Optional - for admin operations)
   - Your Supabase service role key
   - Example: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

## 🔧 **Vercel Configuration Steps**

### 1. **Deploy to Vercel**
```bash
# Connect your GitHub repository to Vercel
# Or use Vercel CLI:
npm i -g vercel
vercel --prod
```

### 2. **Configure Environment Variables**
In Vercel Dashboard:
1. Go to your project → Settings → Environment Variables
2. Add each variable with values for **Production** environment
3. Click "Save"

### 3. **Update WEBHOOK_BASE_URL After First Deploy**
1. After first deployment, note your Vercel URL (e.g., `thumbnex-abc123.vercel.app`)
2. Update `WEBHOOK_BASE_URL` with this URL
3. Redeploy to activate webhooks

### 4. **Test Webhook Endpoint**
Your webhook endpoint will be:
```
https://your-app-name.vercel.app/api/webhooks/training-complete
```

## 🔐 **Webhook Security**

The webhook endpoint verifies signatures using HMAC SHA256:
- Replicate sends signature in `replicate-signature` header
- Format: `sha256=<hash>`
- Uses your `REPLICATE_WEBHOOK_SECRET` for verification

## 🎯 **Complete Automation Setup**

### 1. **Configure Replicate Webhooks**
When training LoRA models, the system automatically:
- Registers webhook URL during training
- Receives notifications when training completes
- Updates persona status automatically
- No manual intervention needed!

### 2. **Training Workflow**
```
Start Training → Replicate Processes → Webhook Fired → Persona Updated
    (Manual)         (15-20 min)        (Automatic)      (Automatic)
```

## 🐛 **Troubleshooting**

### Build Issues
- **Module not found**: Check all relative imports are correct
- **TypeScript errors**: Ensure all dependencies in package.json
- **ESLint errors**: Fix quote escaping and import issues

### Runtime Issues
- **API errors**: Check REPLICATE_API_TOKEN is valid
- **Webhook failures**: Verify REPLICATE_WEBHOOK_SECRET matches
- **Training not updating**: Check WEBHOOK_BASE_URL is correct

### Environment Verification
```javascript
// Add to any API route for debugging:
console.log('Environment check:', {
  hasReplicateToken: !!process.env.REPLICATE_API_TOKEN,
  hasWebhookSecret: !!process.env.REPLICATE_WEBHOOK_SECRET,
  webhookBaseUrl: process.env.WEBHOOK_BASE_URL
})
```

## ✅ **Success Indicators**

Your deployment is successful when:
- ✅ Build completes without errors
- ✅ All API routes are accessible
- ✅ Webhook endpoint responds (test with curl)
- ✅ Training starts and completes automatically
- ✅ Personas update without manual intervention

## 🔄 **Post-Deployment**

1. **Test the full workflow:**
   - Create a persona with 10+ images
   - Start LoRA training
   - Wait 15-20 minutes
   - Verify persona updates automatically

2. **Monitor webhook activity:**
   - Check `/api/notifications` for webhook notifications
   - Verify training progress updates in real-time

3. **Clean up old scripts:**
   - Remove manual fix scripts
   - Delete temporary files
   - Enjoy fully automated workflow! 🎉

---

**🎊 Congratulations! Your Thumbnex app now has complete webhook automation!** 