import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '../../../lib/supabase'

export async function POST(request: NextRequest) {
  try {
    console.log('🗄️ Setting up database tables...')
    
    if (!supabase) {
      return NextResponse.json(
        { error: 'Supabase not configured' },
        { status: 500 }
      )
    }

    // Create personas table
    const createPersonasTable = `
      CREATE TABLE IF NOT EXISTS personas (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        image_url TEXT NOT NULL,
        image_base64 TEXT NOT NULL,
        created_at TIMESTAMPTZ DEFAULT NOW(),
        updated_at TIMESTAMPTZ DEFAULT NOW(),
        usage_count INTEGER DEFAULT 0,
        category TEXT DEFAULT 'other',
        is_default BOOLEAN DEFAULT FALSE,
        last_used TIMESTAMPTZ DEFAULT NOW(),
        lora_training JSONB
      );
    `

    console.log('📊 Creating personas table...')
    const { error: personasError } = await supabase.rpc('exec_sql', {
      sql: createPersonasTable
    })

    if (personasError) {
      console.error('❌ Failed to create personas table:', personasError)
      return NextResponse.json(
        { 
          error: 'Failed to create personas table',
          details: personasError.message
        },
        { status: 500 }
      )
    }

    // Create indexes
    const createIndexes = `
      CREATE INDEX IF NOT EXISTS idx_personas_created_at ON personas(created_at);
      CREATE INDEX IF NOT EXISTS idx_personas_is_default ON personas(is_default);
      CREATE INDEX IF NOT EXISTS idx_personas_usage_count ON personas(usage_count);
      CREATE INDEX IF NOT EXISTS idx_personas_category ON personas(category);
    `

    console.log('📇 Creating indexes...')
    const { error: indexError } = await supabase.rpc('exec_sql', {
      sql: createIndexes
    })

    if (indexError) {
      console.warn('⚠️ Index creation warning:', indexError)
      // Don't fail on index errors
    }

    // Enable RLS
    const enableRLS = `
      ALTER TABLE personas ENABLE ROW LEVEL SECURITY;
      
      DROP POLICY IF EXISTS "Allow all operations on personas" ON personas;
      CREATE POLICY "Allow all operations on personas" ON personas
        FOR ALL USING (true);
    `

    console.log('🔒 Setting up Row Level Security...')
    const { error: rlsError } = await supabase.rpc('exec_sql', {
      sql: enableRLS
    })

    if (rlsError) {
      console.warn('⚠️ RLS setup warning:', rlsError)
      // Don't fail on RLS errors
    }

    // Create training jobs table
    const createTrainingJobsTable = `
      CREATE TABLE IF NOT EXISTS training_jobs (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        persona_id TEXT NOT NULL,
        replicate_training_id TEXT NOT NULL UNIQUE,
        status TEXT NOT NULL CHECK (status IN ('pending', 'training', 'completed', 'failed')) DEFAULT 'pending',
        progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
        training_images_count INTEGER NOT NULL,
        trigger_word TEXT NOT NULL,
        model_url TEXT,
        training_cost DECIMAL(10,2) DEFAULT 0.00,
        estimated_completion TIMESTAMPTZ,
        started_at TIMESTAMPTZ DEFAULT NOW(),
        completed_at TIMESTAMPTZ,
        updated_at TIMESTAMPTZ DEFAULT NOW(),
        error_message TEXT,
        webhook_data JSONB DEFAULT '{}'::jsonb
      );
    `

    console.log('📊 Creating training_jobs table...')
    const { error: trainingJobsError } = await supabase.rpc('exec_sql', {
      sql: createTrainingJobsTable
    })

    if (trainingJobsError) {
      console.warn('⚠️ Training jobs table warning:', trainingJobsError)
      // Don't fail if table already exists
    }

    console.log('✅ Database setup completed successfully!')
    
    return NextResponse.json({
      success: true,
      message: 'Database tables created successfully',
      tables: ['personas', 'training_jobs'],
      note: 'You can now create and train personas'
    })

  } catch (error) {
    console.error('❌ Database setup failed:', error)
    return NextResponse.json(
      { 
        error: 'Database setup failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
} 