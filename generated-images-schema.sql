-- Generated Images Table for Thumbnex
-- This table stores generated thumbnail images with permanent Supabase Storage URLs

CREATE TABLE IF NOT EXISTS generated_images (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  persona_id TEXT REFERENCES personas(id) ON DELETE SET NULL,
  prompt TEXT NOT NULL,
  image_url TEXT NOT NULL, -- Permanent Supabase Storage URL
  replicate_url TEXT, -- Original temporary URL (for reference)
  file_name TEXT NOT NULL,
  file_size INTEGER,
  mime_type TEXT DEFAULT 'image/png',
  generation_params JSONB DEFAULT '{}'::jsonb, -- Store generation settings
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_generated_images_persona_id ON generated_images(persona_id);
CREATE INDEX IF NOT EXISTS idx_generated_images_created_at ON generated_images(created_at);
CREATE INDEX IF NOT EXISTS idx_generated_images_prompt ON generated_images USING gin(to_tsvector('english', prompt));

-- Enable Row Level Security (RLS)
ALTER TABLE generated_images ENABLE ROW LEVEL SECURITY;

-- Create policy to allow all operations for now
CREATE POLICY "Allow all operations on generated_images" ON generated_images
  FOR ALL USING (true);

-- Trigger to automatically update updated_at when row is modified
CREATE TRIGGER update_generated_images_updated_at 
  BEFORE UPDATE ON generated_images 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- Create storage bucket for generated thumbnails (run this in Supabase dashboard)
-- INSERT INTO storage.buckets (id, name, public) VALUES ('generated-thumbnails', 'generated-thumbnails', true);

-- Create storage policy for the bucket
-- CREATE POLICY "Allow public read access" ON storage.objects FOR SELECT USING (bucket_id = 'generated-thumbnails');
-- CREATE POLICY "Allow public upload" ON storage.objects FOR INSERT WITH CHECK (bucket_id = 'generated-thumbnails'); 