import { NextRequest, NextResponse } from 'next/server';
import { loadPersonas, savePersona } from '../../../utils/personaStorage';

export async function GET(request: NextRequest) {
  try {
    // Check webhook configuration
    const webhookConfig = {
      hasWebhookSecret: !!process.env.REPLICATE_WEBHOOK_SECRET,
      hasWebhookUrl: !!process.env.WEBHOOK_BASE_URL,
      webhookUrl: process.env.WEBHOOK_BASE_URL ? 
        `${process.env.WEBHOOK_BASE_URL}/api/webhooks/training-complete` : 'Not configured',
      webhookSecret: process.env.REPLICATE_WEBHOOK_SECRET ? 
        process.env.REPLICATE_WEBHOOK_SECRET.substring(0, 8) + '...' : 'Not set'
    };

    // Check personas stuck in training
    const personas = await loadPersonas();
    const trainingPersonas = personas.filter(p => p.loraTraining?.status === 'training');
    
    return NextResponse.json({
      success: true,
      webhookConfig,
      trainingPersonas: trainingPersonas.map(p => ({
        id: p.id,
        name: p.name,
        trainingId: p.loraTraining?.trainingId,
        status: p.loraTraining?.status,
        progress: p.loraTraining?.trainingProgress,
        triggerWord: p.loraTraining?.triggerWord
      })),
      instructions: {
        manualUpdate: "POST to this endpoint with {personaId, trainingId} to manually check and update status",
        checkTraining: "Use the trainingId to manually check training status with Replicate"
      }
    });

  } catch (error) {
    return NextResponse.json(
      { 
        error: 'Webhook test failed', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

/**
 * Simple webhook test endpoint to check if webhooks are being received
 * This bypasses all verification for testing purposes
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🧪 TEST WEBHOOK: Received webhook request')
    console.log('🔍 Headers:', Object.fromEntries(request.headers.entries()))
    
    const body = await request.text()
    console.log('📦 Raw body:', body)
    
    try {
      const payload = JSON.parse(body)
      console.log('📋 Parsed payload:', {
        id: payload.id,
        status: payload.status,
        type: payload.type,
        destination: payload.destination,
        keys: Object.keys(payload)
      })
      
      // Simple response
      return NextResponse.json({
        success: true,
        message: 'Test webhook received successfully',
        receivedData: {
          id: payload.id,
          status: payload.status,
          type: payload.type,
          timestamp: new Date().toISOString()
        }
      })
    } catch (parseError) {
      console.error('❌ Failed to parse JSON:', parseError)
      return NextResponse.json({
        success: false,
        error: 'Invalid JSON',
        rawBody: body.substring(0, 200) + '...'
      })
    }
    
  } catch (error) {
    console.error('❌ Test webhook error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
} 